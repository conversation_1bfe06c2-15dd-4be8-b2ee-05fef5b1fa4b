#!/usr/bin/env python3
"""
Build Windows Installer for Vid2Frames Main Branch
Creates a directory-based installation, not a single EXE
"""

import os
import shutil
import subprocess
from pathlib import Path

def create_installer():
    print("🚀 Building Vid2Frames Windows Installer...")
    print("=" * 50)
    
    # Step 1: Create PyInstaller spec for directory distribution
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('assets/*', 'assets/'),
        ('src/ui/**/*.py', 'src/ui/'),
        ('src/core/**/*.py', 'src/core/'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'flet',
        'opencv-python',
        'torch',
        'torchaudio',
        'soundfile',
        'numpy',
        'whisper',
        'openai-whisper',
        'faster-whisper',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,  # This creates directory distribution
    name='Vid2Frames',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # Windows app (no console)
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Vid2Frames'
)
'''
    
    # Write spec file
    with open('vid2frames.spec', 'w') as f:
        f.write(spec_content)
    
    print("✅ Created PyInstaller spec for directory distribution")
    
    # Step 2: Build with PyInstaller
    print("🔧 Building application with PyInstaller...")
    result = subprocess.run(['pyinstaller', 'vid2frames.spec', '--clean'], 
                          capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ PyInstaller failed: {result.stderr}")
        return False
    
    print("✅ PyInstaller build completed")
    
    # Step 3: Prepare installer files
    print("📁 Preparing installer files...")
    
    # Copy built application
    dist_path = Path('dist/Vid2Frames')
    if not dist_path.exists():
        print(f"❌ Build output not found: {dist_path}")
        return False
    
    # Create installer directory structure
    installer_files = Path('installer_files')
    installer_files.mkdir(exist_ok=True)
    
    # Copy application files
    if (installer_files / 'dist').exists():
        shutil.rmtree(installer_files / 'dist')
    shutil.copytree(dist_path, installer_files / 'dist')
    
    # Copy additional files
    files_to_copy = [
        'README.md',
        'LICENSE', 
        'requirements.txt'
    ]
    
    for file in files_to_copy:
        if Path(file).exists():
            shutil.copy2(file, installer_files / Path(file).name)
    
    print("✅ Installer files prepared")
    
    # Step 4: Build NSIS installer
    print("🏗️ Building NSIS installer...")
    
    # Check if NSIS is installed
    nsis_path = r"C:\Program Files (x86)\NSIS\makensis.exe"
    if not Path(nsis_path).exists():
        print("❌ NSIS not found. Please install NSIS from https://nsis.sourceforge.io/")
        print("   Alternative: Use Inno Setup or WiX Toolset")
        return False
    
    # Build installer
    result = subprocess.run([nsis_path, 'installer/vid2frames_installer.nsi'], 
                          capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ NSIS build failed: {result.stderr}")
        return False
    
    print("✅ Windows installer created successfully!")
    print("📦 Output: Vid2Frames-Pro-Setup-1.0.0.exe")
    
    return True

if __name__ == "__main__":
    success = create_installer()
    if success:
        print("\\n🎉 Build completed successfully!")
        print("\\nThe installer will create a directory structure like:")
        print("  📁 C:/Program Files/Vid2Frames/")
        print("  ├── 📄 Vid2Frames.exe")
        print("  ├── 📁 _internal/ (Python runtime & libraries)")
        print("  ├── 📄 README.txt")
        print("  └── 📄 LICENSE.txt")
    else:
        print("\\n❌ Build failed. Please check the errors above.")