"""
Test ComfyUI F5-TTS Integration with Voice Examples
"""

import sys
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.f5_tts import F5TTSProcessor

def test_comfy_voice_generation():
    """Test generating speech with ComfyUI voices"""
    print("🎤 Testing ComfyUI Voice Generation")
    print("=" * 50)
    
    processor = F5TTSProcessor()
    
    # Load model
    print("📥 Loading F5-TTS model...")
    processor.load_model("E2TTS_Base")
    
    # Get available voices
    voices = processor.get_available_comfy_voices()
    print(f"🎵 Available voices: {len(voices)}")
    
    if not voices:
        print("❌ No ComfyUI voices found")
        return
    
    # Test with first few voices
    test_voices = voices[:3]  # Test first 3 voices
    test_sentences = [
        "Hello there, this is a test of the ComfyUI voice integration.",
        "The F5-TTS system is now working with your existing voice examples.",
        "Each sentence will use the selected voice for generation."
    ]
    
    for i, voice in enumerate(test_voices):
        print(f"\n🎭 Testing voice: {voice['name']}")
        
        # Set reference audio
        voice_path = Path(voice['path'])
        if voice_path.exists():
            try:
                # Use suggested reference text
                ref_text = get_reference_text_for_voice(voice['name'])
                processor.set_reference_audio(voice_path, ref_text)
                print(f"✅ Reference audio set: {voice_path.name}")
                
                # Generate test audio
                output_dir = Path(f"test_comfy_voice_{voice['name'].replace(' ', '_')}")
                output_dir.mkdir(exist_ok=True)
                
                sentence = test_sentences[i % len(test_sentences)]
                output_file = output_dir / f"test_{voice['name'].replace(' ', '_')}.wav"
                
                print(f"🎯 Generating: '{sentence}'")
                success = processor.generate_speech(sentence, output_file)
                
                if success:
                    print(f"✅ Generated: {output_file}")
                else:
                    print(f"❌ Failed to generate audio")
                    
            except Exception as e:
                print(f"❌ Error with voice {voice['name']}: {e}")
        else:
            print(f"❌ Voice file not found: {voice_path}")

def get_reference_text_for_voice(voice_name: str) -> str:
    """Get appropriate reference text for known voices"""
    reference_texts = {
        "David_Attenborough": "The natural world is full of extraordinary creatures and phenomena that continue to fascinate us.",
        "Morgan_Freeman": "In a world full of mysteries, science helps us understand the incredible complexity of life.",
        "Clint_Eastwood": "Well, I reckon there's always more than one way to look at things in this world.",
        "Sophie_Anderson": "Welcome to this demonstration of advanced text-to-speech technology.",
        "voice_preview_prince": "Hello there, my name is Prince, and this is a sample of my voice.",
        "crestfallen_original": "This is a sample of natural speech for voice cloning purposes."
    }
    
    # Try to match voice name
    for key, text in reference_texts.items():
        if key.lower() in voice_name.lower():
            return text
    
    return "This is a sample of my voice for reference audio cloning."

def test_batch_processing():
    """Test batch processing with ComfyUI voices"""
    print(f"\n🔄 Testing Batch Processing")
    print("=" * 30)
    
    processor = F5TTSProcessor()
    processor.load_model()
    
    # Get a voice to use
    voices = processor.get_available_comfy_voices()
    if not voices:
        print("❌ No voices available for batch test")
        return
    
    # Use David Attenborough voice if available
    selected_voice = None
    for voice in voices:
        if "david" in voice['name'].lower() or "attenborough" in voice['name'].lower():
            selected_voice = voice
            break
    
    if not selected_voice:
        selected_voice = voices[0]  # Use first available
    
    print(f"🎤 Using voice: {selected_voice['name']}")
    
    # Set reference audio
    voice_path = Path(selected_voice['path'])
    ref_text = get_reference_text_for_voice(selected_voice['name'])
    processor.set_reference_audio(voice_path, ref_text)
    
    # Batch sentences
    sentences = [
        "Welcome to the ComfyUI F5-TTS integration test.",
        "This system can process multiple sentences in sequence.",
        "Each sentence maintains the same voice characteristics.",
        "The quality depends on the reference audio provided.",
        "This completes our batch processing demonstration."
    ]
    
    output_dir = Path("test_batch_comfy_f5")
    output_dir.mkdir(exist_ok=True)
    
    print(f"🎯 Processing {len(sentences)} sentences...")
    
    # Custom parameters for better quality
    params = {
        'model': 'E2TTS_Base',
        'seed': 42,
        'temperature': 0.7,
        'speed': 1.0,
        'target_rms': 0.12,
        'nfe_step': 32,
        'cfg_strength': 2.5
    }
    
    def progress_callback(msg, progress):
        print(f"  📊 {progress:.0%} - {msg}")
    
    results = processor.process_sentences(
        sentences=sentences,
        output_dir=output_dir,
        params=params,
        progress_callback=progress_callback
    )
    
    print(f"\n✅ Batch Results:")
    print(f"  Success: {results['success_count']}")
    print(f"  Failed: {results['failed_count']}")
    print(f"  Output: {output_dir}")

def demonstrate_voice_features():
    """Demonstrate key voice features"""
    print(f"\n🌟 ComfyUI F5-TTS Features")
    print("=" * 40)
    
    processor = F5TTSProcessor()
    voices = processor.get_available_comfy_voices()
    
    print(f"✅ Voice Detection: {len(voices)} voices found")
    print(f"✅ Voice Loading: Automatic path resolution")
    print(f"✅ Reference Text: Smart suggestions based on voice names")
    print(f"✅ Batch Processing: Multiple sentences with same voice")
    print(f"✅ Parameter Control: Speed, temperature, quality settings")
    print(f"✅ Format Support: WAV, MP3 reference audio")
    
    print(f"\n🎵 Available Voices:")
    for voice in voices:
        print(f"  • {voice['name']} ({voice['format'].upper()})")
        ref_text = get_reference_text_for_voice(voice['name'])
        print(f"    Suggested text: \"{ref_text[:50]}...\"")

if __name__ == "__main__":
    print("🚀 ComfyUI F5-TTS Integration Test Suite")
    print("=" * 60)
    
    try:
        test_comfy_voice_generation()
        test_batch_processing()
        demonstrate_voice_features()
        
        print(f"\n🎉 All tests completed!")
        print(f"\n💡 To use in the app:")
        print("  1. Launch: python src/main.py")
        print("  2. Go to F5-TTS tab")
        print("  3. Select a ComfyUI voice from dropdown")
        print("  4. Enter text and generate")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()