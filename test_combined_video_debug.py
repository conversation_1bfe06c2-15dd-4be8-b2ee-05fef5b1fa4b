"""
Debug script to check combined video issues:
1. Check if audio-video indexing is correct
2. Check if combined_videos are in results properly
"""
import os
import sys
sys.path.append('src')

from core.f5_tts import F5TTSProcessor
from core.broll.broll_generator import BRoll<PERSON>enerator

def test_combined_videos():
    # Test with a simple sentence
    test_text = """
    Welcome to our technology showcase.
    Our AI systems provide advanced solutions.
    Innovation drives our development process.
    """
    
    # Create processor
    processor = F5TTSProcessor()
    
    # Mock broll config
    broll_config = {
        'enabled': True,
        'api_key': 'test_key',
        'num_videos': 3,
        'video_quality': 'hd'
    }
    
    # Process with debug output
    print("Processing text with B-roll...")
    results = processor.process_sentences(
        sentences=[s.strip() for s in test_text.strip().split('\n') if s.strip()],
        voice_type='business',
        voice_file=None,
        reference_text="This is a professional business voice for our corporate communications.",
        broll_config=broll_config,
        output_dir="test_output_debug"
    )
    
    print(f"\nResults keys: {list(results.keys())}")
    
    if 'combined_videos' in results:
        combined_videos = results['combined_videos']
        print(f"\nCombined videos count: {len(combined_videos)}")
        
        for i, video in enumerate(combined_videos):
            print(f"\nVideo {i+1}:")
            print(f"  File: {video.get('file', 'N/A')}")
            print(f"  Sentence: {video.get('sentence', 'N/A')}")
            print(f"  Audio file: {video.get('audio_file', 'N/A')}")
            
            # Check if files exist
            if 'file' in video and os.path.exists(video['file']):
                print(f"  Video exists: YES")
            else:
                print(f"  Video exists: NO")
                
            if 'audio_file' in video and os.path.exists(video['audio_file']):
                print(f"  Audio exists: YES")
            else:
                print(f"  Audio exists: NO")
    else:
        print("No combined_videos in results!")
        
    # Also check broll_data if it exists
    if 'broll_data' in results:
        broll_data = results['broll_data']
        print(f"\nB-roll data keys: {list(broll_data.keys())}")
        for key, value in broll_data.items():
            print(f"  {key}: {len(value)} videos")
    
    # Check audio files
    if 'audio_files' in results:
        audio_files = results['audio_files']
        print(f"\nAudio files count: {len(audio_files)}")
        for i, audio_file in enumerate(audio_files):
            print(f"  Audio {i}: {audio_file}")
    
    return results

if __name__ == "__main__":
    try:
        results = test_combined_videos()
        print("\nTest completed successfully!")
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()