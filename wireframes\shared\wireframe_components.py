"""
Reusable wireframe components for consistent UI patterns
Based on the reference designs from stitch_video_upload
"""
import flet as ft


def create_header(title: str, subtitle: str):
    """Create a modern header section with clean typography"""
    return ft.Container(
        content=ft.Column([
            ft.Text(
                title, 
                size=28, 
                weight=ft.FontWeight.BOLD, 
                color=ft.Colors.ON_SURFACE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                subtitle, 
                size=16, 
                color=ft.Colors.ON_SURFACE_VARIANT,
                text_align=ft.TextAlign.CENTER
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=12),
        padding=ft.padding.only(top=40, bottom=30, left=20, right=20),
        alignment=ft.alignment.center
    )


def create_file_upload_area(title: str, subtitle: str, icon: str, supported_formats: str, on_click=None):
    """Create a modern file upload area matching the reference design"""
    return ft.Container(
        content=ft.Column([
            ft.Container(
                content=ft.Icon(
                    icon, 
                    size=72, 
                    color=ft.Colors.ON_SURFACE_VARIANT,
                ),
                animate_scale=ft.Animation(300, ft.AnimationCurve.EASE_IN_OUT)
            ),
            ft.Text(
                title, 
                size=20, 
                weight=ft.FontWeight.W_600, 
                color=ft.Colors.ON_SURFACE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                "or", 
                size=14, 
                color=ft.Colors.ON_SURFACE_VARIANT,
                text_align=ft.TextAlign.CENTER
            ),
            ft.ElevatedButton(
                "Select File",
                icon=ft.Icons.UPLOAD_FILE,
                on_click=on_click,
                style=ft.ButtonStyle(
                    bgcolor=ft.Colors.PRIMARY,
                    color=ft.Colors.ON_PRIMARY,
                    shape=ft.RoundedRectangleBorder(radius=8),
                    padding=ft.padding.symmetric(horizontal=20, vertical=12)
                ),
                animate_scale=ft.Animation(200, ft.AnimationCurve.EASE_IN_OUT)
            ),
            ft.Text(
                supported_formats, 
                size=12, 
                color=ft.Colors.ON_SURFACE_VARIANT, 
                italic=True,
                text_align=ft.TextAlign.CENTER
            )
        ],
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=16
        ),
        bgcolor=ft.Colors.GREY_100,
        border_radius=12,
        padding=64,
        margin=ft.margin.symmetric(horizontal=40, vertical=20),
        border=ft.border.all(2, ft.Colors.GREY_400),
        ink=True,
        on_click=on_click,
        animate=ft.Animation(300, ft.AnimationCurve.EASE_OUT),
        animate_opacity=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
    )


def create_settings_section(title: str, controls: list):
    """Create a settings section with consistent styling and animations"""
    return ft.Container(
        content=ft.Column([
            ft.Text(title, size=18, weight=ft.FontWeight.W_600, color=ft.Colors.ON_SURFACE),
            ft.Divider(color=ft.Colors.GREY_400),
            *controls
        ], spacing=15),
        padding=20,
        bgcolor=ft.Colors.GREY_100,
        border_radius=12,
        margin=ft.margin.symmetric(horizontal=20, vertical=10),
        border=ft.border.all(1, ft.Colors.GREY_400),
        animate=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
    )


def create_setting_row(label: str, control: ft.Control, description: str = None):
    """Create a setting row with label and control"""
    items = [
        ft.Text(label, width=200, color=ft.Colors.ON_SURFACE),
        control if isinstance(control, ft.Row) else ft.Container(control, width=200)
    ]
    
    column_items = [ft.Row(items, spacing=20)]
    
    if description:
        column_items.append(
            ft.Text(description, size=10, color=ft.Colors.ON_SURFACE_VARIANT)
        )
    
    return ft.Container(
        content=ft.Column(column_items, spacing=5),
        animate_opacity=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
    )


def create_progress_section(title: str, progress_value: float, status_text: str):
    """Create a modern progress section matching the reference design"""
    return ft.Container(
        content=ft.Column([
            ft.Text(
                title, 
                size=24, 
                weight=ft.FontWeight.BOLD, 
                color=ft.Colors.ON_SURFACE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                "Please wait while we extract the frames. This might take a moment.", 
                size=16, 
                color=ft.Colors.ON_SURFACE_VARIANT,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Container(height=20),  # Spacing
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Text(
                            status_text, 
                            size=14, 
                            weight=ft.FontWeight.W_500,
                            color=ft.Colors.ON_SURFACE_VARIANT
                        ),
                        ft.Text(
                            f"{progress_value*100:.0f}%", 
                            size=14, 
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.PRIMARY
                        )
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    ft.Container(height=8),
                    ft.ProgressBar(
                        value=progress_value, 
                        color=ft.Colors.PRIMARY,
                        bgcolor=ft.Colors.GREY_300,
                        height=10,
                        border_radius=5
                    ),
                    ft.Container(height=8),
                    ft.Text(
                        "Estimated time remaining: 5 minutes", 
                        size=12, 
                        color=ft.Colors.ON_SURFACE_VARIANT,
                        text_align=ft.TextAlign.CENTER
                    )
                ], spacing=0),
                padding=24,
                bgcolor=ft.Colors.GREY_100,
                border_radius=12,
                border=ft.border.all(1, ft.Colors.GREY_400)
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
        padding=ft.padding.symmetric(horizontal=40, vertical=60),
        animate=ft.Animation(300, ft.AnimationCurve.EASE_OUT),
        animate_opacity=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
    )


def create_result_card(title: str, subtitle: str, icon: str, metadata: dict, on_click=None):
    """Create a modern result card matching the reference design"""
    return ft.Card(
        content=ft.Container(
            content=ft.Stack([
                # Background image placeholder
                ft.Container(
                    width=200,
                    height=120,
                    bgcolor=ft.Colors.GREY_400,
                    border_radius=8,
                    content=ft.Icon(
                        ft.Icons.IMAGE,
                        size=40,
                        color=ft.Colors.ON_SURFACE_VARIANT
                    ),
                    alignment=ft.alignment.center
                ),
                # Hover overlay
                ft.Container(
                    width=200,
                    height=120,
                    bgcolor=ft.colors.with_opacity(0.0, ft.Colors.PRIMARY),
                    border_radius=8,
                    content=ft.Icon(
                        ft.Icons.DOWNLOAD,
                        size=32,
                        color=ft.Colors.ON_PRIMARY
                    ),
                    alignment=ft.alignment.center,
                    animate_opacity=ft.Animation(300, ft.AnimationCurve.EASE_IN_OUT)
                ),
                # Timestamp overlay
                ft.Container(
                    content=ft.Text(
                        subtitle, 
                        size=12, 
                        weight=ft.FontWeight.W_600,
                        color=ft.Colors.WHITE
                    ),
                    bgcolor=ft.colors.with_opacity(0.7, ft.Colors.BLACK),
                    padding=ft.padding.symmetric(horizontal=8, vertical=4),
                    border_radius=4,
                    left=8,
                    bottom=8
                )
            ]),
            padding=0,
            ink=True,
            on_click=on_click,
            animate_scale=ft.Animation(200, ft.AnimationCurve.EASE_IN_OUT)
        ),
        elevation=4,
        surface_tint_color=ft.Colors.PRIMARY,
        animate=ft.Animation(500, ft.AnimationCurve.EASE_OUT)
    )


def create_action_button(text: str, icon: str, color: str, on_click=None, disabled=False):
    """Create a consistent action button with animations"""
    return ft.ElevatedButton(
        text=text,
        icon=icon,
        on_click=on_click,
        disabled=disabled,
        style=ft.ButtonStyle(
            bgcolor=color,
            color=ft.Colors.WHITE,
            shape=ft.RoundedRectangleBorder(radius=8),
            elevation=4
        ),
        height=48,
        width=200,
        animate_scale=ft.Animation(200, ft.AnimationCurve.EASE_IN_OUT),
        animate_opacity=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
    )


def create_status_text(message: str, color: str = None):
    """Create a status text with consistent styling"""
    return ft.Text(
        message,
        size=14,
        color=color or ft.Colors.ON_SURFACE_VARIANT,
        text_align=ft.TextAlign.CENTER
    )


def create_file_info_display(file_data: dict):
    """Create a modern file information display"""
    return ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Text(
                    "Video Name:", 
                    size=14, 
                    weight=ft.FontWeight.W_600,
                    color=ft.Colors.ON_SURFACE
                ),
                ft.Text(
                    file_data["name"], 
                    size=14, 
                    color=ft.Colors.ON_SURFACE_VARIANT
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            ft.Row([
                ft.Text(
                    "Duration:", 
                    size=14, 
                    weight=ft.FontWeight.W_600,
                    color=ft.Colors.ON_SURFACE
                ),
                ft.Text(
                    f"{file_data['duration']:.0f} minutes", 
                    size=14, 
                    color=ft.Colors.ON_SURFACE_VARIANT
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            ft.Row([
                ft.Text(
                    "Resolution:", 
                    size=14, 
                    weight=ft.FontWeight.W_600,
                    color=ft.Colors.ON_SURFACE
                ),
                ft.Text(
                    f"{file_data['width']}x{file_data['height']}", 
                    size=14, 
                    color=ft.Colors.ON_SURFACE_VARIANT
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
        ], spacing=12),
        bgcolor=ft.Colors.SURFACE_VARIANT if ft.Colors.SURFACE_VARIANT else ft.Colors.GREY_100,
        border_radius=12,
        padding=24,
        margin=ft.margin.symmetric(horizontal=40, vertical=20),
        border=ft.border.all(1, ft.Colors.GREY_400),
        animate=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
    )


def create_results_grid(items: list, create_item_func):
    """Create a modern results grid matching the reference design"""
    grid = ft.GridView(
        expand=True,
        runs_count=6,  # More columns like the reference
        max_extent=200,
        child_aspect_ratio=0.8,  # Taller cards
        spacing=16,
        run_spacing=16,
        padding=ft.padding.symmetric(horizontal=40, vertical=20),
        animate_opacity=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
    )
    
    for item in items:
        grid.controls.append(create_item_func(item))
    
    return grid


def create_tab_content(content_widget):
    """Wrap content in a scrollable container with modern styling"""
    return ft.Container(
        content=ft.Column(
            [content_widget], 
            scroll=ft.ScrollMode.AUTO, 
            expand=True,
            animate_opacity=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
        ),
        expand=True,
        bgcolor=ft.Colors.WHITE,
        animate=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
    )


def create_results_summary(extracted_frames: int, scenes_detected: int, processing_time: str, total_size: str):
    """Create a results summary section matching the reference design"""
    return ft.Container(
        content=ft.Column([
            ft.Text(
                "Processing Results",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                "Review the extracted frames from your video. Each frame is displayed with its timestamp.",
                size=16,
                color=ft.Colors.ON_SURFACE_VARIANT,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Container(height=20),
            ft.Row([
                ft.Column([
                    ft.Text(
                        str(extracted_frames),
                        size=32,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.ON_SURFACE,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        "Frames Extracted",
                        size=12,
                        color=ft.Colors.ON_SURFACE_VARIANT,
                        text_align=ft.TextAlign.CENTER
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, expand=True),
                ft.Column([
                    ft.Text(
                        str(scenes_detected),
                        size=32,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.ON_SURFACE,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        "Scenes Detected",
                        size=12,
                        color=ft.Colors.ON_SURFACE_VARIANT,
                        text_align=ft.TextAlign.CENTER
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, expand=True),
                ft.Column([
                    ft.Text(
                        processing_time,
                        size=32,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.ON_SURFACE,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        "Processing Time",
                        size=12,
                        color=ft.Colors.ON_SURFACE_VARIANT,
                        text_align=ft.TextAlign.CENTER
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, expand=True),
                ft.Column([
                    ft.Text(
                        total_size,
                        size=32,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.ON_SURFACE,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        "Total Size",
                        size=12,
                        color=ft.Colors.ON_SURFACE_VARIANT,
                        text_align=ft.TextAlign.CENTER
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, expand=True),
            ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=20),
        padding=ft.padding.symmetric(horizontal=40, vertical=40)
    )