#!/usr/bin/env python3
"""
Improved B-roll keyword extraction system
Based on test results from business analyst script
"""

import re
import requests
from collections import Counter, defaultdict
from typing import List, Dict, Set, Tuple
from pathlib import Path

class ImprovedKeywordExtractor:
    def __init__(self):
        self.visual_keywords = {
            # Business/Office - High priority
            "meeting", "office", "business", "professional", "workplace", "team", 
            "collaboration", "presentation", "conference", "discussion", "handshake",
            
            # Technology - High priority
            "computer", "laptop", "typing", "coding", "developer", "programming", 
            "software", "app", "tech", "digital", "screen", "website", "database",
            
            # Documentation - Medium priority  
            "documents", "paperwork", "writing", "notes", "requirements", "analysis",
            "chart", "graph", "diagram", "planning", "whiteboard", "documentation",
            
            # Communication - Medium priority
            "phone", "email", "video", "call", "interview", "conversation", "communication",
            
            # People - Generic fallback
            "people", "person", "worker", "employee", "professional", "suit"
        }
        
        self.abstract_words = {
            "idea", "concept", "thought", "feeling", "method", "way", "thing", "process",
            "approach", "strategy", "solution", "problem", "issue", "mystery", "magic",
            "trick", "power", "superpower", "kicker", "part", "terms", "life"
        }
        
        # Keyword clusters for better cohesion
        self.keyword_clusters = {
            "office_work": ["office", "business", "professional", "workplace", "corporate"],
            "meetings": ["meeting", "discussion", "collaboration", "team", "conference"],
            "technology": ["computer", "laptop", "typing", "tech", "digital", "software"],
            "development": ["developer", "coding", "programming", "app", "system"],
            "documentation": ["documents", "paperwork", "requirements", "analysis", "writing"],
            "communication": ["phone", "email", "call", "interview", "conversation"]
        }
    
    def extract_cohesive_keywords(self, sentences: List[str]) -> Dict[int, List[str]]:
        """Extract contextually aware keywords with improved cohesion"""
        
        # Step 1: Comprehensive context analysis
        context = self._analyze_comprehensive_context(sentences)
        
        # Step 2: Generate keyword sequences with flow
        sentence_keywords = self._generate_keyword_sequence(sentences, context)
        
        # Step 3: Apply cohesion smoothing
        sentence_keywords = self._apply_cohesion_smoothing(sentence_keywords)
        
        return sentence_keywords
    
    def _analyze_comprehensive_context(self, sentences: List[str]) -> Dict:
        """Perform deep context analysis"""
        full_text = " ".join(sentences).lower()
        
        # Domain detection with scoring
        domain_scores = self._calculate_domain_scores(full_text)
        primary_domain = max(domain_scores, key=domain_scores.get)
        
        # Extract entity mentions
        entities = self._extract_entities(full_text)
        
        # Theme extraction with clustering
        themes = self._extract_clustered_themes(full_text)
        
        # Tone and style analysis
        tone = self._analyze_tone(full_text)
        
        return {
            'primary_domain': primary_domain,
            'domain_scores': domain_scores,
            'entities': entities,
            'themes': themes,
            'tone': tone,
            'keyword_preferences': self._get_domain_preferences(primary_domain)
        }
    
    def _generate_keyword_sequence(self, sentences: List[str], context: Dict) -> Dict[int, List[str]]:
        """Generate keywords with better flow and narrative awareness"""
        sentence_keywords = {}
        narrative_flow = []
        
        for i, sentence in enumerate(sentences):
            sentence_lower = sentence.lower()
            
            # Extract raw candidates
            candidates = self._extract_sentence_candidates(sentence_lower, context)
            
            # Apply narrative flow considerations
            if i > 0:
                previous_keywords = sentence_keywords.get(i-1, [])
                candidates = self._apply_flow_boost(candidates, previous_keywords, narrative_flow)
            
            # Select final keywords
            final_keywords = self._select_final_keywords(candidates, context, i)
            sentence_keywords[i] = final_keywords
            
            # Update narrative flow
            narrative_flow.extend(final_keywords)
            if len(narrative_flow) > 6:  # Keep rolling window
                narrative_flow = narrative_flow[-6:]
        
        return sentence_keywords
    
    def _extract_sentence_candidates(self, sentence: str, context: Dict) -> List[Tuple[str, float]]:
        """Extract keyword candidates with scores"""
        candidates = []
        words = re.findall(r'\b[a-zA-Z]{3,}\b', sentence)
        
        for word in words:
            if self._is_valid_visual_keyword(word):
                score = self._calculate_keyword_score(word, sentence, context)
                candidates.append((word, score))
        
        # Add context-driven keywords
        context_keywords = self._get_contextual_keywords(sentence, context)
        for keyword, score in context_keywords:
            if keyword not in [c[0] for c in candidates]:
                candidates.append((keyword, score))
        
        return sorted(candidates, key=lambda x: x[1], reverse=True)
    
    def _calculate_keyword_score(self, word: str, sentence: str, context: Dict) -> float:
        """Calculate comprehensive keyword score"""
        score = 1.0
        
        # Base visual concept score
        if word in self.visual_keywords:
            score += 2.0
        
        # Domain relevance boost
        if word in context['keyword_preferences']:
            score += 3.0
        
        # Theme relevance
        if word in context['themes']:
            score += 1.5
        
        # Entity mention boost
        if any(word in entity for entity in context['entities']):
            score += 2.0
        
        # Sentence context boost
        if self._has_strong_context_in_sentence(word, sentence):
            score += 1.0
        
        # Penalize abstract words
        if word in self.abstract_words:
            score -= 2.0
        
        return score
    
    def _get_contextual_keywords(self, sentence: str, context: Dict) -> List[Tuple[str, float]]:
        """Get contextual keywords based on sentence content"""
        keywords = []
        primary_domain = context['primary_domain']
        
        # Business context patterns
        if primary_domain == 'business':
            if any(pattern in sentence for pattern in ['meeting', 'stakeholder', 'interview']):
                keywords.append(('meeting', 3.0))
                keywords.append(('professional', 2.0))
            
            if any(pattern in sentence for pattern in ['document', 'requirement', 'write']):
                keywords.append(('documents', 3.0))
                keywords.append(('computer', 2.5))
            
            if any(pattern in sentence for pattern in ['developer', 'code', 'tech', 'app']):
                keywords.append(('developer', 3.0))
                keywords.append(('computer', 2.5))
            
            if any(pattern in sentence for pattern in ['translate', 'bridge', 'communication']):
                keywords.append(('collaboration', 2.5))
                keywords.append(('team', 2.0))
        
        return keywords
    
    def _apply_flow_boost(self, candidates: List[Tuple[str, float]], 
                         previous_keywords: List[str], narrative_flow: List[str]) -> List[Tuple[str, float]]:
        """Apply narrative flow considerations to boost related keywords"""
        
        boosted_candidates = []
        
        for word, score in candidates:
            # Continuity boost - related to previous sentence
            if any(self._are_semantically_related(word, prev) for prev in previous_keywords):
                score += 1.0
            
            # Narrative consistency - avoid jarring topic jumps
            if narrative_flow:
                flow_compatibility = sum(0.2 for flow_word in narrative_flow 
                                       if self._are_semantically_related(word, flow_word))
                score += flow_compatibility
            
            boosted_candidates.append((word, score))
        
        return sorted(boosted_candidates, key=lambda x: x[1], reverse=True)
    
    def _select_final_keywords(self, candidates: List[Tuple[str, float]], 
                              context: Dict, sentence_index: int) -> List[str]:
        """Select final 2-3 keywords ensuring diversity"""
        
        if not candidates:
            return self._get_fallback_keywords(context['primary_domain'])
        
        selected = []
        used_clusters = set()
        
        # Select from different clusters for diversity
        for word, score in candidates[:8]:  # Consider top 8 candidates
            word_cluster = self._get_keyword_cluster(word)
            
            if len(selected) < 3:
                if word_cluster not in used_clusters or len(selected) == 0:
                    selected.append(word)
                    if word_cluster:
                        used_clusters.add(word_cluster)
        
        # Ensure minimum 2 keywords
        if len(selected) < 2:
            fallbacks = self._get_fallback_keywords(context['primary_domain'])
            for fallback in fallbacks:
                if fallback not in selected:
                    selected.append(fallback)
                    if len(selected) >= 2:
                        break
        
        return selected[:3]
    
    def _apply_cohesion_smoothing(self, sentence_keywords: Dict[int, List[str]]) -> Dict[int, List[str]]:
        """Apply final cohesion smoothing to reduce jarring transitions"""
        
        smoothed = {}
        
        for i, keywords in sentence_keywords.items():
            if i == 0:
                smoothed[i] = keywords
                continue
            
            previous_keywords = smoothed[i-1]
            
            # Check for jarring transitions
            compatibility_score = sum(
                1 for current in keywords for prev in previous_keywords
                if self._are_semantically_related(current, prev)
            ) / max(len(keywords), 1)
            
            # If transition is too jarring, blend with previous keywords
            if compatibility_score < 0.3 and i > 0:
                # Keep 1-2 keywords from previous sentence for continuity
                blended = keywords.copy()
                for prev_keyword in previous_keywords[:1]:  # Take 1 from previous
                    if prev_keyword not in blended and len(blended) < 3:
                        blended.append(prev_keyword)
                
                smoothed[i] = blended[:3]
            else:
                smoothed[i] = keywords
        
        return smoothed
    
    # Helper methods
    def _calculate_domain_scores(self, text: str) -> Dict[str, float]:
        domain_patterns = {
            'business': ['business', 'analyst', 'stakeholder', 'requirement', 'corporate', 'meeting', 'office'],
            'technology': ['tech', 'app', 'software', 'developer', 'code', 'digital', 'computer'],
            'office': ['office', 'workplace', 'professional', 'team', 'collaboration'],
            'education': ['explain', 'teach', 'learn', 'understand', 'example']
        }
        
        scores = {}
        for domain, patterns in domain_patterns.items():
            scores[domain] = sum(text.count(pattern) for pattern in patterns)
        return scores
    
    def _extract_entities(self, text: str) -> List[str]:
        business_entities = ['business analyst', 'developer', 'stakeholder', 'manager', 
                           'project manager', 'tester', 'cfo', 'team']
        return [entity for entity in business_entities if entity in text]
    
    def _extract_clustered_themes(self, text: str) -> List[str]:
        words = re.findall(r'\b[a-zA-Z]{4,}\b', text)
        word_freq = Counter(words)
        return [word for word, count in word_freq.most_common(15) 
                if word not in {'what', 'that', 'they', 'with', 'like', 'business', 'analyst'}
                and count > 1]
    
    def _analyze_tone(self, text: str) -> str:
        if any(word in text for word in ['explain', 'example', 'teach', 'understand']):
            return 'educational'
        elif any(word in text for word in ['funny', 'honestly', 'kicker', 'glamorous']):
            return 'casual'
        else:
            return 'professional'
    
    def _get_domain_preferences(self, domain: str) -> List[str]:
        preferences = {
            'business': ['office', 'meeting', 'professional', 'business', 'team', 'collaboration'],
            'technology': ['computer', 'developer', 'tech', 'coding', 'software'],
            'office': ['office', 'professional', 'workplace', 'team']
        }
        return preferences.get(domain, preferences['business'])
    
    def _is_valid_visual_keyword(self, word: str) -> bool:
        return (len(word) > 2 and 
                word not in self.abstract_words and 
                not word.isdigit() and
                word not in {'the', 'and', 'you', 'they', 'that', 'with', 'for', 'are', 'but', 'not'})
    
    def _has_strong_context_in_sentence(self, word: str, sentence: str) -> bool:
        strong_indicators = {
            'meeting': ['sit', 'argue', 'discuss', 'gather'],
            'computer': ['type', 'code', 'laptop', 'screen'],
            'documents': ['write', 'document', 'paper', 'requirement']
        }
        
        if word in strong_indicators:
            return any(indicator in sentence for indicator in strong_indicators[word])
        return False
    
    def _are_semantically_related(self, word1: str, word2: str) -> bool:
        for cluster_name, cluster_words in self.keyword_clusters.items():
            if word1 in cluster_words and word2 in cluster_words:
                return True
        return False
    
    def _get_keyword_cluster(self, word: str) -> str:
        for cluster_name, cluster_words in self.keyword_clusters.items():
            if word in cluster_words:
                return cluster_name
        return "other"
    
    def _get_fallback_keywords(self, domain: str) -> List[str]:
        fallbacks = {
            'business': ['office', 'professional', 'business'],
            'technology': ['computer', 'tech', 'digital'],
            'office': ['office', 'professional', 'workplace']
        }
        return fallbacks.get(domain, ['professional', 'office'])

def test_improved_extraction():
    """Test the improved keyword extraction"""
    
    # Read the sample script
    try:
        with open(r"c:\Users\<USER>\OneDrive\Music\what is a ba.txt", 'r', encoding='utf-8') as f:
            script_text = f.read()
    except FileNotFoundError:
        print("❌ Could not find the sample script file")
        return
    
    # Split into sentences (improved)
    sentences = []
    paragraphs = script_text.split('\n\n')
    
    for paragraph in paragraphs:
        paragraph = paragraph.strip()
        if paragraph:
            # Better sentence splitting
            current_sentences = re.split(r'[.!?]+(?=\s+[A-Z])', paragraph)
            for sentence in current_sentences:
                sentence = sentence.strip()
                if sentence and len(sentence) > 15:  # Minimum meaningful length
                    # Clean up the sentence
                    sentence = re.sub(r'^[^A-Za-z]*', '', sentence)  # Remove leading non-letters
                    if sentence:
                        sentences.append(sentence)
    
    print(f"🎬 Improved B-Roll Keyword Extraction Test")
    print(f"📄 Script: Business Analyst Explanation (Improved)")
    print(f"📊 Total sentences: {len(sentences)}")
    print("=" * 70)
    
    # Extract keywords with improved system
    extractor = ImprovedKeywordExtractor()
    results = extractor.extract_cohesive_keywords(sentences)
    
    print("\n" + "=" * 70)
    print("🎯 IMPROVED KEYWORD EXTRACTION RESULTS")
    print("=" * 70)
    
    # Show first 10 sentences for detailed analysis
    for i in range(min(10, len(sentences))):
        sentence = sentences[i]
        keywords = results.get(i, [])
        print(f"\n📝 Sentence {i+1}:")
        print(f"   Text: \"{sentence[:80]}...\"")
        print(f"   🎥 B-roll keywords: {keywords}")
        
        # Show potential API search terms
        search_terms = [f"{' '.join(keywords[:2])}", keywords[0]] if keywords else ["office"]
        print(f"   🔍 API searches: {search_terms}")
    
    if len(sentences) > 10:
        print(f"\n... and {len(sentences) - 10} more sentences")
    
    # Improved analysis
    all_keywords = []
    for keywords in results.values():
        all_keywords.extend(keywords)
    
    keyword_freq = Counter(all_keywords)
    unique_keywords = set(all_keywords)
    
    print(f"\n📊 ANALYSIS:")
    print(f"   🏆 Most common: {dict(keyword_freq.most_common(5))}")
    print(f"   🎯 Unique keywords: {len(unique_keywords)}")
    print(f"   ♻️  Reuse rate: {(len(all_keywords) - len(unique_keywords)) / len(all_keywords) * 100:.1f}%")
    print(f"   🎨 Keyword variety: {', '.join(list(unique_keywords)[:10])}")
    
    # Test cohesion - check for jarring transitions
    transition_scores = []
    for i in range(1, len(results)):
        current = results.get(i, [])
        previous = results.get(i-1, [])
        
        if current and previous:
            # Simple overlap check
            overlap = len(set(current) & set(previous))
            transition_scores.append(overlap / max(len(current), len(previous)))
    
    avg_cohesion = sum(transition_scores) / len(transition_scores) if transition_scores else 0
    print(f"   🔗 Cohesion score: {avg_cohesion:.2f} (0=jarring, 1=perfect continuity)")

if __name__ == "__main__":
    test_improved_extraction()