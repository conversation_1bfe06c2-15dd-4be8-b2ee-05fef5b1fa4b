"""
Core video processing functionality with enhance    def process_video(self, video_path: Path, 
                     similarity_threshold: Optional[float] = None,
                     quality_threshold: Optional[float] = None,
                     max_frames: Optional[int] = None,
                     enable_transcription: bool = False) -> dict:ame extraction and quality assessment
"""
import cv2
import numpy as np
from pathlib import Path
from typing import List, Tuple, Callable, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from skimage.metrics import structural_similarity as ssim

from ..utils.file_manager import FileManager
from ..utils.config import config

try:
    from .transcription import transcribe_scenes, TranscriptionData
    TRANSCRIPTION_AVAILABLE = True
except ImportError:
    TRANSCRIPTION_AVAILABLE = False


class SceneData:
    """Data class for scene information"""
    def __init__(self, start_time: float, end_time: float, start_frame: int, end_frame: int):
        self.start_time = start_time
        self.end_time = end_time
        self.start_frame = start_frame
        self.end_frame = end_frame
        self.duration = end_time - start_time
        self.frame_count = end_frame - start_frame + 1


class FrameData:
    """Data class for frame information"""
    def __init__(self, frame: np.ndarray, timestamp: float, frame_number: int):
        self.frame = frame
        self.timestamp = timestamp
        self.frame_number = frame_number
        self.similarity_score = 0.0
        self.quality_score = 0.0


class VideoProcessor:
    """Handles video processing and frame extraction"""
    
    def __init__(self, progress_callback: Optional[Callable[[dict], None]] = None):
        self.progress_callback = progress_callback
        self.file_manager = FileManager()
        self.is_processing = False
        self.should_stop = False
        self.current_job_dir = None
        self._lock = threading.Lock()
    
    def process_video(self, video_path: Path, 
                     similarity_threshold: Optional[float] = None,
                     quality_threshold: Optional[float] = None,
                     max_frames: Optional[int] = None,
                     save_frames: bool = True,
                     split_scenes: bool = False,
                     enable_transcription: bool = False) -> dict:
        """
        Complete video processing pipeline: extract frames and optionally save them
        
        Returns:
            dict: Processing results including job_dir, frame_count, metadata
        """
        import time
        start_time = time.time()
        
        with self._lock:
            if self.is_processing:
                raise RuntimeError("Processing already in progress")
            self.is_processing = True
            self.should_stop = False
        
        # Use config defaults if not provided
        if similarity_threshold is None:
            similarity_threshold = config.processing.similarity_threshold
        if quality_threshold is None:
            quality_threshold = config.processing.quality_threshold
        if max_frames is None:
            max_frames = config.processing.max_frames
        
        try:
            # Validate video file
            is_valid, message = self.file_manager.validate_video_file(video_path)
            if not is_valid:
                raise ValueError(f"Invalid video file: {message}")
            
            # Create job directory if saving frames
            if save_frames:
                self.current_job_dir = self.file_manager.create_job_directory(video_path)
            
            # Extract frames and scene boundaries in one pass
            extracted_frames, scene_boundaries = self._extract_frames(
                video_path, similarity_threshold, quality_threshold, max_frames
            )
            
            print(f"Extracted {len(extracted_frames)} frames")
            
            # Convert scene boundaries to SceneData objects if scene splitting requested
            detected_scenes = []
            scene_video_paths = []
            scene_audio_paths = []
            transcriptions = []
            
            if split_scenes:
                print("Scene splitting enabled - creating scenes from detected boundaries...")
                detected_scenes = self._create_scenes_from_boundaries(scene_boundaries, extracted_frames)
                print(f"Created {len(detected_scenes)} scenes from boundaries")
                
                if detected_scenes and self.current_job_dir:
                    print("Creating scene video and audio files...")
                    scene_video_paths, scene_audio_paths = self._split_video_into_scenes(video_path, detected_scenes)
                    print(f"Created {len(scene_video_paths)} scene video files")
                    print(f"Created {len(scene_audio_paths)} YouTube-ready audio files (-14 LUFS)")
                    
                    # Add transcription if requested
                    print(f"Transcription settings - Enabled: {enable_transcription}, Available: {TRANSCRIPTION_AVAILABLE}")
                    if enable_transcription and TRANSCRIPTION_AVAILABLE:
                        print("🎤 Starting audio transcription...")
                        
                        # Update progress for transcription stage
                        self._update_progress({
                            'stage': 'transcribing',
                            'progress': 0.0,
                            'transcriptions_completed': 0,
                            'total_to_transcribe': len(detected_scenes)
                        })
                        
                        try:
                            # Prepare scene data for transcription
                            scenes_for_transcription = [
                                {
                                    'start_time': scene.start_time,
                                    'end_time': scene.end_time
                                }
                                for scene in detected_scenes
                            ]
                            
                            transcriptions = transcribe_scenes(
                                video_path, 
                                scenes_for_transcription, 
                                self.current_job_dir,
                                self.progress_callback
                            )
                            print(f"Transcribed {len(transcriptions)} scenes")
                            
                            # Update progress for transcription completion
                            self._update_progress({
                                'stage': 'transcription_complete',
                                'progress': 1.0,
                                'total_transcribed': len(transcriptions)
                            })
                            
                        except Exception as e:
                            print(f"❌ Transcription failed: {e}")
                            transcriptions = []
                    elif enable_transcription and not TRANSCRIPTION_AVAILABLE:
                        print("❌ Transcription requested but dependencies not available (install torch, transformers)")
                    elif not enable_transcription:
                        print("ℹ️ Transcription disabled in settings")
                    else:
                        print("ℹ️ Transcription not requested")
                else:
                    print("No scenes to split or no job directory available")
            else:
                print("Scene splitting disabled")
            
            # Save frames if requested
            saved_frame_paths = []
            if save_frames and self.current_job_dir:
                saved_frame_paths = self._save_frames(extracted_frames)
            
            # Get video info and create metadata
            video_info = self.get_video_info(video_path)
            
            # Update progress with video duration and scene info
            self._update_progress({
                'stage': 'metadata',
                'progress': 1.0,
                'video_info': video_info,
                'scenes_detected': len(detected_scenes),
                'duration': video_info.get('duration', 0),
                'total_scenes': len(detected_scenes)
            })
            
            metadata = {
                'video_file': str(video_path),
                'video_info': video_info,
                'processing_settings': {
                    'similarity_threshold': similarity_threshold,
                    'quality_threshold': quality_threshold,
                    'max_frames': max_frames,
                },
                'results': {
                    'total_extracted': len(extracted_frames),
                    'frames_saved': len(saved_frame_paths),
                    'scenes_detected': len(detected_scenes),
                    'scene_videos_created': len(scene_video_paths),
                    'scene_audio_files_created': len(scene_audio_paths),
                    'transcriptions_created': len(transcriptions),
                    'average_quality': np.mean([f.quality_score for f in extracted_frames]) if extracted_frames else 0,
                }
            }
            
            # Save metadata
            if save_frames and self.current_job_dir:
                self.file_manager.save_job_metadata(self.current_job_dir, metadata)
            
            # Calculate processing time
            processing_time = time.time() - start_time

            return {
                'success': True,
                'job_dir': self.current_job_dir,
                'extracted_frames': extracted_frames,
                'saved_paths': saved_frame_paths,
                'detected_scenes': detected_scenes,
                'scene_video_paths': scene_video_paths,
                'scene_audio_paths': scene_audio_paths,
                'transcriptions': transcriptions,
                'metadata': metadata,
                'processing_time': processing_time
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'job_dir': None,
                'extracted_frames': [],
                'saved_paths': [],
                'detected_scenes': [],
                'scene_video_paths': [],
                'scene_audio_paths': [],
                'transcriptions': [],
                'metadata': {}
            }
        finally:
            with self._lock:
                self.is_processing = False
                self.current_job_dir = None
    
    def _extract_frames(self, video_path: Path, similarity_threshold: float,
                       quality_threshold: float, max_frames: Optional[int]) -> Tuple[List[FrameData], List[float]]:
        """Internal frame extraction method that also captures scene boundaries"""
        cap = cv2.VideoCapture(str(video_path))
        
        if not cap.isOpened():
            raise ValueError(f"Cannot open video file: {video_path}")
        
        try:
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps if fps > 0 else 0
            
            # Notify progress
            self._update_progress({
                'stage': 'analyzing',
                'progress': 0.0,
                'total_frames': total_frames,
                'duration': duration,
                'fps': fps
            })
            
            extracted_frames = []
            scene_boundaries = [0.0]  # Always start with scene at 0.0
            frame_number = 0
            last_frame = None
            
            while True:
                if self.should_stop:
                    break
                
                ret, frame = cap.read()
                if not ret:
                    break
                
                timestamp = frame_number / fps if fps > 0 else frame_number
                
                # Skip frames if we have a maximum limit and it's reached
                if max_frames and len(extracted_frames) >= max_frames:
                    break
                
                # Calculate quality score
                quality_score = self._calculate_quality(frame)
                
                # Skip low quality frames
                if quality_score < quality_threshold:
                    frame_number += 1
                    continue
                
                # Check similarity with last extracted frame
                scene_boundary_detected = False
                if last_frame is not None:
                    similarity = self._calculate_similarity(frame, last_frame.frame)
                    if similarity >= similarity_threshold:
                        frame_number += 1
                        continue
                    else:
                        # This is a scene boundary - frame is significantly different
                        scene_boundary_detected = True
                
                # Add frame to extracted list
                frame_data = FrameData(frame.copy(), timestamp, frame_number)
                frame_data.quality_score = quality_score
                extracted_frames.append(frame_data)
                
                # Record scene boundary if this frame represents a significant change
                if scene_boundary_detected and (len(scene_boundaries) == 0 or timestamp > scene_boundaries[-1]):
                    scene_boundaries.append(timestamp)
                    print(f"Scene boundary detected at {timestamp:.1f}s (frame {frame_number})")
                
                last_frame = frame_data
                
                # Update progress with current frame
                progress = (frame_number / total_frames) if total_frames > 0 else 0
                self._update_progress({
                    'stage': 'extracting',
                    'progress': progress,
                    'current_frame': frame_number,
                    'extracted_count': len(extracted_frames),
                    'current_timestamp': timestamp,
                    'current_quality': quality_score,
                    'current_frame_data': frame.copy()  # Add current frame for preview
                })
                
                frame_number += 1
            
            # Add end time as final scene boundary if we have extracted frames
            if extracted_frames and scene_boundaries:
                if duration > scene_boundaries[-1]:
                    scene_boundaries.append(duration)
                elif len(scene_boundaries) == 1:
                    # Single scene case - ensure it has the full duration
                    scene_boundaries.append(duration)
                    
            print(f"Scene boundaries: {scene_boundaries}")
            
            # Final progress update
            self._update_progress({
                'stage': 'complete',
                'progress': 1.0,
                'extracted_count': len(extracted_frames),
                'total_processed': frame_number
            })
            
            return extracted_frames, scene_boundaries
            
        finally:
            cap.release()
    
    def _create_scenes_from_boundaries(self, scene_boundaries: List[float], extracted_frames: List[FrameData]) -> List[SceneData]:
        """Convert scene boundary timestamps into SceneData objects"""
        print(f"DEBUG: Creating scenes from {len(scene_boundaries)} boundaries: {scene_boundaries}")
        
        if not scene_boundaries or len(scene_boundaries) < 1:
            # No boundaries - create single scene from entire video duration
            if extracted_frames:
                video_duration = extracted_frames[-1].timestamp if extracted_frames else 10.0
                single_scene = SceneData(
                    start_time=0.0,
                    end_time=video_duration,
                    start_frame=0,
                    end_frame=extracted_frames[-1].frame_number if extracted_frames else 0
                )
                print(f"No boundaries found - creating single scene: 0.0s - {video_duration:.1f}s")
                return [single_scene]
            return []
        
        # If we only have one boundary (start time), estimate end time from extracted frames
        if len(scene_boundaries) == 1:
            if extracted_frames:
                video_duration = extracted_frames[-1].timestamp + 1.0  # Add buffer
                scene_boundaries.append(video_duration)
                print(f"Single boundary found - adding end time: {video_duration:.1f}s")
            else:
                # Fallback duration
                scene_boundaries.append(scene_boundaries[0] + 10.0)
        
        scenes = []
        min_scene_duration = getattr(config.processing, 'min_scene_duration', 1.0)  # Safe access
        
        print(f"Processing {len(scene_boundaries)} boundaries with min duration {min_scene_duration}s")
        
        # Create scenes from consecutive boundary pairs
        for i in range(len(scene_boundaries) - 1):
            start_time = scene_boundaries[i]
            end_time = scene_boundaries[i + 1]
            duration = end_time - start_time
            
            print(f"Boundary pair {i}: {start_time:.1f}s - {end_time:.1f}s (duration: {duration:.1f}s)")
            
            # Skip very short scenes
            if duration < min_scene_duration:
                print(f"Skipping short scene: {start_time:.1f}s - {end_time:.1f}s (duration: {duration:.1f}s)")
                continue
            
            # Find frame numbers for this time range
            start_frame = 0
            end_frame = 0
            
            # Find start frame
            for frame in extracted_frames:
                if frame.timestamp >= start_time:
                    start_frame = frame.frame_number
                    break
            
            # Find end frame
            for frame in reversed(extracted_frames):
                if frame.timestamp <= end_time:
                    end_frame = frame.frame_number
                    break
            
            # Ensure we have valid frame numbers
            if end_frame <= start_frame and extracted_frames:
                end_frame = extracted_frames[-1].frame_number
            
            scene = SceneData(
                start_time=start_time,
                end_time=end_time,
                start_frame=start_frame,
                end_frame=end_frame
            )
            scenes.append(scene)
            print(f"Scene created: {start_time:.1f}s - {end_time:.1f}s (duration: {duration:.1f}s, frames: {start_frame}-{end_frame})")
        
        print(f"Created {len(scenes)} scenes from {len(scene_boundaries)} boundaries")
        
        # If no scenes were created due to duration filtering, create a single scene
        if not scenes and extracted_frames:
            video_duration = extracted_frames[-1].timestamp + 1.0
            fallback_scene = SceneData(
                start_time=0.0,
                end_time=video_duration,
                start_frame=0,
                end_frame=extracted_frames[-1].frame_number
            )
            scenes.append(fallback_scene)
            print(f"No valid scenes created - fallback single scene: 0.0s - {video_duration:.1f}s")
        
        return scenes

    def _detect_scenes(self, extracted_frames: List[FrameData], video_path: Path) -> List[SceneData]:
        """Detect scene boundaries based on extracted frames"""
        if len(extracted_frames) < 1:
            return []
        
        # Get video info for proper timing
        video_info = self.get_video_info(video_path)
        fps = video_info['fps']
        total_frames = video_info['total_frames']
        
        scenes = []
        current_scene_start_frame = extracted_frames[0].frame_number
        current_scene_start_time = extracted_frames[0].timestamp
        
        # Use a much lower similarity threshold for scene detection 
        # Since extracted frames are already distinct, we need to catch bigger changes
        base_threshold = config.processing.similarity_threshold
        scene_similarity_threshold = max(0.3, base_threshold * 0.5)  # Much more sensitive
        
        print(f"Scene detection: analyzing {len(extracted_frames)} frames with threshold {scene_similarity_threshold:.2f}")
        
        self._update_progress({
            'stage': 'detecting_scenes',
            'progress': 0.0,
            'total_frames_to_analyze': len(extracted_frames)
        })
        
        # If we only have one frame, create a single scene for the entire video
        if len(extracted_frames) == 1:
            single_scene = SceneData(
                start_time=0.0,
                end_time=video_info.get('duration', extracted_frames[0].timestamp),
                start_frame=0,
                end_frame=video_info.get('total_frames', extracted_frames[0].frame_number)
            )
            scenes.append(single_scene)
            print(f"Single scene created: {single_scene.duration:.1f}s")
        else:
            # Analyze consecutive frames for scene changes
            for i in range(1, len(extracted_frames)):
                if self.should_stop:
                    break
                    
                current_frame = extracted_frames[i]
                previous_frame = extracted_frames[i-1]
                
                # Calculate similarity between consecutive extracted frames
                similarity = self._calculate_similarity(current_frame.frame, previous_frame.frame)
                
                print(f"Frame {i}: similarity = {similarity:.3f} (threshold: {scene_similarity_threshold:.3f})")
                
                # If similarity is low, this indicates a scene change
                if similarity < scene_similarity_threshold:
                    # End current scene at the previous frame
                    scene_end_frame = previous_frame.frame_number
                    scene_end_time = previous_frame.timestamp
                    
                    # Ensure scene has some duration - if timestamps are identical, 
                    # use a small time increment or space between frames
                    if scene_end_time <= current_scene_start_time:
                        # Calculate approximate time between frames
                        frame_gap = (current_frame.timestamp - previous_frame.timestamp) / 2
                        scene_end_time = current_scene_start_time + max(0.1, frame_gap)
                    
                    scene = SceneData(
                        start_time=current_scene_start_time,
                        end_time=scene_end_time,
                        start_frame=current_scene_start_frame,
                        end_frame=scene_end_frame
                    )
                    scenes.append(scene)
                    print(f"Scene detected: {scene.start_time:.1f}s - {scene.end_time:.1f}s (duration: {scene.duration:.1f}s)")
                    
                    # Start new scene
                    current_scene_start_frame = current_frame.frame_number
                    current_scene_start_time = current_frame.timestamp
                
                # Update progress
                progress = i / len(extracted_frames)
                self._update_progress({
                    'stage': 'detecting_scenes',
                    'progress': progress,
                    'scenes_detected': len(scenes),
                    'total_scenes': len(scenes),
                    'current_similarity': similarity,
                    'frames_processed': i + 1,
                    'total_frames': len(extracted_frames)
                })
            
            # Add final scene (from last scene change to end of video)
            if extracted_frames:
                final_frame = extracted_frames[-1]
                # Estimate video end time - use final frame timestamp or video duration
                video_duration = video_info.get('duration', final_frame.timestamp)
                final_scene_end_time = max(final_frame.timestamp, video_duration)
                
                final_scene = SceneData(
                    start_time=current_scene_start_time,
                    end_time=final_scene_end_time,
                    start_frame=current_scene_start_frame,
                    end_frame=video_info.get('total_frames', final_frame.frame_number)
                )
                scenes.append(final_scene)
                print(f"Final scene: {final_scene.start_time:.1f}s - {final_scene.end_time:.1f}s (duration: {final_scene.duration:.1f}s)")
        
        print(f"Total scenes before filtering: {len(scenes)}")
        
        # Filter out very short scenes
        min_scene_duration = config.processing.min_scene_duration
        filtered_scenes = [scene for scene in scenes if scene.duration >= min_scene_duration]
        
        print(f"Scenes after duration filter (min {min_scene_duration}s): {len(filtered_scenes)}")
        for i, scene in enumerate(filtered_scenes):
            print(f"  Scene {i+1}: {scene.start_time:.1f}s - {scene.end_time:.1f}s ({scene.duration:.1f}s)")
        
        self._update_progress({
            'stage': 'scenes_detected',
            'progress': 1.0,
            'scenes_detected': len(filtered_scenes),
            'total_scenes': len(filtered_scenes),
            'video_info': video_info,
            'duration': video_info.get('duration', 0)
        })
        
        return filtered_scenes
    
    def _split_video_into_scenes(self, video_path: Path, scenes: List[SceneData]) -> Tuple[List[Path], List[Path]]:
        """Split the video into separate scene files using FFmpeg"""
        if not scenes or not self.current_job_dir:
            return [], []
        
        # Check if FFmpeg is available
        import subprocess
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
            if result.returncode != 0:
                print("FFmpeg not available - skipping video scene splitting")
                return [], []
        except FileNotFoundError:
            print("FFmpeg not found - skipping video scene splitting")
            return [], []
        
        # Create scenes directory
        scenes_dir = self.current_job_dir / "scenes"
        scenes_dir.mkdir(exist_ok=True)
        
        scene_paths = []
        audio_paths = []
        total_scenes = len(scenes)
        
        self._update_progress({
            'stage': 'splitting_scenes',
            'progress': 0.0,
            'total_scenes': total_scenes
        })
        
        for i, scene in enumerate(scenes):
            if self.should_stop:
                break
            
            try:
                # Generate filenames
                base_name = f"scene_{i+1:02d}_{scene.start_time:.1f}s-{scene.end_time:.1f}s"
                scene_filename = f"{base_name}.mp4"
                audio_filename = f"{base_name}_audio.wav"
                
                scene_path = scenes_dir / scene_filename
                audio_path = scenes_dir / audio_filename
                
                duration = scene.end_time - scene.start_time
                
                # FFmpeg command to extract video scene
                video_cmd = [
                    'ffmpeg',
                    '-i', str(video_path),
                    '-ss', str(scene.start_time),
                    '-t', str(duration),
                    '-c', 'copy',  # Copy streams without re-encoding for speed
                    '-avoid_negative_ts', 'make_zero',
                    '-y',  # Overwrite output file
                    str(scene_path)
                ]
                
                # Run video extraction
                result = subprocess.run(
                    video_cmd,
                    capture_output=True,
                    text=True,
                    timeout=60  # 60 second timeout per scene
                )
                
                if result.returncode == 0 and scene_path.exists():
                    scene_paths.append(scene_path)
                else:
                    print(f"Failed to create video scene {i+1}: {result.stderr}")
                
                # Extract audio with YouTube-optimized settings (-14 LUFS)
                audio_success = self._extract_scene_audio_youtube_ready(
                    video_path, scene, audio_path, i + 1
                )
                
                if audio_success:
                    audio_paths.append(audio_path)
                
                # Update progress
                progress = (i + 1) / total_scenes
                self._update_progress({
                    'stage': 'splitting_scenes',
                    'progress': progress,
                    'scenes_created': len(scene_paths),
                    'audio_files_created': len(audio_paths),
                    'current_scene': i + 1
                })
                
            except subprocess.TimeoutExpired:
                print(f"Timeout creating scene {i+1}")
            except Exception as e:
                print(f"Error creating scene {i+1}: {e}")
        
        self._update_progress({
            'stage': 'scenes_split',
            'progress': 1.0,
            'total_scenes_created': len(scene_paths),
            'total_audio_files_created': len(audio_paths)
        })
        
        return scene_paths, audio_paths
    
    def _extract_scene_audio_youtube_ready(self, video_path: Path, scene: SceneData, 
                                         audio_path: Path, scene_number: int) -> bool:
        """Extract audio from scene with YouTube-optimized settings (-14 LUFS)"""
        import subprocess
        
        try:
            duration = scene.end_time - scene.start_time
            
            # FFmpeg command for YouTube-optimized audio extraction
            # YouTube recommends -14 LUFS with -1 dBTP peak limiting
            audio_cmd = [
                'ffmpeg',
                '-i', str(video_path),
                '-ss', str(scene.start_time),
                '-t', str(duration),
                '-vn',  # No video
                '-acodec', 'pcm_s16le',  # 16-bit PCM for high quality
                '-ar', '48000',  # 48kHz sample rate (YouTube standard)
                '-ac', '2',  # Stereo
                # Audio normalization filters for -14 LUFS
                '-af', 'loudnorm=I=-14:TP=-1:LRA=7:dual_mono=true:linear=true:print_format=json',
                '-y',  # Overwrite output file
                str(audio_path)
            ]
            
            print(f"🎵 Extracting YouTube-optimized audio for scene {scene_number} (-14 LUFS)")
            
            result = subprocess.run(
                audio_cmd,
                capture_output=True,
                text=True,
                timeout=120  # 2 minute timeout for audio processing
            )
            
            if result.returncode == 0 and audio_path.exists():
                # Get file size for verification
                file_size_mb = audio_path.stat().st_size / (1024 * 1024)
                print(f"✅ Audio extracted: {audio_path.name} ({file_size_mb:.1f} MB)")
                
                # Parse loudnorm output to show actual LUFS achieved
                if "loudnorm" in result.stderr:
                    try:
                        import re
                        # Look for JSON output in stderr
                        json_match = re.search(r'\{[^}]*"input_i"[^}]*\}', result.stderr)
                        if json_match:
                            import json
                            loudnorm_data = json.loads(json_match.group())
                            input_lufs = float(loudnorm_data.get('input_i', 0))
                            output_lufs = float(loudnorm_data.get('output_i', 0))
                            print(f"   📊 Loudness: {input_lufs:.1f} LUFS → {output_lufs:.1f} LUFS (target: -14 LUFS)")
                    except (json.JSONDecodeError, ValueError, KeyError):
                        print(f"   📊 Loudness normalization applied (target: -14 LUFS)")
                
                return True
            else:
                print(f"❌ Failed to extract audio for scene {scene_number}: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏱️ Timeout extracting audio for scene {scene_number}")
            return False
        except Exception as e:
            print(f"❌ Error extracting audio for scene {scene_number}: {e}")
            return False
    
    def _save_frames(self, extracted_frames: List[FrameData]) -> List[Path]:
        """Save extracted frames to disk"""
        if not self.current_job_dir:
            return []
        
        saved_paths = []
        total_frames = len(extracted_frames)
        
        for i, frame_data in enumerate(extracted_frames):
            if self.should_stop:
                break
            
            try:
                frame_path = self.file_manager.save_frame(
                    frame_data.frame,
                    self.current_job_dir,
                    frame_data.frame_number,
                    frame_data.timestamp,
                    config.processing.output_format
                )
                saved_paths.append(frame_path)
                
                # Update progress
                progress = (i + 1) / total_frames
                self._update_progress({
                    'stage': 'saving',
                    'progress': progress,
                    'saved_count': len(saved_paths),
                    'current_frame': i + 1
                })
                
            except Exception as e:
                # Log error but continue with other frames
                print(f"Failed to save frame {i}: {e}")
        
        return saved_paths
    
    def _calculate_similarity(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
        """Calculate similarity between two frames using SSIM"""
        # Convert to grayscale for comparison
        gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
        
        # Resize to standard size for consistent comparison
        gray1 = cv2.resize(gray1, (256, 256))
        gray2 = cv2.resize(gray2, (256, 256))
        
        # Calculate SSIM
        similarity_score, _ = ssim(gray1, gray2, full=True)
        return similarity_score
    
    def _calculate_quality(self, frame: np.ndarray) -> float:
        """Calculate frame quality using Laplacian variance (sharpness)"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        
        # Normalize to 0-1 range (typical values: 0-2000, good quality > 100)
        normalized_quality = min(laplacian_var / 500.0, 1.0)
        return normalized_quality
    
    def _update_progress(self, progress_info: dict):
        """Update progress via callback"""
        if self.progress_callback:
            self.progress_callback(progress_info)
    
    def stop_processing(self):
        """Signal to stop processing"""
        with self._lock:
            self.should_stop = True
    
    def get_video_info(self, video_path: Path) -> dict:
        """Get comprehensive video information"""
        cap = cv2.VideoCapture(str(video_path))
        
        if not cap.isOpened():
            raise ValueError(f"Cannot open video file: {video_path}")
        
        try:
            info = {
                'fps': cap.get(cv2.CAP_PROP_FPS),
                'total_frames': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
                'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                'duration': 0,
                'codec': 'Unknown',
                'file_size_mb': video_path.stat().st_size / (1024 * 1024),
                'file_name': video_path.name
            }
            
            if info['fps'] > 0:
                info['duration'] = info['total_frames'] / info['fps']
            
            # Try to get codec info (may not work on all systems)
            try:
                fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
                codec = ''.join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
                info['codec'] = codec.strip()
            except:
                pass
            
            return info
            
        finally:
            cap.release()

    # Legacy methods for backward compatibility
    def extract_frames(self, video_path, output_dir: str = None):
        """
        Legacy method: Extract distinct frames from video using similarity detection
        """
        video_path = Path(video_path)
        result = self.process_video(
            video_path, 
            save_frames=True
        )
        
        if result['success']:
            # Convert to legacy format for compatibility
            legacy_results = []
            for frame_data in result['extracted_frames']:
                legacy_results.append({
                    'path': str(result['saved_paths'][0].parent / f"frame_{frame_data.frame_number:06d}_{frame_data.timestamp:.2f}s.png"),
                    'frame_number': frame_data.frame_number,
                    'timestamp': frame_data.timestamp,
                    'similarity_score': frame_data.similarity_score
                })
            return legacy_results
        else:
            raise RuntimeError(result['error'])

    def set_progress_callback(self, callback):
        """Legacy method: Set callback function for progress updates"""
        self.progress_callback = callback