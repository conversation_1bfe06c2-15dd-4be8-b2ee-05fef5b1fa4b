"""
Base progress view class for operation-specific progress displays
"""
from abc import ABC, abstractmethod
import flet as ft
from typing import Dict, Any, Optional, List


class BaseProgressView(ABC):
    """Base class for operation-specific progress views"""
    
    def __init__(self, operation_config: Dict[str, Any]):
        self.operation_config = operation_config
        self.page: Optional[ft.Page] = None
        
        # Common components (set by framework)
        self.overall_progress: Optional[ft.ProgressBar] = None
        self.progress_text: Optional[ft.Text] = None
        self.stage_containers: List[ft.Container] = []
        self.cancel_button: Optional[ft.ElevatedButton] = None
    
    @abstractmethod
    def build_stats_panel(self) -> ft.Control:
        """Build the stats display panel - implement this for your operation"""
        pass
    
    @abstractmethod
    def build_preview_panel(self) -> ft.Control:
        """Build the preview area - implement this for your operation"""
        pass
    
    @abstractmethod
    def update_progress(self, progress_info: Dict[str, Any]):
        """Update progress display - implement this for your operation"""
        pass
    
    @abstractmethod
    def get_stages(self) -> List[str]:
        """Get list of stage names for this operation"""
        pass
    
    def build(self) -> ft.Control:
        """Build the complete progress view (common layout + operation-specific panels)"""
        # Build common header
        header = self._build_common_header()
        
        # Build main content area
        content = ft.Row([
            ft.Container(
                content=self.build_stats_panel(),
                width=400,
                padding=20
            ),
            ft.Container(
                content=self.build_preview_panel(),
                width=300,
                padding=20
            )
        ], alignment=ft.MainAxisAlignment.CENTER)
        
        # Build footer
        footer = self._build_common_footer()
        
        return ft.Column([
            header,
            content,
            footer
        ], expand=True, horizontal_alignment=ft.CrossAxisAlignment.CENTER)
    
    def _build_common_header(self) -> ft.Control:
        """Build common progress header (progress bar, stage indicators)"""
        # Overall progress bar
        self.overall_progress = ft.ProgressBar(
            width=600,
            color=ft.Colors.PRIMARY,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            value=0.0
        )
        
        # Progress text
        self.progress_text = ft.Text(
            "Starting...",
            size=16,
            color=ft.Colors.ON_SURFACE,
            text_align=ft.TextAlign.CENTER
        )
        
        # Stage indicators
        stages = self.get_stages()
        self.stage_containers = []
        stage_indicators = []
        
        for i, stage in enumerate(stages):
            container = ft.Container(
                content=ft.Text(str(i + 1), size=12, color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                width=30,
                height=30,
                bgcolor=ft.Colors.GREY_400,  # Default inactive
                border_radius=15,
                alignment=ft.alignment.center
            )
            self.stage_containers.append(container)
            
            stage_indicators.append(
                ft.Column([
                    container,
                    ft.Text(stage, size=10, color=ft.Colors.ON_SURFACE_VARIANT)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=5)
            )
        
        return ft.Column([
            ft.Text("Processing Status", size=20, weight=ft.FontWeight.BOLD, 
                   color=ft.Colors.PRIMARY, text_align=ft.TextAlign.CENTER),
            ft.Container(height=20),
            self.overall_progress,
            ft.Container(height=10),
            self.progress_text,
            ft.Container(height=20),
            ft.Row(stage_indicators, alignment=ft.MainAxisAlignment.CENTER, spacing=30)
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
    
    def _build_common_footer(self) -> ft.Control:
        """Build common footer with cancel button"""
        self.cancel_button = ft.ElevatedButton(
            "Cancel Processing",
            icon=ft.Icons.CANCEL,
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.RED_400,
            on_click=self.cancel_processing
        )
        
        return ft.Container(
            content=self.cancel_button,
            alignment=ft.alignment.center,
            padding=20
        )
    
    def update_stage(self, stage_index: int):
        """Update stage indicators (called by framework)"""
        for i, container in enumerate(self.stage_containers):
            if i < stage_index:
                container.bgcolor = ft.Colors.GREEN_400  # Completed
            elif i == stage_index:
                container.bgcolor = ft.Colors.BLUE_400   # Active
            else:
                container.bgcolor = ft.Colors.GREY_400   # Inactive
    
    def cancel_processing(self, e=None):
        """Cancel processing - can be overridden by subclasses"""
        print("🛑 Cancelling operation...")
        if self.cancel_button:
            self.cancel_button.disabled = True
        if self.progress_text:
            self.progress_text.value = "Cancelling..."
        if self.page:
            self.page.update()
    
    def set_page(self, page: ft.Page):
        """Set page reference (called by framework)"""
        self.page = page