"""
Quick fix for all with_opacity issues in tabs
"""
import re
import os

def fix_with_opacity_in_file(file_path):
    """Fix with_opacity calls in a file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix common patterns
    replacements = [
        (r'ft\.Colors\.PRIMARY\.with_opacity\(0\.3\)', 'ft.Colors.PRIMARY'),
        (r'ft\.Colors\.SECONDARY\.with_opacity\(0\.3\)', 'ft.Colors.SECONDARY'),
        (r'ft\.Colors\.PRIMARY_CONTAINER\.with_opacity\(0\.3\)', 'ft.Colors.BLUE_100'),
        (r'ft\.Colors\.SECONDARY_CONTAINER\.with_opacity\(0\.3\)', 'ft.Colors.GREEN_100'),
        (r'ft\.Colors\.SURFACE\.with_opacity\(0\.3\)', 'ft.Colors.GREY_200'),
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Fixed {file_path}")

# Fix all tab files
tab_files = [
    'src/ui/tabs/video_processing_tab.py',
    'src/ui/tabs/audio_splitting_tab.py', 
    'src/ui/tabs/f5_tts_tab.py'
]

for file_path in tab_files:
    if os.path.exists(file_path):
        fix_with_opacity_in_file(file_path)
    else:
        print(f"❌ File not found: {file_path}")

print("🎉 All with_opacity issues fixed!")