"""
Application Settings Tab Wireframe
Demonstrates global application preferences and configuration
"""
import flet as ft
from shared.wireframe_components import *


class SettingsWireframe:
    def __init__(self, page: ft.Page):
        self.page = page
        
        # Global settings
        self.theme_mode = "dark"
        self.language = "en"
        self.notifications_enabled = True
        self.auto_save_interval = 5  # minutes
        self.default_output_path = "C:\\Users\\<USER>\\Documents\\Vid2Frames"
        self.temp_cleanup_days = 7
        self.memory_limit_gb = 8
        
        self.build_components()
    
    def build_components(self):
        """Build all UI components"""
        # User Interface section
        self.ui_section = self.create_ui_section()
        
        # File Management section
        self.file_section = self.create_file_section()
        
        # Performance section
        self.performance_section = self.create_performance_section()
        
        # Advanced section
        self.advanced_section = self.create_advanced_section()
        
        # Action buttons
        self.action_buttons = ft.Row([
            create_action_button("Reset to Defaults", ft.Icons.RESTORE, ft.Colors.ORANGE, self.reset_defaults),
            create_action_button("Export Settings", ft.Icons.DOWNLOAD, ft.Colors.BLUE, self.export_settings),
            create_action_button("Import Settings", ft.Icons.UPLOAD, ft.Colors.PURPLE, self.import_settings),
            create_action_button("Save Settings", ft.Icons.SAVE, ft.Colors.GREEN, self.save_settings),
        ], alignment=ft.MainAxisAlignment.CENTER, spacing=10, wrap=True)
    
    def create_ui_section(self):
        """Create user interface preferences section"""
        return create_settings_section("User Interface", [
            create_setting_row(
                "Theme:",
                ft.Dropdown(
                    options=[
                        ft.dropdown.Option("system", "System Default"),
                        ft.dropdown.Option("light", "Light Mode"),
                        ft.dropdown.Option("dark", "Dark Mode")
                    ],
                    value=self.theme_mode,
                    width=150,
                    on_change=self.on_theme_change
                ),
                "Appearance theme for the application"
            ),
            create_setting_row(
                "Language:",
                ft.Dropdown(
                    options=[
                        ft.dropdown.Option("en", "English"),
                        ft.dropdown.Option("es", "Español"),
                        ft.dropdown.Option("fr", "Français"),
                        ft.dropdown.Option("de", "Deutsch"),
                        ft.dropdown.Option("ja", "日本語")
                    ],
                    value=self.language,
                    width=150,
                    on_change=self.on_language_change
                ),
                "Application display language"
            ),
            create_setting_row(
                "Notifications:",
                ft.Switch(value=self.notifications_enabled, on_change=self.on_notifications_change),
                "Show system notifications for completed tasks"
            ),
            create_setting_row(
                "Auto-save Interval:",
                ft.Slider(
                    min=1, max=30, value=self.auto_save_interval,
                    divisions=29, label=f"{self.auto_save_interval} min",
                    on_change=self.on_autosave_change, width=200
                ),
                "Automatically save work and settings"
            )
        ])
    
    def create_file_section(self):
        """Create file management section"""
        return create_settings_section("File Management", [
            create_setting_row(
                "Default Output Directory:",
                ft.Row([
                    ft.TextField(
                        value=self.default_output_path,
                        width=300,
                        on_change=self.on_output_path_change
                    ),
                    ft.ElevatedButton(
                        "Browse",
                        icon=ft.Icons.FOLDER_OPEN,
                        on_click=self.browse_output_path
                    )
                ], spacing=10),
                "Default location for saving processed files"
            ),
            create_setting_row(
                "Temporary File Cleanup:",
                ft.Slider(
                    min=1, max=30, value=self.temp_cleanup_days,
                    divisions=29, label=f"{self.temp_cleanup_days} days",
                    on_change=self.on_cleanup_change, width=200
                ),
                "Automatically delete temporary files older than specified days"
            ),
            create_setting_row(
                "File Organization:",
                ft.Column([
                    ft.Switch(label="Create timestamped folders", value=True),
                    ft.Switch(label="Organize by processing type", value=True),
                    ft.Switch(label="Keep original file structure", value=False)
                ], spacing=5),
                "How to organize output files and folders"
            )
        ])
    
    def create_performance_section(self):
        """Create performance settings section"""
        return create_settings_section("Performance", [
            create_setting_row(
                "Memory Limit (GB):",
                ft.Slider(
                    min=2, max=32, value=self.memory_limit_gb,
                    divisions=30, label=f"{self.memory_limit_gb} GB",
                    on_change=self.on_memory_change, width=200
                ),
                "Maximum RAM usage for processing operations"
            ),
            create_setting_row(
                "GPU Acceleration:",
                ft.Column([
                    ft.Switch(label="Enable GPU processing", value=True),
                    ft.Dropdown(
                        label="GPU Device",
                        options=[
                            ft.dropdown.Option("auto", "Auto-detect"),
                            ft.dropdown.Option("cuda:0", "NVIDIA GPU 0"),
                            ft.dropdown.Option("cpu", "CPU Only")
                        ],
                        value="auto",
                        width=200
                    )
                ], spacing=10),
                "Use GPU acceleration when available"
            ),
            create_setting_row(
                "Priority Settings:",
                ft.Column([
                    ft.Dropdown(
                        label="Process Priority",
                        options=[
                            ft.dropdown.Option("low", "Low (background)"),
                            ft.dropdown.Option("normal", "Normal"),
                            ft.dropdown.Option("high", "High")
                        ],
                        value="normal",
                        width=200
                    ),
                    ft.Switch(label="Prevent system sleep during processing", value=True)
                ], spacing=10),
                "System resource allocation settings"
            )
        ])
    
    def create_advanced_section(self):
        """Create advanced settings section"""
        return create_settings_section("Advanced", [
            create_setting_row(
                "Logging Level:",
                ft.Dropdown(
                    options=[
                        ft.dropdown.Option("info", "Info"),
                        ft.dropdown.Option("debug", "Debug"),
                        ft.dropdown.Option("warning", "Warning Only"),
                        ft.dropdown.Option("error", "Error Only")
                    ],
                    value="info",
                    width=150,
                    on_change=self.on_logging_change
                ),
                "Detail level for application logs"
            ),
            create_setting_row(
                "Network Settings:",
                ft.Column([
                    ft.TextField(label="Proxy Server (optional)", width=250),
                    ft.Slider(
                        min=5, max=60, value=30,
                        divisions=11, label="30 sec",
                        width=200
                    ),
                    ft.Text("Connection timeout", size=12, color=ft.Colors.ON_SURFACE_VARIANT)
                ], spacing=5),
                "Network and internet connection settings"
            ),
            create_setting_row(
                "Developer Options:",
                ft.Column([
                    ft.Switch(label="Enable debug mode", value=False),
                    ft.Switch(label="Show performance metrics", value=False),
                    ft.Switch(label="Enable experimental features", value=False)
                ], spacing=5),
                "Advanced options for debugging and testing"
            ),
            create_setting_row(
                "Data Privacy:",
                ft.Column([
                    ft.Switch(label="Send anonymous usage statistics", value=False),
                    ft.Switch(label="Enable crash reporting", value=True),
                    ft.ElevatedButton("Clear All Data", icon=ft.Icons.DELETE_FOREVER, on_click=self.clear_data)
                ], spacing=10),
                "Privacy and data collection preferences"
            )
        ])
    
    def build(self):
        """Build the complete tab content"""
        header = create_header(
            "Application Settings",
            "Configure global preferences and application behavior"
        )
        
        # Settings info banner
        info_banner = ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.INFO_OUTLINE, color=ft.Colors.BLUE),
                ft.Text(
                    "These settings apply globally to all tabs and will be saved automatically. "
                    "Tab-specific settings are configured within each individual tab.",
                    size=12,
                    color=ft.Colors.ON_SURFACE_VARIANT,
                    expand=True
                )
            ], spacing=10),
            bgcolor=ft.Colors.BLUE_100,
            border_radius=8,
            padding=15,
            margin=20,
            border=ft.border.all(1, ft.Colors.BLUE_300)
        )
        
        content = ft.Column([
            header,
            info_banner,
            self.ui_section,
            self.file_section,
            self.performance_section,
            self.advanced_section,
            ft.Container(
                content=self.action_buttons,
                margin=ft.margin.symmetric(vertical=30)
            )
        ], spacing=20, scroll=ft.ScrollMode.AUTO)
        
        return create_tab_content(content)
    
    def on_theme_change(self, e):
        """Handle theme change"""
        self.theme_mode = e.control.value
        
        # Apply theme change immediately
        if e.control.value == "light":
            self.page.theme_mode = ft.ThemeMode.LIGHT
        elif e.control.value == "dark":
            self.page.theme_mode = ft.ThemeMode.DARK
        else:
            self.page.theme_mode = ft.ThemeMode.SYSTEM
        
        self.page.update()
        self.page.show_snack_bar(ft.SnackBar(ft.Text(f"Theme changed to {e.control.value} mode")))
    
    def on_language_change(self, e):
        """Handle language change"""
        self.language = e.control.value
        self.page.show_snack_bar(ft.SnackBar(ft.Text(f"Language changed to {e.control.value} (restart required)")))
    
    def on_notifications_change(self, e):
        """Handle notifications toggle"""
        self.notifications_enabled = e.control.value
        status = "enabled" if e.control.value else "disabled"
        self.page.show_snack_bar(ft.SnackBar(ft.Text(f"Notifications {status}")))
    
    def on_autosave_change(self, e):
        """Handle auto-save interval change"""
        self.auto_save_interval = int(e.control.value)
        e.control.label = f"{int(e.control.value)} min"
        self.page.update()
    
    def on_output_path_change(self, e):
        """Handle output path change"""
        self.default_output_path = e.control.value
    
    def browse_output_path(self, e):
        """Simulate folder browser"""
        self.page.show_snack_bar(ft.SnackBar(ft.Text("📁 Opening folder browser...")))
    
    def on_cleanup_change(self, e):
        """Handle cleanup days change"""
        self.temp_cleanup_days = int(e.control.value)
        e.control.label = f"{int(e.control.value)} days"
        self.page.update()
    
    def on_memory_change(self, e):
        """Handle memory limit change"""
        self.memory_limit_gb = int(e.control.value)
        e.control.label = f"{int(e.control.value)} GB"
        self.page.update()
    
    def on_logging_change(self, e):
        """Handle logging level change"""
        self.page.show_snack_bar(ft.SnackBar(ft.Text(f"Logging level set to {e.control.value}")))
    
    def reset_defaults(self, e):
        """Reset all settings to defaults"""
        # Show confirmation dialog
        def confirm_reset(e):
            dialog.open = False
            self.page.update()
            if e.control.text == "Reset":
                self.page.show_snack_bar(ft.SnackBar(ft.Text("🔄 All settings reset to defaults")))
        
        dialog = ft.AlertDialog(
            title=ft.Text("Reset Settings"),
            content=ft.Text("Are you sure you want to reset all settings to their default values? This action cannot be undone."),
            actions=[
                ft.TextButton("Cancel", on_click=confirm_reset),
                ft.TextButton("Reset", on_click=confirm_reset)
            ]
        )
        
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()
    
    def export_settings(self, e):
        """Export settings to file"""
        self.page.show_snack_bar(ft.SnackBar(ft.Text("💾 Exporting settings to vid2frames_settings.json")))
    
    def import_settings(self, e):
        """Import settings from file"""
        self.page.show_snack_bar(ft.SnackBar(ft.Text("📁 Opening file browser to import settings...")))
    
    def save_settings(self, e):
        """Save current settings"""
        self.page.show_snack_bar(ft.SnackBar(ft.Text("✅ Settings saved successfully")))
    
    def clear_data(self, e):
        """Clear all application data"""
        def confirm_clear(e):
            dialog.open = False
            self.page.update()
            if e.control.text == "Clear":
                self.page.show_snack_bar(ft.SnackBar(ft.Text("🗑️ All application data cleared")))
        
        dialog = ft.AlertDialog(
            title=ft.Text("Clear All Data", color=ft.Colors.RED),
            content=ft.Text(
                "This will permanently delete all application data including:\\n"
                "• Processing history\\n"
                "• Saved presets\\n"
                "• Temporary files\\n"
                "• User preferences\\n\\n"
                "This action cannot be undone. Are you sure?"
            ),
            actions=[
                ft.TextButton("Cancel", on_click=confirm_clear),
                ft.TextButton("Clear", on_click=confirm_clear, style=ft.ButtonStyle(color=ft.Colors.RED))
            ]
        )
        
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()


def main(page: ft.Page):
    page.title = "Application Settings - Wireframe"
    page.theme_mode = ft.ThemeMode.DARK
    page.window.width = 1200
    page.window.height = 800
    
    wireframe = SettingsWireframe(page)
    page.add(wireframe.build())


if __name__ == "__main__":
    ft.app(target=main)