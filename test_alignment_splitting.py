"""
Test script for audio splitting using existing alignment data
"""
import sys
from pathlib import Path

# Add src to path so we can import modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.core.audio_splitter_from_alignment import split_audio_from_alignment_file
    
    def test_alignment_splitting():
        """Test audio splitting with your alignment data"""
        print("🎵 Testing Audio Splitting from Alignment Data")
        print("=" * 50)
        
        # Example paths (update these to match your files)
        audio_path = Path("C:/Users/<USER>/Downloads/7habits/autonoe_7habits_split_20250930_171245/audio.wav")  # or .mp3
        alignment_json_path = Path("C:/Users/<USER>/Downloads/7habits/autonoe_7habits_split_20250930_171245/alignment_info.json")
        output_dir = Path("C:/Users/<USER>/Downloads/7habits/audio_segments_test")
        
        # Check if files exist
        if not alignment_json_path.exists():
            print(f"❌ Alignment file not found: {alignment_json_path}")
            print("Please update the path in this script to match your alignment_info.json file")
            return False
        
        if not audio_path.exists():
            # Try common audio file extensions
            for ext in ['.wav', '.mp3', '.m4a', '.flac']:
                test_path = alignment_json_path.parent / f"audio{ext}"
                if test_path.exists():
                    audio_path = test_path
                    break
            else:
                print(f"❌ Audio file not found. Please place your audio file in:")
                print(f"   {alignment_json_path.parent}")
                print("   with name: audio.wav, audio.mp3, audio.m4a, or audio.flac")
                return False
        
        print(f"📁 Audio file: {audio_path}")
        print(f"📄 Alignment file: {alignment_json_path}")
        print(f"📂 Output directory: {output_dir}")
        print()
        
        def progress_update(message, progress):
            print(f"[{progress*100:6.1f}%] {message}")
        
        # Split the audio
        success = split_audio_from_alignment_file(
            audio_path=audio_path,
            alignment_json_path=alignment_json_path,
            output_dir=output_dir,
            output_format="wav",  # Change to "mp3" or "flac" if desired
            buffer_ms=100,  # 100ms buffer at start/end of each segment
            progress_callback=progress_update
        )
        
        if success:
            print()
            print("🎉 SUCCESS! Audio has been split using your alignment data.")
            print(f"📂 Check the output directory: {output_dir}")
            print()
            print("Expected results:")
            print("- segment_001_lets_get_straight_into_the_7_habits.wav")
            print("- segment_002_this_book_wants_your_life_to_run.wav") 
            print("- segment_003_stick_around_by_the_end_youll.wav")
            print("- ... (and more segments)")
            print("- alignment_info.json (copy of original alignment data)")
        else:
            print("❌ Audio splitting failed. Check the error messages above.")
            
        return success
    
    if __name__ == "__main__":
        test_alignment_splitting()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the Vid2Frames directory")
    
except Exception as e:
    print(f"❌ Error: {e}")