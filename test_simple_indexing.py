"""
Simple test to debug the audio-video indexing issue
"""
import os
import sys
sys.path.append('src')

def test_audio_video_indexing():
    # Simulate the data structure from B-roll generation
    audio_files = [
        "audio_001.wav",  # index 0
        "audio_002.wav",  # index 1 
        "audio_003.wav",  # index 2
    ]
    
    # This is how broll_data is structured (sentence_idx as key)
    broll_data = {
        0: [{"video_id": "123", "download_url": "video1.mp4"}],  # sentence 0
        1: [{"video_id": "456", "download_url": "video2.mp4"}],  # sentence 1
        2: [{"video_id": "789", "download_url": "video3.mp4"}],  # sentence 2
    }
    
    print("Audio files:")
    for i, audio_file in enumerate(audio_files):
        print(f"  Index {i}: {audio_file}")
    
    print("\nB-roll data:")
    for sentence_idx, videos in broll_data.items():
        print(f"  Sentence {sentence_idx}: {videos[0]['video_id']}")
    
    print("\nCombined video creation (current logic):")
    for sentence_idx, videos in broll_data.items():
        # This is the current logic in create_combined_videos()
        audio_file = audio_files[sentence_idx]  # Should work correctly
        video_info = videos[0]
        
        print(f"  Sentence {sentence_idx}:")
        print(f"    Audio: {audio_file}")
        print(f"    Video: {video_info['video_id']}")
        
        # Check if indexing matches
        expected_audio = f"audio_00{sentence_idx + 1}.wav"
        if audio_file == expected_audio:
            print(f"    ✅ Indexing matches")
        else:
            print(f"    ❌ Indexing mismatch! Expected {expected_audio}")

if __name__ == "__main__":
    test_audio_video_indexing()