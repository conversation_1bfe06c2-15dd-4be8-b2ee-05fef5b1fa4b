#!/usr/bin/env python3
"""
Test persistent settings functionality for audio splitting
"""

import sys
from pathlib import Path

# Add src to path for imports
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.utils.config import config

def test_persistent_settings():
    """Test that audio splitting settings persist correctly"""
    print("🧪 Testing Persistent Audio Settings")
    print("=" * 50)
    
    # Test 1: Check default values
    print(f"📋 Default audio buffer: {config.processing.audio_buffer_ms}ms")
    print(f"📋 Default similarity threshold: {config.processing.audio_similarity_threshold}")
    print(f"📋 Default output format: {config.processing.audio_output_format}")
    
    # Test 2: Change values and save
    print("\n🔧 Changing settings...")
    original_buffer = config.processing.audio_buffer_ms
    original_threshold = config.processing.audio_similarity_threshold
    original_format = config.processing.audio_output_format
    
    config.processing.audio_buffer_ms = 300
    config.processing.audio_similarity_threshold = 0.9
    config.processing.audio_output_format = "MP3"
    config.save_settings()
    
    print(f"✓ Set audio buffer to: {config.processing.audio_buffer_ms}ms")
    print(f"✓ Set similarity threshold to: {config.processing.audio_similarity_threshold}")
    print(f"✓ Set output format to: {config.processing.audio_output_format}")
    
    # Test 3: Create new config instance to verify persistence
    print("\n🔄 Testing persistence (creating new config instance)...")
    from src.utils.config import ConfigManager
    new_config = ConfigManager()
    
    print(f"📖 Loaded audio buffer: {new_config.processing.audio_buffer_ms}ms")
    print(f"📖 Loaded similarity threshold: {new_config.processing.audio_similarity_threshold}")
    print(f"📖 Loaded output format: {new_config.processing.audio_output_format}")
    
    # Verify values persist
    success = True
    if new_config.processing.audio_buffer_ms != 300:
        print("❌ Buffer setting not persisted!")
        success = False
    else:
        print("✅ Buffer setting persisted correctly")
        
    if new_config.processing.audio_similarity_threshold != 0.9:
        print("❌ Similarity threshold not persisted!")
        success = False
    else:
        print("✅ Similarity threshold persisted correctly")
        
    if new_config.processing.audio_output_format != "MP3":
        print("❌ Output format not persisted!")
        success = False
    else:
        print("✅ Output format persisted correctly")
    
    # Test 4: Test AudioSplitter with settings
    print("\n🎵 Testing AudioSplitter with persistent settings...")
    from src.core.audio_splitter import AudioSplitter
    
    # Check if audio processing is available
    from src.core.audio_splitter import AUDIO_PROCESSING_AVAILABLE
    if not AUDIO_PROCESSING_AVAILABLE:
        print("⚠️ Audio processing libraries not available - skipping AudioSplitter test")
    else:
        splitter = AudioSplitter()
        print(f"🔊 AudioSplitter loaded buffer: {splitter.buffer_ms}ms")
        print(f"🔊 AudioSplitter loaded threshold: {splitter.similarity_threshold}")
        print(f"🔊 AudioSplitter loaded format: {splitter.output_format}")
        
        if (splitter.buffer_ms == 300 and 
            splitter.similarity_threshold == 0.9 and 
            splitter.output_format == "MP3"):
            print("✅ AudioSplitter loaded settings correctly")
        else:
            print("❌ AudioSplitter settings mismatch!")
            success = False
    
    # Test 5: Test update_settings method
    if AUDIO_PROCESSING_AVAILABLE:
        print("\n🔧 Testing settings update method...")
        splitter.update_settings(buffer_ms=250, similarity_threshold=0.85, output_format="FLAC")
        
        # Create another config to verify
        test_config = ConfigManager()
        if (test_config.processing.audio_buffer_ms == 250 and
            test_config.processing.audio_similarity_threshold == 0.85 and
            test_config.processing.audio_output_format == "FLAC"):
            print("✅ Settings update method works correctly")
        else:
            print("❌ Settings update method failed!")
            success = False
    
    # Cleanup: Restore original values
    print("\n🧹 Restoring original settings...")
    config.processing.audio_buffer_ms = original_buffer
    config.processing.audio_similarity_threshold = original_threshold
    config.processing.audio_output_format = original_format
    config.save_settings()
    print("✅ Original settings restored")
    
    # Final result
    print("\n" + "=" * 50)
    if success:
        print("🎉 All persistent settings tests PASSED!")
        return True
    else:
        print("❌ Some persistent settings tests FAILED!")
        return False

if __name__ == "__main__":
    success = test_persistent_settings()
    sys.exit(0 if success else 1)