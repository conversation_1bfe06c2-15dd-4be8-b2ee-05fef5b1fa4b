"""
Install F5-TTS dependencies for Vid2Frames
This script attempts to install the required packages for F5-TTS functionality
"""

import subprocess
import sys
from pathlib import Path

def install_package(package_name, pip_name=None):
    """Install a Python package via pip"""
    if pip_name is None:
        pip_name = package_name
    
    try:
        print(f"🔄 Installing {package_name}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", pip_name
        ], capture_output=True)
        print(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}: {e}")
        return False

def install_f5_tts_dependencies():
    """Install F5-TTS and related dependencies"""
    print("🚀 Installing F5-TTS dependencies...")
    print("=" * 50)
    
    # Core dependencies
    dependencies = [
        ("soundfile", "soundfile"),
        ("librosa", "librosa"),
        ("numpy", "numpy"),
        ("scipy", "scipy"),
    ]
    
    success_count = 0
    for name, pip_name in dependencies:
        if install_package(name, pip_name):
            success_count += 1
    
    print(f"\n📊 Installed {success_count}/{len(dependencies)} core dependencies")
    
    # Try to install F5-TTS from various sources
    print("\n🎤 Attempting to install F5-TTS...")
    
    f5_tts_sources = [
        # Try GitHub repository (most likely source)
        "git+https://github.com/SWivid/F5-TTS.git",
        # Try official package if available
        "f5-tts",
        # Try alternative names
        "f5_tts",
    ]
    
    f5_installed = False
    for source in f5_tts_sources:
        print(f"🔄 Trying to install F5-TTS from: {source}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", source
            ], capture_output=True, timeout=300)  # 5 minute timeout
            print(f"✅ F5-TTS installed from {source}")
            f5_installed = True
            break
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            print(f"❌ Failed to install from {source}")
            continue
    
    if not f5_installed:
        print("⚠️  F5-TTS not installed. The system will use mock generation.")
        print("   You can manually install F5-TTS later if available.")
    
    print("\n🎉 Dependency installation complete!")
    return f5_installed

if __name__ == "__main__":
    install_f5_tts_dependencies()