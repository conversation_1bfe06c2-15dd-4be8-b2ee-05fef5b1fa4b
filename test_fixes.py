#!/usr/bin/env python3
"""
Test script to verify the fixes for zip export and file selection clearing
"""
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.file_manager import FileManager
from src.ui.upload_view import UploadView
from src.ui.results_view import ResultsView


def test_file_manager_zip_export():
    """Test FileManager ZIP export functionality"""
    print("Testing FileManager ZIP export...")
    
    try:
        fm = FileManager()
        # Check if method exists and is callable
        assert hasattr(fm, 'export_frames_as_zip'), "export_frames_as_zip method missing"
        assert callable(getattr(fm, 'export_frames_as_zip')), "export_frames_as_zip not callable"
        print("✓ FileManager ZIP export method exists")
        
        # Test method signature
        import inspect
        sig = inspect.signature(fm.export_frames_as_zip)
        params = list(sig.parameters.keys())
        expected_params = ['job_dir', 'zip_path', 'frame_indices']
        for param in expected_params:
            assert param in params, f"Missing parameter: {param}"
        print("✓ FileManager ZIP export method has correct signature")
        
    except Exception as e:
        print(f"✗ FileManager ZIP export test failed: {e}")
        return False
    
    return True


def test_upload_view_clear_selection():
    """Test UploadView clear selection functionality"""
    print("Testing UploadView clear selection...")
    
    try:
        # Mock callback
        def mock_callback(path):
            pass
        
        uv = UploadView(on_video_selected=mock_callback)
        
        # Check if method exists
        assert hasattr(uv, 'clear_selection'), "clear_selection method missing"
        assert callable(getattr(uv, 'clear_selection')), "clear_selection not callable"
        print("✓ UploadView clear_selection method exists")
        
        # Build the UI to initialize elements
        uv.build()
        
        # Check if UI elements exist after build
        assert hasattr(uv, 'filename_text'), "filename_text attribute missing"
        assert hasattr(uv, 'file_details_text'), "file_details_text attribute missing"
        assert hasattr(uv, 'file_info_section'), "file_info_section attribute missing"
        assert hasattr(uv, 'upload_area'), "upload_area attribute missing"
        print("✓ UploadView has required UI elements after build")
        
        # Test that clear_selection can be called without error
        uv.clear_selection()
        print("✓ UploadView clear_selection executes without error")
        
    except Exception as e:
        print(f"✗ UploadView clear selection test failed: {e}")
        return False
    
    return True


def test_results_view_zip_export():
    """Test ResultsView ZIP export functionality"""
    print("Testing ResultsView ZIP export...")
    
    try:
        # Mock main window
        class MockMainWindow:
            def __init__(self):
                self.page = None
        
        rv = ResultsView(main_window=MockMainWindow())
        
        # Check if method exists
        assert hasattr(rv, 'export_as_zip'), "export_as_zip method missing"
        assert callable(getattr(rv, 'export_as_zip')), "export_as_zip not callable"
        print("✓ ResultsView export_as_zip method exists")
        
        # Check helper methods
        helper_methods = [
            '_on_zip_save_result',
            '_create_zip_file', 
            '_create_progress_dialog',
            '_show_info_dialog',
            '_show_error_dialog',
            '_close_current_dialog'
        ]
        
        for method in helper_methods:
            assert hasattr(rv, method), f"{method} method missing"
            assert callable(getattr(rv, method)), f"{method} not callable"
        
        print("✓ ResultsView has all required helper methods")
        
    except Exception as e:
        print(f"✗ ResultsView ZIP export test failed: {e}")
        return False
    
    return True


def main():
    """Run all tests"""
    print("Running fix verification tests...\n")
    
    tests = [
        test_file_manager_zip_export,
        test_upload_view_clear_selection,
        test_results_view_zip_export,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ Test passed\n")
            else:
                failed += 1
                print("✗ Test failed\n")
        except Exception as e:
            failed += 1
            print(f"✗ Test failed with exception: {e}\n")
    
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The fixes should work correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())