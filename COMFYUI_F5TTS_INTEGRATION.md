# ComfyUI F5-TTS Integration Architecture

## Overview

Vid2Frames integrates with ComfyUI's F5-TTS implementation to provide high-quality text-to-speech generation. This document explains the architecture, implementation details, and how the integration works.

## Architecture

### Hybrid F5-TTS Architecture

Vid2Frames implements a **hybrid F5-TTS architecture** that intelligently chooses between different F5-TTS implementations:

```
┌─────────────────────────────────────────────────────────────┐
│                    F5TTSProcessor                           │
├─────────────────────────────────────────────────────────────┤
│  1. ComfyUI F5-TTS (Primary - ChatterBoxF5TTS)            │
│  2. Standard F5-TTS (Fallback - F5TTS API)                 │
│  3. Mock Generation (Testing/Development)                   │
└─────────────────────────────────────────────────────────────┘
```

### Detection and Prioritization Logic

The system follows this priority order:

1. **ComfyUI F5-TTS Detection** (Preferred):
   - Path: `C:/Users/<USER>/Documents/ComfyUI/custom_nodes/chatterbox_srt_voice`
   - Implementation: `ChatterBoxF5TTS`
   - API: Uses `generate()` method
   - Benefits: Voice library integration, enhanced features

2. **Standard F5-TTS** (Fallback):
   - Package: `f5-tts` pip package
   - Implementation: `F5TTS` class
   - API: Uses `infer()` method
   - Benefits: Standard implementation, reliable

3. **Mock Generation** (Development):
   - Sine wave audio generation
   - Maintains file structure and timing
   - Useful for testing and development

## ComfyUI Integration Details

### Path Resolution

```python
# Automatic ComfyUI path detection
comfy_f5_path = Path("C:/Users/<USER>/Documents/ComfyUI/custom_nodes/chatterbox_srt_voice")
if comfy_f5_path.exists():
    # Add to Python path
    sys.path.insert(0, str(comfy_f5_path))
    # Import ChatterBoxF5TTS
    from engines.f5tts.f5tts import ChatterBoxF5TTS
```

### Model Architecture

Vid2Frames uses the **pointing architecture** rather than copying models:

```
Vid2Frames → Points to → ComfyUI Models
     │                        │
     ├─ No model duplication  ├─ ComfyUI/models/F5-TTS/
     ├─ Shared voice library  ├─ ComfyUI/models/chatterbox/
     └─ Efficient disk usage  └─ ComfyUI/models/voices/
```

**Benefits of Self-Contained Architecture:**
- **No External Dependencies**: Works without ComfyUI installation
- **Bundled Voice Library**: 10 professional voices included (~3.6MB)
- **Installer Ready**: Complete F5-TTS functionality in Windows installer
- **ComfyUI Compatibility**: Uses same voice format, can expand library from ComfyUI

### Voice Library Integration

Vid2Frames includes a self-contained voice library:

```
Vid2Frames/chatterbox_srt_voice/voices_examples/
├── Clint_Eastwood CC3 (enhanced2).wav
├── Clint_Eastwood CC3 (enhanced2).reference.txt
├── David_Attenborough CC3.wav
├── David_Attenborough CC3.reference.txt
├── Morgan_Freeman CC3.wav
├── Morgan_Freeman CC3.reference.txt
├── Sophie_Anderson CC3.wav
├── Sophie_Anderson CC3.reference.txt
├── female/
│   ├── female_01.wav
│   ├── female_01.reference.txt
│   ├── female_02.wav
│   └── female_02.reference.txt
└── male/
    ├── male_01.wav
    ├── male_01.reference.txt
    ├── male_02.wav
    └── male_02.reference.txt
```

**Voice Detection Logic:**
```python
def get_available_comfy_voices(self):
    """Discover voices from ComfyUI models/voices directory"""
    voices_dir = Path("C:/Users/<USER>/Documents/ComfyUI/models/voices")
    voices = []
    
    for audio_file in voices_dir.glob("*.wav"):
        # Look for reference text file
        ref_text_file = audio_file.with_suffix('.reference.txt')
        if not ref_text_file.exists():
            ref_text_file = audio_file.with_suffix('.txt')
        
        if ref_text_file.exists():
            voices.append({
                'name': audio_file.stem,
                'path': str(audio_file),
                'reference_text': ref_text_file.read_text('utf-8').strip()
            })
    
    return voices
```

## API Implementation

### ChatterBoxF5TTS API

When ComfyUI is available, Vid2Frames uses the ChatterBoxF5TTS API:

```python
def generate_speech_comfy(self, text: str, output_path: Path, params: Dict[str, Any]) -> bool:
    """Generate speech using ChatterBox F5-TTS implementation"""
    
    # Auto-select voice if none set
    if not hasattr(self, 'reference_audio_path'):
        voices = self.get_available_comfy_voices()
        if voices:
            self.reference_audio_path = voices[0]['path']
            self.reference_text = voices[0]['reference_text']
    
    # Generate using ChatterBoxF5TTS
    wav_tensor = self.model.generate(
        text=text,
        ref_audio_path=str(self.reference_audio_path),
        ref_text=self.reference_text,
        temperature=params.get('temperature', 0.8),
        speed=params.get('speed', 1.0),
        target_rms=params.get('target_rms', 0.1),
        cross_fade_duration=params.get('cross_fade_duration', 0.15),
        nfe_step=params.get('nfe_step', 32),
        cfg_strength=params.get('cfg_strength', 2.0)
    )
    
    # Save audio file
    torchaudio.save(str(output_path), wav_tensor.unsqueeze(0), 24000)
    return True
```

### Standard F5-TTS API (Fallback)

When ComfyUI is not available, Vid2Frames falls back to the standard F5-TTS API:

```python
def generate_speech_standard(self, text: str, output_path: Path, params: Dict[str, Any]) -> bool:
    """Generate speech using standard F5-TTS infer method"""
    
    # Use F5-TTS infer method (not generate)
    wav, sr, spec = self.model.infer(
        ref_file=self.reference_audio if self.reference_audio else None,
        ref_text=self.reference_text if self.reference_text else "",
        gen_text=text,
        file_wave=str(output_path),
        seed=params.get('seed', None),
        remove_silence=params.get('remove_silence', False)
    )
    
    return True
```

## Configuration and Parameters

### Default Parameters

Vid2Frames uses parameters optimized for quality and compatibility:

```python
self.default_params = {
    'model': 'E2TTS_Base',           # Compatible with ComfyUI
    'seed': 1193103530,              # Reproducible results
    'device': 'auto',                # Auto-detect GPU/CPU
    'temperature': 0.8,              # Balanced creativity
    'speed': 1.0,                    # Natural speech speed
    'target_rms': 0.1,               # Optimal audio level
    'nfe_step': 32,                  # Quality vs speed balance
    'cfg_strength': 2.0,             # Guidance strength
    'remove_silence': False,         # Preserve natural timing
    'control_after_generate': 'randomize'
}
```

### Model Compatibility

**Supported Models:**
- **E2TTS_Base**: Primary model, well-tested
- **F5TTS_Base**: Alternative base model  
- **F5TTS_Large**: High-quality, slower generation
- **F5TTS_v1_Base**: Version 1 compatibility

**Language Models:**
- **F5-DE**: German language support
- **F5-ES**: Spanish language support  
- **F5-FR**: French language support
- **F5-JP**: Japanese language support

## Error Handling and Fallbacks

### Graceful Degradation

```python
def generate_speech(self, text: str, output_path: Path, params: Optional[Dict] = None) -> bool:
    try:
        # Try ComfyUI implementation first
        if self.use_comfy_f5 and self.model:
            return self.generate_speech_comfy(text, output_path, params)
        
        # Fall back to standard F5-TTS
        elif self.model and hasattr(self.model, 'infer'):
            return self.generate_speech_standard(text, output_path, params)
        
    except Exception as e:
        print(f"❌ TTS generation failed: {e}")
        
    # Final fallback to mock generation
    self.generate_speech_mock(text, output_path, params)
    return True  # Always succeeds with mock
```

### Common Issues and Solutions

**Issue 1: "`F5TTS` object has no attribute 'generate'"**
- **Cause**: Using standard F5-TTS with old API call
- **Solution**: Use `infer()` method instead of `generate()`
- **Fixed**: Updated in current implementation

**Issue 2: "ComfyUI F5-TTS found but import failed"** 
- **Cause**: Missing dependencies in ComfyUI installation
- **Solution**: Ensure `f5_tts` package installed in ComfyUI environment
- **Mitigation**: Falls back to standard F5-TTS automatically

**Issue 3: "No reference audio set"**
- **Cause**: Voice not selected for generation
- **Solution**: Auto-selects first available ComfyUI voice
- **Enhancement**: Provides voice selection UI

## Performance Considerations

### GPU Compatibility

**RTX 5090 Support:**
- ✅ **Confirmed Working**: PyTorch 2.8.0 with CUDA support
- ✅ **Auto-Detection**: Automatically uses GPU when available
- ✅ **CPU Fallback**: Seamless fallback to CPU if GPU issues

```python
def __init__(self):
    # RTX 5090 compatibility confirmed
    gpu_available = torch.cuda.is_available()
    if gpu_available:
        try:
            torch.cuda.init()
            test_tensor = torch.tensor([1.0]).cuda()
            print(f"🔍 Detected GPU: {torch.cuda.get_device_name(0)}")
            self.device = "cuda"
        except Exception:
            self.device = "cpu"
```

### Memory Management

**Model Loading:**
- Models loaded once and cached
- GPU memory managed automatically
- CPU fallback for memory-constrained systems

**Batch Processing:**
- One-sentence-per-file approach prevents memory issues
- Background processing with progress updates
- Efficient file I/O with proper cleanup

## Integration Benefits

### For Users
- **No Additional Setup**: Works with existing ComfyUI installation
- **Voice Library Access**: Use ComfyUI voices directly
- **Quality Consistency**: Same models as ComfyUI workflows
- **Disk Efficiency**: No model duplication

### For Developers  
- **Modular Design**: Easy to extend and maintain
- **Fallback Architecture**: Robust error handling
- **Standard APIs**: Compatible with multiple F5-TTS implementations
- **Future-Proof**: Ready for new F5-TTS versions

## File Structure

```
src/core/f5_tts.py                    # Main F5-TTS processor
├── F5TTSProcessor                    # Main class
├── load_model()                      # Model loading logic  
├── generate_speech_comfy()           # ComfyUI integration
├── generate_speech_standard()        # Standard F5-TTS
├── generate_speech_mock()            # Development fallback
├── get_available_comfy_voices()      # Voice discovery
└── preprocess_text()                 # Text preparation

ComfyUI Integration Files:
C:/Users/<USER>/Documents/ComfyUI/custom_nodes/chatterbox_srt_voice/
├── engines/f5tts/f5tts.py           # ChatterBoxF5TTS implementation
├── models/voices/                    # Voice reference library
└── models/F5-TTS/                   # F5-TTS model files
```

## Future Enhancements

### Planned Features
- **Voice Cloning UI**: Built-in voice recording and setup
- **Model Management**: Download and manage F5-TTS models from UI
- **Advanced Preprocessing**: Enhanced text normalization and SSML support
- **Streaming Generation**: Real-time audio generation for long texts
- **Voice Mixing**: Combine multiple reference voices

### Compatibility Roadmap
- **ComfyUI v4.0+**: Updated API compatibility
- **F5-TTS v2.0**: Next-generation model support
- **Multi-Engine Support**: ChatterBox + F5-TTS + additional engines
- **Cloud Integration**: Remote model and voice library access

## Conclusion

The ComfyUI F5-TTS integration in Vid2Frames demonstrates a **pointing architecture** approach that:

1. **Leverages Existing Infrastructure**: Uses ComfyUI's F5-TTS implementation
2. **Provides Robust Fallbacks**: Multiple implementation layers  
3. **Optimizes Resources**: No model duplication, shared voice library
4. **Ensures Compatibility**: Works across different F5-TTS versions
5. **Maintains Performance**: GPU acceleration with CPU fallback

This architecture provides users with high-quality text-to-speech generation while maintaining efficiency and compatibility across different system configurations.

---

*This documentation covers the technical implementation of ComfyUI F5-TTS integration as of September 2025. For the latest updates, see the main README and release notes.*