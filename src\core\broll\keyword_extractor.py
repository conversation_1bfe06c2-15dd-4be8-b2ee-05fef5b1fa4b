"""
B-Roll keyword extraction module
Contextual keyword extraction for video B-roll search
"""

import re
from collections import Counter
from typing import List, Dict, Optional, Tuple

class BRollKeywordExtractor:
    """Production-ready keyword extractor for B-roll video search"""
    
    def __init__(self):
        # Curated visual keywords that work well for video search
        self.visual_keywords = {
            # Business/Office (highest priority for business content)
            "office", "business", "meeting", "professional", "workplace", "team",
            "collaboration", "conference", "presentation", "handshake", "discussion",
            
            # Technology 
            "computer", "laptop", "typing", "coding", "developer", "programming",
            "software", "tech", "digital", "screen", "app", "website",
            
            # Documentation
            "documents", "paperwork", "writing", "requirements", "analysis",
            "planning", "chart", "whiteboard", "notes",
            
            # Communication
            "phone", "email", "interview", "conversation", "communication",
            
            # People/Actions
            "people", "worker", "employee", "working", "thinking", "talking"
        }
        
        # Words to avoid (too abstract or poor video results)
        self.excluded_words = {
            "idea", "concept", "thought", "feeling", "method", "way", "thing", "process",
            "approach", "strategy", "solution", "problem", "issue", "mystery", "magic",
            "trick", "power", "superpower", "kicker", "part", "terms", "life", "here",
            "there", "because", "really", "just", "like", "what", "when", "where",
            "they", "them", "this", "that", "these", "those", "some", "many", "much",
            "very", "more", "most", "also", "even", "still", "again", "always", "never"
        }
        
        # Keyword clusters for semantic relationships
        self.clusters = {
            "office_business": ["office", "business", "professional", "workplace", "corporate"],
            "meetings": ["meeting", "discussion", "collaboration", "team", "conference"],
            "technology": ["computer", "laptop", "typing", "tech", "digital", "software"],
            "development": ["developer", "coding", "programming", "app"],
            "documentation": ["documents", "paperwork", "requirements", "analysis"],
            "communication": ["phone", "email", "interview", "conversation"]
        }
    
    def extract_keywords(self, sentences: List[str]) -> Dict[int, List[str]]:
        """
        Extract contextual keywords for B-roll video search
        
        Args:
            sentences: List of text sentences
            
        Returns:
            Dictionary mapping sentence index to list of 2-3 keywords
        """
        if not sentences:
            return {}
        
        # Analyze overall context
        context = self._analyze_context(sentences)
        
        # Generate keywords for each sentence
        results = {}
        previous_keywords = []
        
        for i, sentence in enumerate(sentences):
            keywords = self._extract_sentence_keywords(
                sentence, context, previous_keywords
            )
            results[i] = keywords
            previous_keywords = keywords
        
        return results
    
    def _analyze_context(self, sentences: List[str]) -> Dict:
        """Analyze the overall context of all sentences"""
        full_text = " ".join(sentences).lower()
        
        # Detect domain
        domain = self._detect_domain(full_text)
        
        # Extract key themes
        words = re.findall(r'\b[a-z]{3,}\b', full_text)
        word_freq = Counter(words)
        themes = [word for word, count in word_freq.most_common(20) 
                 if word in self.visual_keywords and count > 1]
        
        return {
            'domain': domain,
            'themes': themes,
            'preferences': self._get_domain_preferences(domain)
        }
    
    def _detect_domain(self, text: str) -> str:
        """Detect the primary domain of the content"""
        domain_keywords = {
            'business': ['business', 'analyst', 'stakeholder', 'requirement', 'meeting', 'office'],
            'technology': ['tech', 'app', 'software', 'developer', 'code', 'computer'],
            'education': ['explain', 'teach', 'learn', 'example', 'understand'],
            'general': ['people', 'work', 'professional']
        }
        
        scores = {}
        for domain, keywords in domain_keywords.items():
            scores[domain] = sum(text.count(keyword) for keyword in keywords)
        
        return max(scores, key=scores.get)
    
    def _get_domain_preferences(self, domain: str) -> List[str]:
        """Get preferred keywords for the domain"""
        preferences = {
            'business': ['office', 'meeting', 'professional', 'business', 'team'],
            'technology': ['computer', 'developer', 'tech', 'programming', 'software'],
            'education': ['people', 'presentation', 'professional', 'discussion'],
            'general': ['office', 'professional', 'people', 'working']
        }
        return preferences.get(domain, preferences['general'])
    
    def _extract_sentence_keywords(self, sentence: str, context: Dict, 
                                  previous_keywords: List[str]) -> List[str]:
        """Extract keywords for a specific sentence"""
        sentence_lower = sentence.lower().strip()
        
        # Extract candidate words
        words = re.findall(r'\b[a-z]{3,}\b', sentence_lower)
        
        # Score candidates
        candidates = []
        for word in words:
            if self._is_valid_keyword(word):
                score = self._calculate_score(word, sentence_lower, context, previous_keywords)
                candidates.append((word, score))
        
        # Add contextual keywords
        contextual = self._get_contextual_keywords(sentence_lower, context)
        for keyword, score in contextual:
            if keyword not in [c[0] for c in candidates]:
                candidates.append((keyword, score))
        
        # Sort by score and select top keywords
        candidates.sort(key=lambda x: x[1], reverse=True)
        
        # Select 2-3 diverse keywords
        selected = self._select_diverse_keywords(candidates, previous_keywords)
        
        # Ensure minimum fallback
        if not selected:
            selected = context['preferences'][:2]
        
        return selected[:3]
    
    def _is_valid_keyword(self, word: str) -> bool:
        """Check if word is a valid keyword for video search"""
        return (word in self.visual_keywords and 
                word not in self.excluded_words and
                len(word) > 2)
    
    def _calculate_score(self, word: str, sentence: str, context: Dict, 
                        previous_keywords: List[str]) -> float:
        """Calculate keyword relevance score"""
        score = 1.0
        
        # Base visual keyword score
        if word in self.visual_keywords:
            score += 2.0
        
        # Domain preference boost
        if word in context['preferences']:
            score += 3.0
        
        # Theme relevance
        if word in context['themes']:
            score += 1.5
        
        # Continuity with previous sentence
        if any(self._are_related(word, prev) for prev in previous_keywords):
            score += 1.0
        
        # Context-specific boosts
        score += self._get_context_boost(word, sentence)
        
        return score
    
    def _get_context_boost(self, word: str, sentence: str) -> float:
        """Get additional score based on sentence context"""
        boost = 0.0
        
        context_patterns = {
            'meeting': ['sit', 'discuss', 'argue', 'stakeholder', 'interview'],
            'computer': ['type', 'code', 'laptop', 'screen', 'digital'],
            'documents': ['write', 'document', 'requirement', 'paperwork'],
            'developer': ['code', 'tech', 'app', 'software', 'build'],
            'office': ['work', 'professional', 'corporate', 'workplace']
        }
        
        if word in context_patterns:
            patterns = context_patterns[word]
            if any(pattern in sentence for pattern in patterns):
                boost += 1.0
        
        return boost
    
    def _get_contextual_keywords(self, sentence: str, context: Dict) -> List[Tuple[str, float]]:
        """Get keywords based on sentence context"""
        keywords = []
        domain = context['domain']
        
        if domain == 'business':
            if any(word in sentence for word in ['meeting', 'stakeholder', 'discuss']):
                keywords.append(('meeting', 3.5))
            if any(word in sentence for word in ['document', 'requirement', 'write']):
                keywords.append(('documents', 3.0))
            if any(word in sentence for word in ['developer', 'tech', 'app']):
                keywords.append(('developer', 3.0))
        
        return keywords
    
    def _are_related(self, word1: str, word2: str) -> bool:
        """Check if two words are semantically related"""
        for cluster_words in self.clusters.values():
            if word1 in cluster_words and word2 in cluster_words:
                return True
        return False
    
    def _select_diverse_keywords(self, candidates: List[Tuple[str, float]], 
                                previous_keywords: List[str]) -> List[str]:
        """Select diverse keywords from candidates"""
        if not candidates:
            return []
        
        selected = []
        used_clusters = set()
        
        # First pass: select high-scoring diverse keywords
        for word, score in candidates[:8]:  # Consider top candidates
            if len(selected) >= 3:
                break
            
            cluster = self._get_cluster(word)
            if cluster not in used_clusters or len(selected) == 0:
                selected.append(word)
                if cluster:
                    used_clusters.add(cluster)
        
        # Second pass: ensure we have at least 2 keywords
        if len(selected) < 2 and candidates:
            for word, score in candidates:
                if word not in selected:
                    selected.append(word)
                    if len(selected) >= 2:
                        break
        
        return selected
    
    def _get_cluster(self, word: str) -> Optional[str]:
        """Get the cluster name for a word"""
        for cluster_name, cluster_words in self.clusters.items():
            if word in cluster_words:
                return cluster_name
        return None