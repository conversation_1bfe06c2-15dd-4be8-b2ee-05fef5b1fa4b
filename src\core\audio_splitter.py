"""
Audio splitter using Whisper transcription and text alignment
"""
import os
import tempfile
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Callable, Optional, Tuple
import json
from difflib import SequenceMatcher
import re
from datetime import datetime

from ..utils.config import config

try:
    import torch
    import whisper
    import librosa
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False


class AudioSegment:
    """Represents a segment of audio with text alignment"""
    def __init__(self, start_time: float, end_time: float, text: str, confidence: float = 0.0):
        self.start_time = start_time
        self.end_time = end_time
        self.text = text
        self.confidence = confidence
        self.duration = end_time - start_time
    
    def __str__(self):
        return f"[{self.start_time:.2f}s - {self.end_time:.2f}s]: {self.text}"


class AudioSplitter:
    """Handles audio splitting using Whisper transcription and text alignment"""
    
    def __init__(self):
        self.whisper_model = None
        
        # Load settings from config
        self.similarity_threshold = config.processing.audio_similarity_threshold
        self.output_format = config.processing.audio_output_format
        self.buffer_ms = config.processing.audio_buffer_ms
        self.device = self._select_device()
        
        if not AUDIO_PROCESSING_AVAILABLE:
            raise ImportError("Audio processing libraries are required (torch, whisper, librosa, soundfile)")
    
    def _select_device(self) -> str:
        """Select the best available device for transcription"""
        if not torch.cuda.is_available():
            print("🖥️  CUDA not available, using CPU for audio processing")
            return "cpu"
            
        try:
            # Test GPU functionality
            gpu_name = torch.cuda.get_device_name()
            print(f"🔍 Detected GPU: {gpu_name}")
            
            test_tensor = torch.randn(100, 100, device='cuda')
            result = torch.matmul(test_tensor, test_tensor)
            result.cpu()
            del test_tensor, result
            torch.cuda.empty_cache()
            
            print("✅ GPU test successful - using CUDA for audio processing")
            return "cuda"
            
        except Exception as e:
            print(f"❌ GPU error: {e}")
            print("🔄 Falling back to CPU for audio processing")
            return "cpu"
    
    def load_whisper_model(self, model_size: str = "base") -> bool:
        """Load Whisper model for transcription"""
        try:
            print(f"Loading Whisper {model_size} model on {self.device}...")
            self.whisper_model = whisper.load_model(model_size, device=self.device)
            print(f"✅ Whisper model loaded successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to load Whisper model: {e}")
            return False
    
    def transcribe_audio_with_timestamps(self, audio_path: Path) -> List[Dict]:
        """Transcribe audio file and return segments with timestamps"""
        if not self.whisper_model:
            if not self.load_whisper_model():
                raise Exception("Failed to load Whisper model")
        
        try:
            print(f"Transcribing audio: {audio_path.name}")
            
            # Use Whisper to transcribe with word-level timestamps
            result = self.whisper_model.transcribe(
                str(audio_path), 
                word_timestamps=True,
                verbose=False
            )
            
            segments = []
            for segment in result["segments"]:
                # Extract word-level information if available
                words = segment.get("words", [])
                segments.append({
                    "start": segment["start"],
                    "end": segment["end"], 
                    "text": segment["text"].strip(),
                    "words": words
                })
            
            print(f"✅ Transcription complete: {len(segments)} segments found")
            return segments
            
        except Exception as e:
            print(f"❌ Transcription failed: {e}")
            raise
    
    def align_text_with_transcription(self, transcription_segments: List[Dict], 
                                    target_sentences: List[str], 
                                    similarity_threshold: float = 0.6) -> List[AudioSegment]:
        """Align target sentences with transcribed segments using fuzzy matching"""
        
        print(f"Aligning {len(target_sentences)} sentences with {len(transcription_segments)} transcribed segments")
        
        # Combine all transcription text
        full_transcription = " ".join([seg["text"] for seg in transcription_segments])
        
        aligned_segments = []
        used_segments = set()
        
        for i, sentence in enumerate(target_sentences):
            print(f"Processing sentence {i+1}/{len(target_sentences)}: '{sentence[:50]}...'")
            
            best_match = None
            best_similarity = 0
            best_segment_range = None
            
            # Try to find the best matching segment(s) for this sentence
            for start_idx in range(len(transcription_segments)):
                if start_idx in used_segments:
                    continue
                    
                # Try different combinations of consecutive segments
                for end_idx in range(start_idx, min(start_idx + 5, len(transcription_segments))):
                    if any(idx in used_segments for idx in range(start_idx, end_idx + 1)):
                        continue
                    
                    # Combine segments in this range
                    combined_text = " ".join([
                        transcription_segments[idx]["text"] 
                        for idx in range(start_idx, end_idx + 1)
                    ])
                    
                    # Calculate similarity
                    similarity = self._calculate_text_similarity(sentence, combined_text)
                    
                    if similarity > best_similarity and similarity >= similarity_threshold:
                        best_similarity = similarity
                        best_match = combined_text
                        best_segment_range = (start_idx, end_idx)
            
            if best_match and best_segment_range:
                # Mark segments as used
                for idx in range(best_segment_range[0], best_segment_range[1] + 1):
                    used_segments.add(idx)
                
                # Get timing from the segment range
                start_time = transcription_segments[best_segment_range[0]]["start"]
                end_time = transcription_segments[best_segment_range[1]]["end"]
                
                aligned_segments.append(AudioSegment(
                    start_time=start_time,
                    end_time=end_time,
                    text=sentence,
                    confidence=best_similarity
                ))
                
                print(f"  ✅ Matched with similarity {best_similarity:.2f}: {start_time:.1f}s - {end_time:.1f}s")
            else:
                # No good match found - this could mean the sentence isn't in the audio
                print(f"  ⚠️ No match found for sentence (similarity < {similarity_threshold})")
                # Create a placeholder segment - we could skip this or estimate timing
                if i > 0 and aligned_segments:
                    # Estimate timing based on previous segments
                    prev_segment = aligned_segments[-1]
                    estimated_start = prev_segment.end_time
                    estimated_duration = 3.0  # Assume 3 seconds for missing segments
                    estimated_end = estimated_start + estimated_duration
                    
                    aligned_segments.append(AudioSegment(
                        start_time=estimated_start,
                        end_time=estimated_end,
                        text=sentence,
                        confidence=0.0  # Low confidence for estimated segments
                    ))
        
        print(f"✅ Alignment complete: {len(aligned_segments)} segments aligned")
        return aligned_segments
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings"""
        # Clean and normalize text
        clean1 = re.sub(r'[^\w\s]', '', text1.lower().strip())
        clean2 = re.sub(r'[^\w\s]', '', text2.lower().strip())
        
        # Use SequenceMatcher for similarity
        similarity = SequenceMatcher(None, clean1, clean2).ratio()
        return similarity
    
    def extract_audio_segment(self, audio_path: Path, start_time: float, end_time: float, 
                            output_path: Path, output_format: str = "wav", 
                            buffer_ms: float = 100) -> bool:
        """Extract a segment of audio using FFmpeg with optional buffer padding"""
        try:
            # Add buffer (convert milliseconds to seconds)
            buffer_seconds = buffer_ms / 1000.0
            buffered_start = max(0, start_time - buffer_seconds)  # Don't go below 0
            buffered_end = end_time + buffer_seconds
            
            # Build FFmpeg command
            cmd = [
                "ffmpeg", "-y",  # -y to overwrite existing files
                "-i", str(audio_path),
                "-ss", str(buffered_start),  # Start time with buffer
                "-t", str(buffered_end - buffered_start),  # Duration with buffer
            ]
            
            # Set codec based on output format
            if output_format == "wav":
                cmd.extend(["-acodec", "pcm_s16le"])
            elif output_format == "flac":
                cmd.extend(["-acodec", "flac"])
            elif output_format == "mp3":
                cmd.extend(["-acodec", "libmp3lame"])
            else:
                # Default to wav for unknown formats
                cmd.extend(["-acodec", "pcm_s16le"])
            
            cmd.append(str(output_path))
            
            # Run FFmpeg
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"FFmpeg error: {result.stderr}")
                return False
                
            return output_path.exists() and output_path.stat().st_size > 0
            
        except Exception as e:
            print(f"Error extracting audio segment: {e}")
            return False
    
    def split_audio_by_text_alignment(self, audio_path: Path, sentences: List[str], 
                                    config: Dict[str, Any], 
                                    progress_callback: Optional[Callable] = None) -> bool:
        """Split audio file into segments based on text alignment"""
        
        try:
            output_dir = config["output_dir"]
            output_format = config.get("output_format", "wav")
            similarity_threshold = config.get("similarity_threshold", 0.6)
            buffer_ms = config.get("buffer_ms", 100)  # Default 100ms buffer
            
            total_steps = 4 + len(sentences)  # Transcription + alignment + extraction steps
            current_step = 0
            
            # Step 1: Transcribe audio with timestamps
            if progress_callback:
                progress_callback("Transcribing audio with Whisper...", current_step / total_steps)
            
            transcription_segments = self.transcribe_audio_with_timestamps(audio_path)
            current_step += 1
            
            # Step 2: Align text with transcription
            if progress_callback:
                progress_callback("Aligning text with transcription...", current_step / total_steps)
            
            aligned_segments = self.align_text_with_transcription(
                transcription_segments, sentences, similarity_threshold
            )
            current_step += 1
            
            # Step 3: Validate segments
            if progress_callback:
                progress_callback("Validating segments...", current_step / total_steps)
            
            if not aligned_segments:
                raise Exception("No segments could be aligned with the provided text")
            
            # Filter out segments with very low confidence if desired
            valid_segments = [seg for seg in aligned_segments if seg.confidence > 0.3]
            if len(valid_segments) < len(aligned_segments) * 0.5:
                print("⚠️ Many segments have low confidence - results may not be accurate")
            
            current_step += 1
            
            # Step 4: Create output directory structure
            if progress_callback:
                progress_callback("Preparing output directory...", current_step / total_steps)
            
            output_dir.mkdir(exist_ok=True)
            
            # Save alignment information
            alignment_info = []
            for i, segment in enumerate(aligned_segments):
                alignment_info.append({
                    "segment_number": i + 1,
                    "start_time": segment.start_time,
                    "end_time": segment.end_time,
                    "duration": segment.duration,
                    "text": segment.text,
                    "confidence": segment.confidence
                })
            
            with open(output_dir / "alignment_info.json", 'w', encoding='utf-8') as f:
                json.dump(alignment_info, f, indent=2, ensure_ascii=False)
            
            current_step += 1
            
            # Step 5: Extract audio segments
            successful_extractions = 0
            
            for i, segment in enumerate(aligned_segments):
                if progress_callback:
                    progress_callback(
                        f"Extracting segment {i+1}/{len(aligned_segments)}: '{segment.text[:30]}...'",
                        (current_step + i) / total_steps
                    )
                
                # Generate output filename
                # Sanitize filename
                safe_text = re.sub(r'[^\w\s-]', '', segment.text[:50]).strip()
                safe_text = re.sub(r'\s+', '_', safe_text)
                
                output_filename = f"segment_{i+1:03d}_{safe_text}.{output_format}"
                output_path = output_dir / output_filename
                
                # Extract segment with configurable buffer
                success = self.extract_audio_segment(
                    audio_path, segment.start_time, segment.end_time, 
                    output_path, output_format, buffer_ms=buffer_ms
                )
                
                if success:
                    successful_extractions += 1
                    print(f"  ✅ Extracted: {output_path.name}")
                else:
                    print(f"  ❌ Failed to extract: {output_path.name}")
            
            # Final progress update
            if progress_callback:
                progress_callback("Audio splitting complete!", 1.0)
            
            print(f"✅ Audio splitting complete!")
            print(f"   Successfully extracted: {successful_extractions}/{len(aligned_segments)} segments")
            print(f"   Output directory: {output_dir}")
            
            return successful_extractions > 0
            
        except Exception as e:
            if progress_callback:
                progress_callback(f"Error: {str(e)}", 1.0)
            print(f"❌ Audio splitting failed: {e}")
            raise
    
    def update_settings(self, buffer_ms: int = None, similarity_threshold: float = None, output_format: str = None):
        """Update audio splitting settings and save to config"""
        if buffer_ms is not None:
            self.buffer_ms = buffer_ms
            config.processing.audio_buffer_ms = buffer_ms
        
        if similarity_threshold is not None:
            self.similarity_threshold = similarity_threshold
            config.processing.audio_similarity_threshold = similarity_threshold
        
        if output_format is not None:
            self.output_format = output_format
            config.processing.audio_output_format = output_format
        
        # Save settings to database
        config.save_settings()
    
    def cleanup(self):
        """Cleanup resources"""
        if self.whisper_model and hasattr(self.whisper_model, 'device') and self.device == "cuda":
            torch.cuda.empty_cache()


# Convenience function
def split_audio_file(audio_path: Path, text_file_path: Path, output_dir: Path, 
                    similarity_threshold: float = 0.6, output_format: str = "wav",
                    buffer_ms: float = 100, progress_callback: Optional[Callable] = None) -> bool:
    """
    Convenience function to split audio file using text alignment
    
    Args:
        audio_path: Path to the audio file
        text_file_path: Path to the text file (one sentence per line)
        output_dir: Directory to save the split audio files
        similarity_threshold: Threshold for text-audio alignment (0.0 to 1.0)
        output_format: Output audio format ("wav", "mp3", "flac")
        buffer_ms: Buffer duration in milliseconds to add at start/end of each segment
        progress_callback: Optional callback for progress updates
    
    Returns:
        True if splitting was successful, False otherwise
    """
    
    if not AUDIO_PROCESSING_AVAILABLE:
        print("Audio processing libraries are not available")
        return False
    
    try:
        # Read sentences from text file
        with open(text_file_path, 'r', encoding='utf-8') as f:
            sentences = [line.strip() for line in f.readlines() if line.strip()]
        
        if not sentences:
            print("No sentences found in text file")
            return False
        
        # Create splitter and process
        splitter = AudioSplitter()
        
        config = {
            'output_dir': output_dir,
            'similarity_threshold': similarity_threshold,
            'output_format': output_format,
            'buffer_ms': buffer_ms
        }
        
        return splitter.split_audio_by_text_alignment(
            audio_path, sentences, config, progress_callback
        )
        
    except Exception as e:
        print(f"Error in split_audio_file: {e}")
        return False