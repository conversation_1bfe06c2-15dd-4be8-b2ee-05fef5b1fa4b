"""
Test script for F5-TTS functionality
"""

from pathlib import Path
import sys

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.f5_tts import F5TTSProcessor

def test_f5_tts_processor():
    """Test the F5TTSProcessor"""
    print("🧪 Testing F5TTSProcessor...")
    
    processor = F5TTSProcessor()
    
    # Test model loading (mock)
    print("Loading model...")
    processor.load_model()
    print(f"Model loaded: {processor.model_loaded}")
    
    # Test sentence processing
    test_sentences = [
        "Hello, this is a test sentence.",
        "This is the second sentence for testing.",
        "And this is the final test sentence."
    ]
    
    output_dir = Path("test_f5_tts_output")
    output_dir.mkdir(exist_ok=True)
    
    print(f"\n🎯 Processing {len(test_sentences)} sentences...")
    results = processor.process_sentences(
        sentences=test_sentences,
        output_dir=output_dir,
        progress_callback=lambda msg, progress: print(f"  📊 {progress:.1%}: {msg}")
    )
    
    print(f"\n✅ Results:")
    print(f"  Success: {results['success_count']}")
    print(f"  Failed: {results['failed_count']}")
    print(f"  Output files: {len(results['output_files'])}")
    
    for file_path in results['output_files']:
        print(f"    📁 {file_path}")
    
    if results['errors']:
        print(f"  ❌ Errors:")
        for error in results['errors']:
            print(f"    {error}")

def test_available_models():
    """Test getting available models"""
    print("\n🔍 Available F5-TTS models:")
    processor = F5TTSProcessor()
    models = processor.get_available_models()
    
    for model in models:
        print(f"  📋 {model}")

def test_parameter_validation():
    """Test parameter validation"""
    print("\n🔧 Testing parameter validation...")
    processor = F5TTSProcessor()
    
    test_params = {
        'model': 'F5TTS_Base',
        'seed': '123456',
        'temperature': '0.9',
        'speed': '1.2',
        'invalid_param': 'should_be_ignored'
    }
    
    validated = processor.validate_parameters(test_params)
    print(f"  Input params: {test_params}")
    print(f"  Validated params: {validated}")

if __name__ == "__main__":
    print("🚀 F5-TTS Test Suite")
    print("=" * 50)
    
    try:
        test_f5_tts_processor()
        test_available_models()
        test_parameter_validation()
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()