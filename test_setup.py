"""
Simple test runner for the Vid2Frames application
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_imports():
    """Test that all imports work correctly"""
    try:
        import flet as ft
        print("✓ Flet imported successfully")
        
        import cv2
        print("✓ OpenCV imported successfully")
        
        from src.ui.main_window import MainWindow
        print("✓ MainWindow imported successfully")
        
        from src.core.video_processor import VideoProcessor
        print("✓ VideoProcessor imported successfully")
        
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_video_processor():
    """Test basic video processor functionality"""
    try:
        from src.core.video_processor import VideoProcessor
        
        processor = VideoProcessor()
        print("✓ VideoProcessor instantiated successfully")
        
        # Test similarity calculation with dummy frames
        import numpy as np
        frame1 = np.zeros((100, 100, 3), dtype=np.uint8)
        frame2 = np.zeros((100, 100, 3), dtype=np.uint8)
        
        similarity = processor._calculate_similarity(frame1, frame2)
        print(f"✓ Similarity calculation works: {similarity}")
        
        return True
    except Exception as e:
        print(f"✗ VideoProcessor error: {e}")
        return False

if __name__ == "__main__":
    print("Running Vid2Frames tests...\n")
    
    tests_passed = 0
    total_tests = 2
    
    if test_imports():
        tests_passed += 1
    
    if test_video_processor():
        tests_passed += 1
    
    print(f"\nTests completed: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Ready to run the application.")
    else:
        print("❌ Some tests failed. Check the errors above.")