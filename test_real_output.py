"""
Test real B-roll and audio indexing with actual files
"""
import os
import sys
sys.path.append('src')

def check_real_output():
    # Check the actual output from the most recent run
    base_dir = "f5_tts_output_20250928_235454"
    
    if not os.path.exists(base_dir):
        print(f"Directory {base_dir} doesn't exist")
        return
    
    # Check audio files
    audio_dir = os.path.join(base_dir, "individual_files")
    if os.path.exists(audio_dir):
        audio_files = sorted([f for f in os.listdir(audio_dir) if f.endswith('.wav')])
        print(f"Audio files found: {len(audio_files)}")
        for i, audio_file in enumerate(audio_files[:5]):  # Show first 5
            print(f"  {i:3d}: {audio_file}")
        if len(audio_files) > 5:
            print(f"  ... and {len(audio_files) - 5} more")
    else:
        print("No audio directory found")
    
    # Check combined videos
    video_dir = os.path.join(base_dir, "combined_videos")
    if os.path.exists(video_dir):
        video_files = sorted([f for f in os.listdir(video_dir) if f.endswith('.mp4')])
        print(f"\nCombined videos found: {len(video_files)}")
        for i, video_file in enumerate(video_files[:5]):  # Show first 5
            print(f"  {i:3d}: {video_file}")
        if len(video_files) > 5:
            print(f"  ... and {len(video_files) - 5} more")
            
        # Check if the names align
        print(f"\nChecking name alignment:")
        for i in range(min(5, len(audio_files), len(video_files))):
            audio_name = audio_files[i]
            video_name = video_files[i]
            
            # Extract the number prefix
            audio_num = audio_name.split('_')[0]
            video_num = video_name.split('_')[0]
            
            print(f"  {i:3d}: Audio {audio_num} vs Video {video_num} - {'✅' if audio_num == video_num else '❌'}")
            
    else:
        print("No combined videos directory found")

if __name__ == "__main__":
    check_real_output()