"""
File manager for handling video files and extracted frames
"""
import os
import shutil
from pathlib import Path
from typing import List, Optional, Tuple
import hashlib
import json
from datetime import datetime

from .config import config


class FileManager:
    """Manages file operations for video processing and frame storage"""
    
    SUPPORTED_VIDEO_FORMATS = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm', '.m4v'}
    SUPPORTED_IMAGE_FORMATS = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'}
    
    def __init__(self):
        self.output_dir = config.get_output_directory()
        self.temp_dir = config.get_temp_directory()
    
    def is_video_file(self, file_path: Path) -> bool:
        """Check if file is a supported video format"""
        return file_path.suffix.lower() in self.SUPPORTED_VIDEO_FORMATS
    
    def validate_video_file(self, file_path: Path) -> Tuple[bool, str]:
        """Validate video file for processing"""
        try:
            if not file_path.exists():
                return False, "File does not exist"
            
            if not self.is_video_file(file_path):
                return False, f"Unsupported format. Supported: {', '.join(self.SUPPORTED_VIDEO_FORMATS)}"
            
            # Check file size
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            if file_size_mb > config.application.max_file_size_mb:
                return False, f"File too large ({file_size_mb:.1f}MB). Max: {config.application.max_file_size_mb}MB"
            
            return True, "File is valid"
            
        except Exception as e:
            return False, f"Error validating file: {str(e)}"
    
    def get_file_hash(self, file_path: Path) -> str:
        """Generate hash for file identification"""
        hasher = hashlib.md5()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        return hasher.hexdigest()
    
    def create_job_directory(self, video_path: Path) -> Path:
        """Create directory for a video processing job"""
        # Create a unique directory name based on video file and timestamp
        video_hash = self.get_file_hash(video_path)[:8]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        job_name = f"{video_path.stem}_{timestamp}_{video_hash}"
        
        job_dir = self.output_dir / job_name
        job_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        (job_dir / "frames").mkdir(exist_ok=True)
        (job_dir / "metadata").mkdir(exist_ok=True)
        
        return job_dir
    
    def save_frame(self, frame_data, job_dir: Path, frame_index: int, 
                   timestamp: float, format: str = "PNG") -> Path:
        """Save a frame to the job directory"""
        import cv2
        from PIL import Image
        
        frame_filename = f"frame_{frame_index:06d}_{timestamp:.3f}s.{format.lower()}"
        frame_path = job_dir / "frames" / frame_filename
        
        # Convert BGR to RGB (OpenCV uses BGR, but PIL/display expects RGB)
        if len(frame_data.shape) == 3:
            frame_rgb = cv2.cvtColor(frame_data, cv2.COLOR_BGR2RGB)
        else:
            frame_rgb = frame_data
        
        # Use PIL to save the image in correct RGB format
        try:
            pil_image = Image.fromarray(frame_rgb)
            pil_image.save(str(frame_path), format=format.upper())
        except Exception as e:
            raise Exception(f"Failed to save frame to {frame_path}: {e}")
        
        return frame_path
    
    def save_job_metadata(self, job_dir: Path, metadata: dict):
        """Save job metadata to JSON file"""
        metadata_file = job_dir / "metadata" / "job_info.json"
        
        # Add system info
        metadata.update({
            'created_at': datetime.now().isoformat(),
            'output_directory': str(job_dir),
            'vid2frames_version': '1.0.0',  # TODO: Get from package info
            'settings': {
                'processing': {
                    'similarity_threshold': config.processing.similarity_threshold,
                    'quality_threshold': config.processing.quality_threshold,
                    'output_format': config.processing.output_format,
                },
                'application': {
                    'max_worker_threads': config.application.max_worker_threads,
                }
            }
        })
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
    
    def load_job_metadata(self, job_dir: Path) -> Optional[dict]:
        """Load job metadata from JSON file"""
        metadata_file = job_dir / "metadata" / "job_info.json"
        
        try:
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except (json.JSONDecodeError, IOError):
            pass
        
        return None
    
    def get_job_directories(self) -> List[Path]:
        """Get all job directories in output folder"""
        job_dirs = []
        
        for item in self.output_dir.iterdir():
            if item.is_dir() and (item / "metadata" / "job_info.json").exists():
                job_dirs.append(item)
        
        # Sort by creation time (newest first)
        job_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        return job_dirs
    
    def get_frame_files(self, job_dir: Path) -> List[Path]:
        """Get all frame files from a job directory"""
        frames_dir = job_dir / "frames"
        if not frames_dir.exists():
            return []
        
        frame_files = []
        for format_ext in self.SUPPORTED_IMAGE_FORMATS:
            frame_files.extend(frames_dir.glob(f"*{format_ext}"))
        
        # Sort by filename (which includes frame index)
        frame_files.sort(key=lambda x: x.name)
        return frame_files
    
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        if self.temp_dir.exists():
            try:
                shutil.rmtree(self.temp_dir)
                self.temp_dir.mkdir(exist_ok=True)
            except OSError:
                pass  # Ignore cleanup errors
    
    def delete_job(self, job_dir: Path) -> bool:
        """Delete a job directory and all its contents"""
        try:
            if job_dir.exists() and job_dir.parent == self.output_dir:
                shutil.rmtree(job_dir)
                return True
        except OSError:
            pass
        return False
    
    def export_frames_as_zip(self, job_dir: Path, zip_path: Path, 
                            frame_indices: Optional[List[int]] = None) -> bool:
        """Export selected frames as a ZIP archive"""
        import zipfile
        
        try:
            frame_files = self.get_frame_files(job_dir)
            
            if frame_indices is None:
                files_to_zip = frame_files
            else:
                files_to_zip = [frame_files[i] for i in frame_indices if i < len(frame_files)]
            
            if not files_to_zip:
                return False
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
                for i, frame_file in enumerate(files_to_zip):
                    # Create a clean archive name
                    arcname = f"{i+1:03d}_{frame_file.name}"
                    zipf.write(frame_file, arcname)
            
            return True
            
        except Exception:
            return False
    
    def export_frames(self, job_dir: Path, export_path: Path, 
                      frame_indices: Optional[List[int]] = None) -> bool:
        """Export selected frames to a different location"""
        try:
            export_path.mkdir(exist_ok=True)
            frame_files = self.get_frame_files(job_dir)
            
            if frame_indices is None:
                files_to_copy = frame_files
            else:
                files_to_copy = [frame_files[i] for i in frame_indices if i < len(frame_files)]
            
            for frame_file in files_to_copy:
                shutil.copy2(frame_file, export_path / frame_file.name)
            
            return True
            
        except Exception:
            return False
    
    def get_disk_usage(self) -> dict:
        """Get disk usage information for output directory"""
        try:
            total_size = 0
            file_count = 0
            
            for job_dir in self.get_job_directories():
                for frame_file in self.get_frame_files(job_dir):
                    total_size += frame_file.stat().st_size
                    file_count += 1
            
            return {
                'total_size_mb': total_size / (1024 * 1024),
                'file_count': file_count,
                'job_count': len(self.get_job_directories()),
                'output_directory': str(self.output_dir)
            }
            
        except Exception:
            return {
                'total_size_mb': 0,
                'file_count': 0,
                'job_count': 0,
                'output_directory': str(self.output_dir)
            }