# Vid2Frames - Tab Functionality Requirements

This document outlines the functional requirements for each tab in the Vid2Frames application, focusing on **what** each tab does rather than **how** it does it.

## Overview

Vid2Frames is a cross-platform desktop application with four main functional tabs. Each tab provides a complete, self-contained workflow including input, processing, progress tracking, and results display.

---

## 1. Video Frames/Scenes/Audio Tab

### Primary Purpose
Complete video processing workflow including frame extraction, scene detection, and audio transcription with integrated progress tracking and results display.

### Functional Requirements

#### File Selection
- **MUST** accept video files in multiple formats: MP4, AVI, MOV, MKV, FLV, WMV, WebM, M4V
- **MUST** provide drag-and-drop interface for file selection
- **MUST** provide browse dialog for file selection
- **MUST** validate file format before acceptance
- **MUST** display file information once selected (filename, duration, resolution, FPS, file size)

#### File Validation
- **MUST** verify file exists and is readable
- **MUST** verify file is a valid video format
- **MUST** display clear error messages for invalid files
- **MUST** prevent processing of invalid files

#### Processing Execution
- **MUST** enable "Start Processing" button only when valid file is selected
- **MUST** perform frame extraction with configurable similarity thresholds
- **MUST** detect and split video into scenes when enabled
- **MUST** extract and transcribe audio content when enabled
- **MUST** apply quality filtering and deduplication to extracted frames
- **MUST** create organized output directories with timestamps

#### Integrated Progress Tracking
- **MUST** display real-time progress within the tab during processing
- **MUST** show current processing step (loading, extracting, analyzing, saving)
- **MUST** provide progress bar with completion percentage
- **MUST** display processing speed and estimated time remaining
- **MUST** allow cancellation of ongoing processing
- **MUST** show processing warnings and errors inline

#### Results Display
- **MUST** display extracted frames in organized grid layout within the tab
- **MUST** show frame metadata (timestamp, similarity score, quality metrics)
- **MUST** display scene video files with playback controls
- **MUST** show audio transcription results with timing information
- **MUST** provide summary statistics (total frames, scenes, processing time)
- **MUST** enable direct access to output folders from results area

#### User Interface States
- **MUST** show upload area when no file is selected
- **MUST** show file details and processing controls when file is selected
- **MUST** show progress indicators during processing
- **MUST** show results area after processing completion
- **MUST** allow user to start new processing or change settings
- **MUST** provide clear visual feedback for all workflow states

---

## 2. Audio Split Tab

### Primary Purpose
Split audio files into individual segments using text alignment and AI transcription.

### Functional Requirements

#### File Input Management
- **MUST** accept audio files in formats: MP3, WAV, M4A, FLAC, OGG, AAC, WMA
- **MUST** accept text files (.TXT) with one sentence per line
- **MUST** validate both audio and text files before processing
- **MUST** require both files to be selected before enabling processing
- **MUST** display selected file names and validation status

#### Tab-Specific Settings Integration
- **MUST** include dedicated settings section within the Audio Split tab
- **MUST** allow adjustment of text-audio similarity threshold (0.5-1.0)
- **MUST** allow adjustment of audio buffer duration (0-500ms start/end padding)
- **MUST** allow selection of output audio format (WAV, MP3, FLAC)
- **MUST** configure processing quality vs speed trade-offs
- **MUST** save tab-specific configuration settings automatically
- **MUST** provide real-time updates of setting values within the tab
- **MUST** reset tab settings to defaults independently

#### Processing Execution
- **MUST** perform AI-powered text-to-audio alignment
- **MUST** split audio into individual files based on sentence boundaries
- **MUST** apply configurable audio buffer padding to each segment
- **MUST** create timestamped output directory for organized results

#### Integrated Progress Tracking
- **MUST** display real-time progress within the tab during processing
- **MUST** show current processing step (loading, analyzing, aligning, splitting)
- **MUST** provide progress bar with completion percentage for overall process
- **MUST** display individual sentence processing status
- **MUST** show alignment confidence scores during processing
- **MUST** allow cancellation of ongoing processing

#### Results Display
- **MUST** display list of generated audio files within the tab
- **MUST** show audio file metadata (duration, format, file size)
- **MUST** provide audio playback controls for each generated file
- **MUST** display text-to-audio alignment visualization
- **MUST** show processing summary (success/failure counts, total duration)
- **MUST** enable direct access to output folder from results area
- **MUST** allow individual file export or batch operations

---

## 3. F5-TTS Tab

### Primary Purpose
Generate high-quality speech audio from text using F5-TTS models with optional voice cloning.

### Functional Requirements

#### Text Input Management
- **MUST** accept text input via direct typing (multi-line text area)
- **MUST** accept text input via file upload (.TXT format)
- **MUST** process text as individual sentences (one per line)
- **MUST** provide sample script loading for voice testing
- **MUST** display sentence count and processing preview

#### Voice Configuration
- **MUST** support multiple F5-TTS model variants (F5TTS_v1_Base, E2TTS_Base, language-specific models)
- **MUST** provide ComfyUI voice examples library
- **MUST** allow custom reference audio upload for voice cloning
- **MUST** auto-generate reference text suggestions based on voice selection
- **MUST** allow manual reference text editing
- **MUST** save custom voices to user library for future use

#### Tab-Specific Settings Integration
- **MUST** include dedicated settings section within the F5-TTS tab
- **MUST** provide configurable generation parameters within the tab:
  - Temperature (0.1-2.0) for output variation
  - Speed control (0.5-2.0) for speech rate
  - Target RMS (0.01-0.5) for volume normalization
  - NFE Steps (16-64) for quality/speed trade-off
  - CFG Strength (1.0-5.0) for conditioning strength
- **MUST** support text chunking configuration (100-800 characters)
- **MUST** allow silence padding configuration (0-2 seconds start/end)
- **MUST** configure B-roll generation settings within the tab
- **MUST** save tab-specific configuration settings automatically
- **MUST** provide real-time parameter preview and validation
- **MUST** reset tab settings to defaults independently

#### B-Roll Integration
- **MUST** offer automatic B-roll video generation for speech content
- **MUST** extract contextual keywords from text sentences
- **MUST** search and download relevant stock videos using keywords
- **MUST** provide video quality selection (HD, SD, Any)
- **MUST** allow configuration of video alternatives per sentence
- **MUST** show keyword preview before processing

#### Processing Execution
- **MUST** load selected F5-TTS model with progress indication
- **MUST** process reference audio for voice cloning when provided
- **MUST** generate speech for each text sentence individually
- **MUST** apply silence padding and audio merging when configured
- **MUST** generate B-roll videos when enabled

#### Integrated Progress Tracking
- **MUST** display real-time progress within the tab during processing
- **MUST** show current processing step (model loading, voice setup, generation)
- **MUST** provide progress bar for overall process and individual sentences
- **MUST** display generation speed and estimated time remaining
- **MUST** show B-roll keyword extraction and video search progress
- **MUST** allow cancellation of ongoing processing

#### Results Display
- **MUST** display list of generated audio files within the tab
- **MUST** provide audio playback controls for each generated file
- **MUST** show audio file metadata (duration, format, voice model used)
- **MUST** display B-roll video previews with sentence associations
- **MUST** allow selection of alternative B-roll videos for each sentence
- **MUST** show combined video results when B-roll is enabled
- **MUST** provide processing summary (success/failure counts, total duration)
- **MUST** enable direct access to output folders from results area
- **MUST** allow individual file export or batch operations

#### Tab-Specific Settings Integration
- **MUST** include dedicated settings section within the Video Frames/Scenes/Audio tab
- **MUST** configure frame extraction similarity threshold (0.1-1.0)
- **MUST** configure frame quality threshold (0.1-1.0)
- **MUST** select output image format (PNG, JPEG, WebP)
- **MUST** set maximum frame extraction limit (optional)
- **MUST** enable/disable scene detection functionality
- **MUST** enable/disable automatic scene splitting into separate videos
- **MUST** configure minimum scene duration (0.5-10 seconds)
- **MUST** enable/disable audio transcription for scenes
- **MUST** configure worker thread count for video processing (1-8 threads)
- **MUST** set optional video resize width for processing

---

## 4. Application Settings Tab

### Primary Purpose
Configure global application preferences and user interface settings that affect all tabs.

### Functional Requirements

#### User Interface Preferences
- **MUST** select application theme (System, Light, Dark)
- **MUST** apply theme changes immediately across all tabs
- **MUST** maintain theme selection across application sessions
- **MUST** configure default file save locations
- **MUST** set application language preferences
- **MUST** configure notification preferences

#### Global Performance Settings
- **MUST** set global memory usage limits
- **MUST** configure temporary file cleanup preferences
- **MUST** set default output directory locations
- **MUST** configure auto-save intervals for user work

#### Settings Management
- **MUST** provide backup and restore of all application settings
- **MUST** reset individual tab settings to defaults
- **MUST** reset all application settings to defaults
- **MUST** export/import settings configurations
- **MUST** validate all setting values within acceptable ranges
- **MUST** provide clear descriptions for all settings

---

## Cross-Tab Functional Requirements

### Navigation and State Management
- **MUST** maintain application state when switching between tabs
- **MUST** preserve user inputs and selections across tab changes
- **MUST** provide consistent navigation experience
- **MUST** prevent data loss during tab transitions
- **MUST** allow tab switching during non-processing states
- **MUST** prevent tab switching during active processing to maintain focus

### Settings Management
- **MUST** maintain tab-specific settings independently
- **MUST** share global application settings (theme, UI preferences) across tabs
- **MUST** prevent settings conflicts between tabs
- **MUST** provide consistent settings UI patterns across all tabs
- **MUST** allow export/import of complete application configuration
- **MUST** validate settings compatibility when importing configurations

### Error Handling and Recovery
- **MUST** provide consistent error reporting across all tabs
- **MUST** allow recovery from non-fatal errors within each tab
- **MUST** maintain application stability during failures
- **MUST** preserve user work and settings during error conditions
- **MUST** isolate errors to prevent affecting other tabs

### Resource Management
- **MUST** efficiently manage memory usage for each tab independently
- **MUST** clean up temporary files and resources per tab
- **MUST** prevent resource conflicts between tabs
- **MUST** optimize for different system capabilities and limitations
- **MUST** support concurrent light operations across tabs (excluding heavy processing)

---

## Performance and Quality Requirements

### Processing Performance
- **MUST** complete frame extraction within reasonable time limits
- **MUST** provide responsive UI during all operations
- **MUST** support cancellation of long-running processes
- **MUST** optimize resource usage for different hardware configurations

### Output Quality
- **MUST** maintain high quality in all generated content
- **MUST** preserve original video/audio quality where possible
- **MUST** Apply consistent processing standards across all operations
- **MUST** Generate professional-quality results suitable for further use

### User Experience
- **MUST** provide intuitive workflow progression
- **MUST** offer clear visual feedback for all operations
- **MUST** Support both novice and advanced user workflows
- **MUST** Maintain consistent design language across all tabs