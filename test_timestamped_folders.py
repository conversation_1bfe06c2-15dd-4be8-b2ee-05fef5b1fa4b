#!/usr/bin/env python3
"""
Test timestamped output folder creation for audio splitting
"""

import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# Add src to path for imports
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_timestamped_folders():
    """Test that output folders are created with timestamps"""
    print("🧪 Testing Timestamped Folder Creation")
    print("=" * 50)
    
    # Create temporary test directory
    test_dir = Path(tempfile.mkdtemp(prefix="vid2frames_test_"))
    print(f"📁 Using test directory: {test_dir}")
    
    try:
        # Simulate the folder creation logic from audio_split_view.py
        audio_file_stem = "test_audio"
        
        # Create first folder
        timestamp1 = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir1 = test_dir / f"{audio_file_stem}_split_{timestamp1}"
        output_dir1.mkdir(parents=True, exist_ok=True)
        
        print(f"✅ Created folder 1: {output_dir1.name}")
        
        # Wait a moment and create second folder
        import time
        time.sleep(1)
        
        timestamp2 = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir2 = test_dir / f"{audio_file_stem}_split_{timestamp2}"
        output_dir2.mkdir(parents=True, exist_ok=True)
        
        print(f"✅ Created folder 2: {output_dir2.name}")
        
        # Check that both folders exist and have different names
        if output_dir1.exists() and output_dir2.exists():
            print("✅ Both timestamped folders created successfully")
        else:
            print("❌ Failed to create timestamped folders")
            return False
            
        if output_dir1.name != output_dir2.name:
            print("✅ Timestamped folders have unique names")
        else:
            print("❌ Timestamped folders have the same name")
            return False
            
        # List all created folders
        print("\n📋 Created folders:")
        for folder in test_dir.iterdir():
            if folder.is_dir():
                print(f"   📁 {folder.name}")
                
        # Test that existing folders don't get overwritten
        # Create a test file in first folder
        test_file = output_dir1 / "test.txt"
        test_file.write_text("This should not be overwritten")
        
        # Create another folder (should be different timestamp)
        time.sleep(1)
        timestamp3 = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir3 = test_dir / f"{audio_file_stem}_split_{timestamp3}"
        output_dir3.mkdir(parents=True, exist_ok=True)
        
        # Check that original file still exists
        if test_file.exists() and test_file.read_text() == "This should not be overwritten":
            print("✅ Existing files preserved (no overwriting)")
        else:
            print("❌ Existing files were overwritten or lost")
            return False
            
        print("\n🎉 Timestamped folder creation test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
        
    finally:
        # Cleanup
        shutil.rmtree(test_dir, ignore_errors=True)
        print(f"🧹 Cleaned up test directory: {test_dir}")

if __name__ == "__main__":
    success = test_timestamped_folders()
    sys.exit(0 if success else 1)