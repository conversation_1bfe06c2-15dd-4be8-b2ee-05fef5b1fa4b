"""
Test the fixed audio split progress dialog to ensure it displays correctly with threading
"""

import flet as ft
import time
import threading
from pathlib import Path

# Import the actual audio split view
from src.ui.audio_split_view import AudioSplitView

def test_audio_split_progress():
    def main(page: ft.Page):
        page.title = "Audio Split Progress Test"
        page.theme_mode = ft.ThemeMode.DARK
        
        # Create audio split view
        audio_view = AudioSplitView()
        audio_view.page = page  # Set page reference
        
        # Mock file selection for testing
        audio_view.selected_audio_file = Path("test_audio.wav")
        audio_view.selected_text_file = Path("example_text.txt")
        
        def simulate_progress():
            """Simulate progress updates from background thread"""
            steps = [
                ("Loading Whisper model...", 0.1),
                ("Transcribing audio...", 0.3),
                ("Aligning text with audio...", 0.5),
                ("Extracting segments...", 0.8),
                ("Complete!", 1.0)
            ]
            
            for message, progress in steps:
                audio_view.update_progress(message, progress)
                time.sleep(2)  # 2 second delay between updates
            
            # Close dialog after completion
            time.sleep(2)
            audio_view.close_progress_dialog()
        
        def test_progress_dialog(e):
            """Test the progress dialog with threading"""
            print("🔄 Starting progress dialog test...")
            audio_view.show_progress_dialog()
            
            # Start simulated progress in background thread
            threading.Thread(target=simulate_progress, daemon=True).start()
        
        # Test button
        test_button = ft.ElevatedButton(
            "Test Audio Split Progress Dialog",
            on_click=test_progress_dialog,
            icon=ft.Icons.PLAY_ARROW
        )
        
        page.add(
            ft.Column([
                ft.Text("Audio Split Progress Dialog Test", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("Click the button to test the progress dialog with background threading"),
                test_button
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=20)
        )
    
    ft.app(target=main)

if __name__ == "__main__":
    test_audio_split_progress()