"""
Check which videos are missing from the combined output
"""
import os

def check_missing_videos():
    base_dir = "f5_tts_output_20250928_235454"
    
    # Get audio files
    audio_dir = os.path.join(base_dir, "individual_files")
    audio_files = sorted([f for f in os.listdir(audio_dir) if f.endswith('.wav')])
    
    # Get video files
    video_dir = os.path.join(base_dir, "combined_videos")
    video_files = sorted([f for f in os.listdir(video_dir) if f.endswith('.mp4')])
    
    # Extract numbers from filenames
    audio_numbers = set()
    video_numbers = set()
    
    for audio_file in audio_files:
        num = audio_file.split('_')[0]
        audio_numbers.add(num)
    
    for video_file in video_files:
        num = video_file.split('_')[0] 
        video_numbers.add(num)
    
    missing_videos = audio_numbers - video_numbers
    print(f"Audio files: {len(audio_files)}")
    print(f"Video files: {len(video_files)}")
    print(f"Missing videos: {len(missing_videos)}")
    
    if missing_videos:
        print("Missing video numbers:")
        for num in sorted(missing_videos):
            # Find the corresponding audio file
            audio_file = next((f for f in audio_files if f.startswith(num + '_')), 'Unknown')
            print(f"  {num}: {audio_file}")

if __name__ == "__main__":
    check_missing_videos()