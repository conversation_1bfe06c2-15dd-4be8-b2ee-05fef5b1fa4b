#!/usr/bin/env python3
"""
Simple test to check View Scenes button visibility
"""
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import after path modification 
from ui.results_view import ResultsView

# Mock scene data
class MockScene:
    def __init__(self, start_time, end_time, start_frame, end_frame, frame_count):
        self.start_time = start_time
        self.end_time = end_time
        self.start_frame = start_frame
        self.end_frame = end_frame
        self.frame_count = frame_count
        self.duration = end_time - start_time

def test_button_visibility():
    # Create results view
    results_view = ResultsView(None)
    
    # Build UI to create button reference
    ui = results_view.build()
    
    print(f"Button exists: {hasattr(results_view, 'view_scenes_button')}")
    if hasattr(results_view, 'view_scenes_button'):
        print(f"Initial button visibility: {results_view.view_scenes_button.visible}")
    
    # Create mock result with scenes
    mock_result = {
        'success': True,
        'job_dir': Path('/tmp/test'),
        'extracted_frames': [],
        'saved_paths': [],
        'detected_scenes': [
            MockScene(0.0, 5.0, 0, 150, 151),
            MockScene(5.0, 10.0, 151, 300, 150)
        ],
        'scene_video_paths': [],
        'processing_time': 30.0
    }
    
    # Set results
    results_view.set_results(mock_result)
    
    print(f"After set_results - button visibility: {results_view.view_scenes_button.visible}")
    print(f"Detected scenes count: {len(results_view.detected_scenes)}")

if __name__ == "__main__":
    test_button_visibility()