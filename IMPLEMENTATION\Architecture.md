# Architecture - Vid2Frames

## System Overview

Vid2Frames is a cross-platform desktop application that extracts distinct frames from video files using computer vision algorithms. Built with Python and modern UI frameworks, it provides a native desktop experience with CSS-like styling capabilities.

## System Architecture

```
┌─────────────────────────────────────────────────────────┐
│                Desktop Application                      │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              User Interface Layer                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │ │
│  │  │   Upload    │  │  Progress   │  │   Results   │ │ │
│  │  │    View     │  │    View     │  │    View     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │ │
│  │  │   Settings  │  │    Scene    │  │  Main Nav   │ │ │
│  │  │    View     │  │   Viewer    │  │   Control   │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │ │
│  └─────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              Application Logic                      │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │ │
│  │  │   File      │  │   Video     │  │   Settings  │ │ │
│  │  │  Manager    │  │ Processor   │  │  Manager    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │ │
│  └─────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────┐ │
│  │        Enhanced Processing Engine (v2.0)            │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │ │
│  │  │   Unified   │  │ Similarity  │  │   Quality   │ │ │
│  │  │Frame + Scene│  │  Detector   │  │  Analyzer   │ │ │
│  │  │  Extractor  │  │    (SSIM)   │  │             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │ │
│  │  │    Scene    │  │   FFmpeg    │  │   Progress  │ │ │
│  │  │  Boundary   │  │ Integration │  │   Tracking  │ │ │
│  │  │  Detection  │  │             │  │             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │ │
│  └─────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────┐ │
│  │               Data Layer                            │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │ │
│  │  │   SQLite    │  │   Local     │  │   Config    │ │ │
│  │  │  Database   │  │   Cache     │  │    Files    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │ │
│  │  │    Scene    │  │   Frames    │  │    Video    │ │ │
│  │  │   Storage   │  │   Storage   │  │  Metadata   │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## Revolutionary Unified Scene Detection Architecture

### Breakthrough Innovation (September 2025)
Instead of traditional two-pass scene detection (extract frames → analyze frames for scenes), Vid2Frames implements a **unified single-pass approach** that detects scene boundaries during frame extraction:

```
Traditional Approach (INEFFICIENT):
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Frame          │    │  Scene          │    │  Video          │
│  Extraction     │───▶│  Detection      │───▶│  Splitting      │
│  (Similarity)   │    │  (Re-analyze)   │    │  (FFmpeg)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
   Detect changes        Detect changes         Create videos
   ↓                     ↓ (DUPLICATE!)         ↓
   Extract frames        Find scene bounds      Split scenes

Unified Approach (BREAKTHROUGH):
┌──────────────────────────────────────────────┐    ┌─────────────────┐
│         Unified Frame + Scene Detection       │───▶│  Video          │
│  ┌─────────────────┐  ┌─────────────────┐   │    │  Splitting      │
│  │  Frame Change   │  │  Scene Boundary │   │    │  (FFmpeg)       │
│  │  Detection      │═▶│  Recording      │   │    └─────────────────┘
│  └─────────────────┘  └─────────────────┘   │         ↑
│            ↓                    ↓           │    Create videos
│       Extract frame     Record timestamp    │    from boundaries
└──────────────────────────────────────────────┘
        Single similarity calculation per frame
```

### Performance Benefits Achieved
- **~50% faster processing** - No duplicate similarity calculations
- **Perfect alignment** - Scene changes = Frame changes exactly
- **Lower memory usage** - Single-pass eliminates intermediate storage
- **More reliable** - No threshold mismatches between passes

## Technology Stack

### Desktop Framework Options
- **Flet** (Recommended): Flutter-based UI with Python backend
  - CSS-like styling with Material Design
  - Cross-platform (Windows, macOS, Linux)
  - Web technologies for native desktop apps
  - Hot reload for development

- **Alternative - Kivy**: Native Python UI framework
  - CSS-like styling with .kv files
  - Touch-friendly interface design
  - OpenGL-based rendering

### Video Processing
- **OpenCV**: Frame extraction and computer vision operations
- **FFmpeg-python**: Video format handling, metadata extraction, and scene video creation
- **scikit-image**: Advanced image similarity algorithms (SSIM for unified scene detection)
- **Pillow**: Image manipulation, format conversion, and optimization
- **imageio**: Additional video reading capabilities

### Advanced Scene Processing (Breakthrough Feature)
- **Unified Detection Algorithm**: Single-pass frame extraction with simultaneous scene boundary detection
- **SSIM-based Similarity**: Structural Similarity Index for accurate content change detection
- **Scene Video Creation**: Automatic splitting of original video into individual scene files
- **Lossless Processing**: Stream copying with FFmpeg for high-quality scene videos
- **Real-time Feedback**: Live scene boundary detection with user progress updates

### Data & Storage
- **SQLite**: Lightweight database for job history and settings
- **diskcache**: Local caching for processed frames
- **pathlib**: Cross-platform file path handling
- **configparser**: Application configuration management

### Development & Packaging
- **Poetry**: Dependency management and packaging
- **PyInstaller**: Create standalone executables
- **pytest**: Testing framework
- **black**: Code formatting
- **pre-commit**: Git hooks for code quality

## Data Models

### Application Data Structures

```python
# Video Processing Job
@dataclass
class VideoJob:
    id: str
    filename: str
    file_path: Path
    file_size: int
    duration: float
    status: JobStatus
    created_at: datetime
    completed_at: Optional[datetime]
    settings: ProcessingSettings
    progress: float = 0.0

# Extracted Frame (Enhanced)
@dataclass
class ExtractedFrame:
    id: str
    job_id: str
    frame_number: int
    timestamp: float
    similarity_score: float
    quality_score: float
    file_path: Path
    file_size: int
    width: int
    height: int
    format: str

# Scene Data (NEW - Breakthrough Feature)
@dataclass
class SceneData:
    id: str
    job_id: str
    start_time: float
    end_time: float
    duration: float
    start_frame: int
    end_frame: int
    frame_count: int
    video_path: Optional[Path] = None

# Processing Settings (Enhanced)
@dataclass
class ProcessingSettings:
    similarity_threshold: float = 0.85
    quality_threshold: float = 0.7
    max_frames: Optional[int] = None
    output_format: str = "PNG"
    resize_width: Optional[int] = None
    scene_detection: bool = True
    split_scenes: bool = True  # NEW: Automatic scene video creation
    min_scene_duration: float = 1.0  # NEW: Filter very short scenes
    extract_keyframes_only: bool = False
```

### SQLite Database Schema

```sql
-- Application settings
CREATE TABLE settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Video processing jobs
CREATE TABLE video_jobs (
    id TEXT PRIMARY KEY,
    filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    duration REAL,
    status TEXT DEFAULT 'pending',
    settings TEXT, -- JSON serialized ProcessingSettings
    progress REAL DEFAULT 0.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT
);

-- Extracted frames
CREATE TABLE extracted_frames (
    id TEXT PRIMARY KEY,
    job_id TEXT REFERENCES video_jobs(id),
    frame_number INTEGER NOT NULL,
    timestamp REAL NOT NULL,
    similarity_score REAL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    width INTEGER NOT NULL,
    height INTEGER NOT NULL,
    format TEXT NOT NULL
);

-- Detected scenes (NEW)
CREATE TABLE detected_scenes (
    id TEXT PRIMARY KEY,
    job_id TEXT REFERENCES video_jobs(id),
    start_time REAL NOT NULL,
    end_time REAL NOT NULL,
    duration REAL NOT NULL,
    start_frame INTEGER NOT NULL,
    end_frame INTEGER NOT NULL,
    frame_count INTEGER NOT NULL,
    video_path TEXT, -- Path to scene video file if created
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Processing history for analytics (Enhanced)
CREATE TABLE processing_stats (
    id TEXT PRIMARY KEY,
    job_id TEXT REFERENCES video_jobs(id),
    total_frames INTEGER,
    extracted_frames INTEGER,
    detected_scenes INTEGER, -- NEW
    scene_videos_created INTEGER, -- NEW
    processing_time_seconds REAL,
    average_similarity REAL,
    unified_detection_used BOOLEAN DEFAULT TRUE, -- NEW
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Application Structure

### Project Layout
```
vid2frames/
├── src/
│   ├── ui/                    # User interface components
│   │   ├── __init__.py
│   │   ├── main_window.py     # Main application window
│   │   ├── upload_view.py     # File upload interface
│   │   ├── progress_view.py   # Processing progress (5-stage pipeline)
│   │   ├── results_view.py    # Results display with scene viewer
│   │   └── settings_view.py   # Configuration panel with scene settings
│   ├── core/                  # Business logic (Enhanced)
│   │   ├── __init__.py
│   │   ├── video_processor.py # Unified frame + scene processing engine
│   │   ├── frame_extractor.py # DEPRECATED - now part of video_processor
│   │   ├── scene_detector.py  # DEPRECATED - now unified approach
│   │   ├── similarity.py      # SSIM similarity detection
│   │   └── file_manager.py    # File operations with scene support
│   ├── models/                # Data models (Enhanced)
│   │   ├── __init__.py
│   │   ├── job.py            # Job data classes
│   │   ├── frame_data.py     # Frame information classes
│   │   ├── scene_data.py     # NEW: Scene information classes
│   │   └── settings.py       # Settings management with scene options
│   ├── database/              # Database operations (Enhanced)
│   │   ├── __init__.py
│   │   ├── connection.py     # SQLite connection
│   │   ├── repository.py     # Data access layer
│   │   └── migrations/       # Database schema updates
│   └── utils/                 # Utilities
│       ├── __init__.py
│       ├── config.py         # Configuration with scene defaults
│       ├── logger.py         # Enhanced logging with scene events
│       └── helpers.py        # Helper functions
├── storage/                   # Local data storage
│   ├── data/                 # Processed frame and scene data
│   └── temp/                 # Temporary processing files
├── assets/                    # Static assets
├── tests/                     # Test suite (Enhanced)
│   ├── test_unified_scene_detection.py # NEW: Scene detection tests
│   ├── test_video_processing.py        # Enhanced processing tests
│   └── test_scene_splitting.py         # NEW: Scene splitting tests
├── docs/                      # Documentation
│   └── UNIFIED_SCENE_DETECTION.md     # NEW: Algorithm documentation
└── requirements.txt          # Dependencies
```

## User Interface Design

### Flet-based Interface Structure

```python
# Main application window
class MainWindow:
    def __init__(self):
        self.page: ft.Page = None
        self.upload_view = UploadView()
        self.progress_view = ProgressView() 
        self.results_view = ResultsView()
        self.settings_view = SettingsView()

# CSS-like styling example
upload_container = ft.Container(
    content=ft.Column([...]),
    bgcolor=ft.colors.BLUE_GREY_50,
    border_radius=10,
    padding=20,
    margin=10,
    animate=ft.Animation(500, ft.AnimationCurve.EASE_OUT)
)
```

### Theme and Styling
- **Material Design 3**: Modern, accessible design system
- **CSS-like Properties**: Padding, margin, border-radius, colors
- **Responsive Layout**: Adapts to different window sizes
- **Dark/Light Mode**: System preference detection
- **Custom Themes**: Brand colors and typography

## Security Considerations

### Local Data Protection
- **File System Permissions**: Restricted access to processing directories
- **Database Security**: SQLite with file-level permissions
- **Temporary File Cleanup**: Automatic cleanup of processing artifacts
- **Safe File Handling**: Input validation and sanitization

### Input Validation
- **File Type Validation**: Whitelist of supported video formats
- **File Size Limits**: Configurable maximum file sizes
- **Path Traversal Prevention**: Secure file path handling
- **Malware Protection**: Basic file signature validation

### Privacy Considerations
- **Local Processing**: All data stays on user's machine
- **No Telemetry**: Optional usage analytics only
- **Secure Defaults**: Conservative security settings
- **Data Retention**: User-controlled cleanup policies

## Scalability Approach

### Performance Optimization
- **Multithreading**: Parallel frame processing using ThreadPoolExecutor
- **Memory Management**: Streaming video processing for large files
- **Disk Caching**: Smart caching of intermediate results
- **Progressive Loading**: UI updates during processing

### Resource Management
- **Memory Limits**: Configurable memory usage caps
- **Disk Space**: Automatic cleanup and storage monitoring
- **CPU Usage**: Adjustable processing thread counts
- **GPU Acceleration**: Optional OpenCV CUDA support

### User Experience Optimization
- **Background Processing**: Non-blocking UI during operations
- **Progress Indicators**: Real-time processing feedback
- **Cancellation Support**: Allow users to stop processing
- **Result Preview**: Quick preview of extracted frames

## Deployment Strategy

### Packaging & Distribution
```bash
# PyInstaller configuration
pyinstaller --onedir \
    --add-data "assets/*:assets/" \
    --add-data "database/schema.sql:database/" \
    --hidden-import cv2 \
    --hidden-import flet \
    --name "Vid2Frames" \
    src/main.py
```

### Cross-Platform Builds
```yaml
# GitHub Actions example
name: Build Desktop App
on: [push, release]

jobs:
  build:
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
    
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: pip install -r requirements.txt
      
      - name: Build executable
        run: pyinstaller vid2frames.spec
      
      - name: Create installer
        run: |
          # Windows: Use Inno Setup
          # macOS: Create .dmg
          # Linux: Create .AppImage
```

### Installation Options
- **Standalone Executable**: Single-file distribution
- **Installer Packages**: Platform-native installers
- **Portable Version**: No installation required
- **Auto-Updates**: Optional update mechanism

## Performance Benchmarks

### Target Performance Metrics
- **Frame Extraction**: Process HD video at 60+ FPS equivalent
- **Memory Usage**: <2GB RAM for typical operations
- **Startup Time**: <3 seconds application launch
- **Processing Efficiency**: Extract frames from 10-minute video in <2 minutes
- **UI Responsiveness**: <100ms response to user interactions

### Optimization Techniques
```python
# Example: Efficient frame processing
class FrameProcessor:
    def __init__(self, max_workers=4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.cache = LRUCache(maxsize=1000)
    
    async def process_video(self, video_path: Path):
        cap = cv2.VideoCapture(str(video_path))
        
        # Process frames in batches
        batch_size = 30
        frames_batch = []
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            frames_batch.append(frame)
            
            if len(frames_batch) >= batch_size:
                # Process batch in parallel
                await self._process_batch(frames_batch)
                frames_batch.clear()
```