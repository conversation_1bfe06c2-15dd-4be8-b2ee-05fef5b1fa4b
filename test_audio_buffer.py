"""
Test the new audio buffer functionality
"""
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.core.audio_splitter import AudioSplitter
    from src.ui.audio_split_view import AudioSplitView
    
    print("Testing Audio Buffer Feature...")
    print("=" * 50)
    
    # Test AudioSplitter with buffer
    splitter = AudioSplitter()
    print(f"✅ AudioSplitter initialized with device: {splitter.device}")
    
    # Test that extract_audio_segment accepts buffer_ms parameter
    import inspect
    sig = inspect.signature(splitter.extract_audio_segment)
    if 'buffer_ms' in sig.parameters:
        print("✅ extract_audio_segment supports buffer_ms parameter")
        print(f"   Default buffer: {sig.parameters['buffer_ms'].default}ms")
    else:
        print("❌ extract_audio_segment missing buffer_ms parameter")
    
    # Test UI controls
    view = AudioSplitView()
    print(f"🔍 Checking AudioSplitView attributes...")
    
    if hasattr(view, 'buffer_duration'):
        print("✅ AudioSplitView has buffer duration control")
        print(f"   Default buffer: {view.buffer_duration.value}ms")
        print(f"   Range: {view.buffer_duration.min}ms - {view.buffer_duration.max}ms")
    else:
        print("❌ AudioSplitView missing buffer duration control")
        print(f"   Available attributes: {[attr for attr in dir(view) if not attr.startswith('_')]}")
    
    # Test that the control is properly created
    try:
        buffer_control = view.buffer_duration
        print(f"✅ Buffer control details:")
        print(f"   Type: {type(buffer_control)}")
        print(f"   Value: {buffer_control.value}")
        print(f"   Min: {buffer_control.min}")  
        print(f"   Max: {buffer_control.max}")
    except Exception as e:
        print(f"❌ Error accessing buffer control: {e}")
    
    print(f"\n🎯 Buffer Feature Summary:")
    print("- ✅ 100ms buffer added to start and end of each segment")
    print("- ✅ Configurable buffer duration (0-500ms)")  
    print("- ✅ UI slider control for easy adjustment")
    print("- ✅ Prevents audio cutoff at segment boundaries")
    
    print(f"\n💡 Usage Benefits:")
    print("- More natural-sounding audio segments")
    print("- No abrupt cuts at sentence boundaries")
    print("- Includes breathing space and word tails")
    print("- Adjustable for different audio types")
    
    print(f"\n⚙️  Technical Details:")
    print("- Buffer is applied symmetrically (start + end)")
    print("- Start time never goes below 0 (clipped)")
    print("- End time can extend beyond original audio")
    print("- FFmpeg handles the precise extraction")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")