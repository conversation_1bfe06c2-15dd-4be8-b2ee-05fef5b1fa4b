# F5-TTS Combined Audio Generation Feature

## Overview
Enhanced F5-TTS functionality to support both individual file generation and combined audio generation based on user preference. This provides better audio quality for combined generation while maintaining the flexibility of individual files when needed.

## Feature Description

### New Checkbox: "Generate Individual Files"
- **Default State**: Checked (True)
- **Location**: F5-TTS tab, Audio Output Controls section
- **Tooltip**: "Create separate audio files for each line. Uncheck to generate one long audio file."

### Behavior

#### When Checked (Individual Files Mode)
- **Default behavior** - maintains backward compatibility
- Creates separate `.wav` files for each line of input text
- Files stored in: `output_dir/individual_files/`
- Naming format: `001_sentence_text.wav`, `002_sentence_text.wav`, etc.
- **Benefits**: 
  - Easy editing and selective regeneration
  - Better error recovery
  - Organized file structure
  - Flexible post-processing

#### When Unchecked (Combined Audio Mode)  
- **New behavior** - generates one long audio file
- Combines all input lines into single text for F5-TTS processing
- File stored in: `output_dir/combined_audio_TIMESTAMP.wav`
- **Benefits**:
  - Better prosody and natural flow
  - More coherent intonation across sentences
  - F5-T<PERSON> maintains context throughout entire text
  - No audio concatenation artifacts
  - Smoother, more natural-sounding result

## Technical Implementation

### UI Changes
```python
# Old checkbox
self.merge_audio_files = ft.Checkbox(
    label="Merge Audio Files",
    value=False,
    tooltip="Combine all individual audio files into one merged file"
)

# New checkbox  
self.generate_individual_files = ft.Checkbox(
    label="Generate Individual Files",
    value=True,
    tooltip="Create separate audio files for each line. Uncheck to generate one long audio file."
)
```

### Core Processing Logic
```python
def process_sentences(self, sentences, output_dir, params=None, ...):
    generate_individual = params.get('generate_individual_files', True)
    
    if not generate_individual:
        # Generate one combined audio file
        return self._process_combined_audio(sentences, output_dir, params, ...)
    
    # Original individual file generation
    # ... existing logic
```

### New Combined Audio Method
```python
def _process_combined_audio(self, sentences, output_dir, params, ...):
    # Combine all sentences with appropriate spacing
    combined_text = " ".join(sentence.strip() for sentence in sentences if sentence.strip())
    
    # Generate single audio file
    output_path = output_dir / f"combined_audio_{timestamp}.wav"
    success = self.generate_speech(combined_text, output_path, params)
    
    # Return results in same format as individual processing
```

## User Experience

### Recommended Usage

**Use Individual Files When:**
- You need to edit specific parts later
- Working with long texts where some parts might fail
- Building content that requires selective updates
- Need organized file structure for project management

**Use Combined Audio When:**
- You want the most natural-sounding speech
- Processing narrative or conversational content
- Quality is more important than file organization
- Creating final output for direct use

### Example Workflow
1. Enter text with multiple lines in F5-TTS tab
2. Choose generation mode:
   - **Checked**: Individual files (default)
   - **Unchecked**: Single combined file
3. Configure other settings (voice, parameters, etc.)
4. Click "Generate Speech"
5. Review results in appropriate output directory

## Quality Comparison

### Before (Merged Files)
```
Individual generation → Audio file 1, 2, 3, 4 → Concatenate → Final audio
```
- Potential audio seams between files
- Inconsistent prosody across transitions
- Artificial pauses at boundaries

### After (Combined Audio)
```
Combined text → Single F5-TTS generation → Final audio
```
- Natural flow throughout entire text
- Consistent prosody and intonation
- Context-aware pauses and emphasis

## Files Modified
- `src/ui/f5_tts_view.py` - Updated checkbox and parameter mapping
- `src/core/f5_tts.py` - Added combined audio processing logic
- Added `_process_combined_audio()` method
- Updated `process_sentences()` routing logic

## Backward Compatibility
✅ **Fully maintained** - existing behavior is default (checkbox checked)
✅ All existing parameters and functionality preserved
✅ No breaking changes to API or file structure

## Testing
- `test_f5tts_combined_audio.py` - Comprehensive feature testing
- Verified UI changes and parameter mapping
- Confirmed both generation modes work correctly

## Date
September 30, 2025