import flet as ft
from pathlib import Path
from typing import Optional, Callable

from ..core.video_processor import VideoProcessor
from ..utils.file_manager import FileManager
import threading


class UploadView:
    """Upload view for selecting video files"""
    
    def __init__(self, on_video_selected: Callable[[Path], None]):
        self.on_video_selected = on_video_selected
        self.file_manager = FileManager()
        self.selected_file = None
        self.page = None  # Will be set by main window
        
        # File picker
        self.file_picker = ft.FilePicker(
            on_result=self.on_file_picker_result
        )
        
    def build(self):
        # Create the upload area
        self.upload_area = ft.Container(
            content=ft.Column(
                [
                    ft.Icon(
                        ft.Icons.CLOUD_UPLOAD_OUTLINED,
                        size=64,
                        color=ft.Colors.PRIMARY
                    ),
                    ft.Text(
                        "Drop video files here or click to browse",
                        size=18,
                        weight=ft.FontWeight.W_500,
                        color=ft.Colors.PRIMARY
                    ),
                    ft.Text(
                        "Supported formats: MP4, AVI, MOV, MKV, FLV, WMV, WebM",
                        size=12,
                        color=ft.Colors.ON_SURFACE_VARIANT
                    ),
                    ft.ElevatedButton(
                        "Browse Files",
                        icon=ft.Icons.FOLDER_OPEN,
                        on_click=self.open_file_picker,
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.PRIMARY,
                            color=ft.Colors.WHITE,
                            padding=20
                        )
                    )
                ],
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=16
            ),
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border=ft.border.all(2, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            border_radius=ft.border_radius.all(12),
            padding=40,
            margin=20,
            animate=ft.Animation(300, ft.AnimationCurve.EASE_OUT),
            on_click=self.open_file_picker
        )
        
        # File info section (initially hidden)
        # Create text elements with references for easier updates
        self.filename_text = ft.Text("", size=14, weight=ft.FontWeight.W_500)
        self.file_details_text = ft.Text("", size=12, color=ft.Colors.ON_SURFACE_VARIANT)
        
        self.file_info_section = ft.Container(
            content=ft.Column([
                ft.Text(
                    "Selected File",
                    size=16,
                    weight=ft.FontWeight.W_600,
                    color=ft.Colors.PRIMARY
                ),
                ft.Row([
                    ft.Icon(ft.Icons.VIDEO_FILE, color=ft.Colors.PRIMARY),
                    ft.Column([
                        self.filename_text,
                        self.file_details_text,
                    ], spacing=2, expand=True)
                ], spacing=10),
                ft.Row([
                    ft.ElevatedButton(
                        "Start Processing",
                        icon=ft.Icons.PLAY_ARROW,
                        on_click=self.start_processing,
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.PRIMARY,
                            color=ft.Colors.WHITE,
                            padding=20
                        )
                    ),
                    ft.OutlinedButton(
                        "Choose Different File",
                        icon=ft.Icons.REFRESH,
                        on_click=self.clear_selection,
                        style=ft.ButtonStyle(
                            color=ft.Colors.PRIMARY,
                            padding=20
                        )
                    )
                ], spacing=10)
            ], spacing=16),
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border=ft.border.all(2, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            border_radius=ft.border_radius.all(12),
            padding=20,
            margin=20,
            visible=False
        )
        
        # Error display
        self.error_display = ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED_600),
                ft.Text("", color=ft.Colors.RED_700, expand=True)
            ], spacing=10),
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border=ft.border.all(2, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            border_radius=ft.border_radius.all(12),
            padding=16,
            margin=20,
            visible=False
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Text(
                        "Upload Video File",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PRIMARY
                    ),
                    padding=20,
                    alignment=ft.alignment.center
                ),
                self.upload_area,
                self.file_info_section,
                self.error_display,
                self.file_picker  # Hidden file picker
            ], 
            scroll=ft.ScrollMode.AUTO,
            expand=True),
            expand=True
        )
    
    def open_file_picker(self, e=None):
        """Open file picker dialog"""
        self.file_picker.pick_files(
            dialog_title="Select video file",
            file_type=ft.FilePickerFileType.CUSTOM,
            allowed_extensions=["mp4", "avi", "mov", "mkv", "flv", "wmv", "webm", "m4v"]
        )
    
    def on_file_picker_result(self, e: ft.FilePickerResultEvent):
        """Handle file picker result"""
        if e.files:
            file_path = Path(e.files[0].path)
            self.set_selected_file(file_path)
    
    def set_selected_file(self, file_path: Path):
        """Set the selected file and update UI"""
        self.clear_error()
        
        # Validate file
        is_valid, message = self.file_manager.validate_video_file(file_path)
        
        if not is_valid:
            self.show_error(f"Invalid file: {message}")
            return
        
        # Store selected file
        self.selected_file = file_path
        
        # Get file info in background thread
        threading.Thread(
            target=self._load_file_info,
            args=(file_path,),
            daemon=True
        ).start()
    
    def _load_file_info(self, file_path: Path):
        """Load file information in background"""
        try:
            processor = VideoProcessor()
            video_info = processor.get_video_info(file_path)
            
            # Update UI on main thread using a safer approach
            def update_ui():
                self._update_file_info(file_path, video_info)
                if self.page:
                    self.page.update()
            
            # Schedule UI update
            if self.page:
                self.page.run_thread(update_ui)
            
        except Exception as e:
            def show_error_ui():
                self.show_error(f"Error reading file: {str(e)}")
                if self.page:
                    self.page.update()
            
            if self.page:
                self.page.run_thread(show_error_ui)
    
    def _update_file_info(self, file_path: Path, video_info: dict):
        """Update file info display"""
        # Update filename using direct reference
        self.filename_text.value = file_path.name
        
        # Update file info using direct reference
        self.file_details_text.value = (
            f"Duration: {video_info['duration']:.1f}s • "
            f"Resolution: {video_info['width']}×{video_info['height']} • "
            f"FPS: {video_info['fps']:.1f} • "
            f"Size: {video_info['file_size_mb']:.1f} MB"
        )
        
        # Show file info section and hide upload area
        self.file_info_section.visible = True
        self.upload_area.visible = False
    
    def start_processing(self, e):
        """Start processing the selected video"""
        if self.selected_file:
            self.on_video_selected(self.selected_file)
    
    def clear_selection(self, e=None):
        """Clear current file selection"""
        self.selected_file = None
        
        # Reset UI state
        self.file_info_section.visible = False
        self.upload_area.visible = True
        self.clear_error()
        
        # Clear file info text
        self.filename_text.value = ""
        self.file_details_text.value = ""
        
        # Update the page to reflect changes
        if self.page:
            self.page.update()
    
    def show_error(self, message: str):
        """Display error message"""
        error_text = self.error_display.content.controls[1]
        error_text.value = message
        self.error_display.visible = True
        
        if self.page:
            self.page.update()
    
    def clear_error(self):
        """Clear error display"""
        self.error_display.visible = False
        if self.page:
            self.page.update()
