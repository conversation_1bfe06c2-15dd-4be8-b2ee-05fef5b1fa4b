"""
Base results view class for operation-specific result displays
"""
from abc import ABC, abstractmethod
import flet as ft
from typing import Dict, Any, List, Optional


class BaseResultsView(ABC):
    """Base class for operation-specific results views"""
    
    def __init__(self, operation_result: Dict[str, Any]):
        self.operation_result = operation_result
        self.page: Optional[ft.Page] = None
    
    @abstractmethod
    def build_summary_cards(self) -> List[ft.Control]:
        """Build summary information cards - implement this for your operation"""
        pass
    
    @abstractmethod
    def build_action_buttons(self) -> List[ft.Control]:
        """Build action buttons for this result type - implement this for your operation"""
        pass
    
    @abstractmethod
    def build_results_display(self) -> ft.Control:
        """Build the main results display area - implement this for your operation"""
        pass
    
    def build(self) -> ft.Control:
        """Build the complete results view (common layout + operation-specific content)"""
        # Build header
        header = self._build_common_header()
        
        # Build summary cards
        summary_section = ft.Column([
            ft.Text("Results Summary", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.PRIMARY),
            ft.Container(height=10),
            ft.Row(self.build_summary_cards(), wrap=True, spacing=10)
        ])
        
        # Build actions section
        actions_section = ft.Column([
            ft.Text("Actions", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.PRIMARY),
            ft.Container(height=10),
            ft.Row(self.build_action_buttons(), wrap=True, spacing=10)
        ])
        
        # Build main results display
        results_section = ft.Column([
            ft.Text("Results", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.PRIMARY),
            ft.Container(height=10),
            self.build_results_display()
        ])
        
        return ft.Column([
            header,
            ft.Container(height=20),
            summary_section,
            ft.Container(height=20),
            actions_section,
            ft.Container(height=20),
            results_section
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _build_common_header(self) -> ft.Control:
        """Build common results header"""
        operation_type = self.operation_result.get('type', 'Unknown')
        completion_time = self.operation_result.get('completion_time', 'Unknown')
        
        return ft.Column([
            ft.Text(f"{operation_type.title()} Complete!", 
                   size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_400,
                   text_align=ft.TextAlign.CENTER),
            ft.Text(f"Completed at: {completion_time}", 
                   size=12, color=ft.Colors.ON_SURFACE_VARIANT,
                   text_align=ft.TextAlign.CENTER)
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
    
    def create_summary_card(self, title: str, value: str, icon: str, 
                          subtitle: str = None, color: str = None) -> ft.Control:
        """Helper method to create consistent summary cards"""
        card_content = [
            ft.Icon(icon, size=32, color=color or ft.Colors.PRIMARY),
            ft.Container(height=5),
            ft.Text(value, size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.ON_SURFACE),
            ft.Text(title, size=12, color=ft.Colors.ON_SURFACE_VARIANT)
        ]
        
        if subtitle:
            card_content.append(ft.Text(subtitle, size=10, color=ft.Colors.ON_SURFACE_VARIANT))
        
        return ft.Container(
            content=ft.Column(
                card_content,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=2
            ),
            width=150,
            height=120,
            padding=15,
            bgcolor=ft.Colors.GREY_800 if hasattr(self, 'page') and self.page and self.page.theme_mode == ft.ThemeMode.DARK else ft.Colors.GREY_100,
            border_radius=10
        )
    
    def create_action_button(self, text: str, icon: str, on_click, 
                           color: str = None, tooltip: str = None) -> ft.Control:
        """Helper method to create consistent action buttons"""
        return ft.ElevatedButton(
            text=text,
            icon=icon,
            on_click=on_click,
            color=ft.Colors.WHITE,
            bgcolor=color or ft.Colors.PRIMARY,
            tooltip=tooltip
        )
    
    def set_page(self, page: ft.Page):
        """Set page reference (called by framework)"""
        self.page = page