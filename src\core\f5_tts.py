"""
F5-TTS (Flow-based) Text-to-Speech implementation
Based on the ComfyUI chatterbox_srt_voice node
"""

import os
import sys
import torch
import torchaudio
import numpy as np
import soundfile as sf
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
import re
import threading
import tempfile
from datetime import datetime
from datetime import datetime


class F5TTSProcessor:
    """F5-TTS processor for converting text sentences to speech"""
    
    def __init__(self):
        # Detect available device (RTX 5090 compatibility confirmed with PyTorch 2.8.0)
        gpu_available = torch.cuda.is_available()
        if gpu_available:
            try:
                # Test GPU functionality
                torch.cuda.init()
                test_tensor = torch.tensor([1.0]).cuda()
                del test_tensor
                print(f"🔍 Detected GPU: {torch.cuda.get_device_name(0)}")
                print("✅ GPU acceleration available for F5-TTS")
                self.device = "cuda"
            except Exception as gpu_error:
                print(f"🔍 Detected GPU: {torch.cuda.get_device_name(0) if torch.cuda.device_count() > 0 else 'Unknown'}")
                print(f"❌ GPU error: {gpu_error}")
                print("🔄 Falling back to CPU for F5-TTS")
                self.device = "cpu"
        else:
            self.device = "cpu"
            
        self.model = None
        self.model_loaded = False
        self.sample_rate = 24000
        
        # Default F5-TTS parameters (based on ComfyUI screenshot)
        self.default_params = {
            'model': 'E2TTS_Base',
            'seed': 1193103530,
            'device': 'auto',
            'control_after_generate': 'randomize',
            'temperature': 0.8,
            'speed': 1.0,
            'target_rms': 0.10,
            'cross_fade_duration': 0.15,
            'nfe_step': 32,
            'cfg_strength': 2.0,
            'enable_chunking': True,
            'max_chars_per_chunk': 400
        }
        
        # Voice settings
        self.reference_audio = None
        self.reference_text = ""
        self.use_comfy_f5 = False
        
        # ComfyUI chatterbox F5-TTS path (local copy)
        self.chatterbox_path = Path("chatterbox_srt_voice")
        self.comfy_voices_path = self.chatterbox_path / "voices_examples"
    
    def init_comfy_f5_model(self, model_name: str):
        """Initialize F5-TTS model using ChatterBox implementation"""
        try:
            print(f"🔧 Initializing ChatterBox F5-TTS model: {model_name}")
            
            # Add chatterbox to Python path
            import sys
            chatterbox_path = str(self.chatterbox_path.absolute())
            if chatterbox_path not in sys.path:
                sys.path.insert(0, chatterbox_path)
            
            # Import ChatterBox F5-TTS
            from engines.f5tts.f5tts import ChatterBoxF5TTS
            
            # Initialize with best available device (GPU preferred)
            self.model = ChatterBoxF5TTS(
                model_name=model_name, 
                device=self.device,
                ckpt_dir=None
            )
            
            print(f"✅ ChatterBox F5-TTS model '{model_name}' loaded successfully")
            return self.model
            
        except Exception as e:
            print(f"❌ Failed to initialize ChatterBox F5-TTS: {e}")
            import traceback
            traceback.print_exc()
            # Fall back to mock
            print("🔧 Using mock implementation as fallback")
            return {"model_name": model_name, "type": "comfy_f5"}
    
    def try_install_f5_tts(self) -> bool:
        """Try to install F5-TTS automatically"""
        try:
            import subprocess
            import sys
            
            print("🔄 Attempting to install F5-TTS...")
            
            # Try installing from GitHub (most common source)
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "git+https://github.com/SWivid/F5-TTS.git"
            ], timeout=300)
            
            print("✅ F5-TTS installed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to install F5-TTS: {e}")
            return False
    
    def get_available_comfy_voices(self) -> List[Dict[str, str]]:
        """Get available voice examples from ComfyUI"""
        voices = []
        
        if self.comfy_voices_path.exists():
            # Search recursively for voice files (including subfolders)
            for voice_file in self.comfy_voices_path.rglob("*.wav"):
                # Extract voice info from filename
                voice_name = voice_file.stem
                
                # Look for corresponding reference text file
                ref_text_file = voice_file.with_suffix('.reference.txt')
                if not ref_text_file.exists():
                    # Fallback to .txt file
                    ref_text_file = voice_file.with_suffix('.txt')
                
                reference_text = ""
                if ref_text_file.exists():
                    try:
                        with open(ref_text_file, 'r', encoding='utf-8') as f:
                            reference_text = f.read().strip()
                    except Exception as e:
                        print(f"⚠️ Failed to read reference text for {voice_name}: {e}")
                
                # Use relative path for display name if in subfolder
                relative_path = voice_file.relative_to(self.comfy_voices_path)
                display_name = str(relative_path.with_suffix('')).replace('\\', '/').replace('/', ' - ')
                if display_name != voice_name:
                    display_name = f"{relative_path.parent.name} - {voice_name}"
                else:
                    display_name = voice_name
                
                voices.append({
                    "name": display_name,
                    "path": str(voice_file),
                    "format": "wav",
                    "reference_text": reference_text
                })
            
            # Also check for other audio formats recursively
            for ext in ["*.mp3", "*.m4a", "*.flac"]:
                for voice_file in self.comfy_voices_path.rglob(ext):
                    voice_name = voice_file.stem
                    
                    # Look for corresponding reference text file
                    ref_text_file = self.comfy_voices_path / f"{voice_name}.reference.txt"
                    reference_text = ""
                    if ref_text_file.exists():
                        try:
                            with open(ref_text_file, 'r', encoding='utf-8') as f:
                                reference_text = f.read().strip()
                        except Exception as e:
                            print(f"⚠️ Failed to read reference text for {voice_name}: {e}")
                    
                    voices.append({
                        "name": voice_name,
                        "path": str(voice_file),
                        "format": voice_file.suffix[1:],
                        "reference_text": reference_text
                    })
        
        print(f"🎤 Found {len(voices)} voice examples in ComfyUI")
        return voices
    
    def load_model(self, model_name: str = "E2TTS_Base", progress_callback: Optional[Callable] = None):
        """Load the F5-TTS model"""
        try:
            if progress_callback:
                progress_callback("Loading F5-TTS model...", 0.1)
            
            # Try to import F5-TTS components - attempt multiple sources
            try:
                # Try importing from different possible locations
                import importlib.util
                
                # Check for local chatterbox_srt_voice first (self-contained)
                local_chatterbox_path = Path(__file__).parent.parent.parent / "chatterbox_srt_voice"
                comfy_f5_path = Path("C:/Users/<USER>/Documents/ComfyUI/custom_nodes/chatterbox_srt_voice")
                
                # Priority 1: Local copy (for installer and self-contained deployment)
                if local_chatterbox_path.exists():
                    print(f"🔍 Found LOCAL ChatterBox F5-TTS at: {local_chatterbox_path}")
                    # Add local ChatterBox to Python path
                    import sys
                    if str(local_chatterbox_path) not in sys.path:
                        sys.path.insert(0, str(local_chatterbox_path))
                    
                    # Try to import the ChatterBox F5-TTS implementation
                    try:
                        import f5_tts
                        self.use_comfy_f5 = True
                        self.chatterbox_path = local_chatterbox_path
                        print("✅ Using LOCAL ChatterBox F5-TTS implementation")
                    except ImportError:
                        print("⚠️ Local ChatterBox F5-TTS found but import failed, trying external ComfyUI")
                        raise ImportError("Local ChatterBox F5-TTS import failed")
                
                # Priority 2: External ComfyUI installation (for development)
                elif comfy_f5_path.exists():
                    print(f"🔍 Found EXTERNAL ComfyUI F5-TTS at: {comfy_f5_path}")
                    # Add ComfyUI F5-TTS to Python path
                    import sys
                    if str(comfy_f5_path) not in sys.path:
                        sys.path.insert(0, str(comfy_f5_path))
                    
                    # Try to import the ComfyUI F5-TTS implementation
                    try:
                        import f5_tts
                        self.use_comfy_f5 = True
                        self.chatterbox_path = comfy_f5_path
                        print("✅ Using EXTERNAL ComfyUI F5-TTS implementation")
                    except ImportError:
                        print("⚠️ External ComfyUI F5-TTS found but import failed")
                        raise ImportError("External ComfyUI F5-TTS import failed")
                else:
                    # Neither local nor external found, fall back to standard F5-TTS
                    # Try standard F5-TTS package
                    from f5_tts.api import F5TTS
                    from f5_tts.model.dataset import load_dataset
                    from f5_tts.infer.utils_infer import preprocess_ref_audio_text, infer_process
                    self.use_comfy_f5 = False
                    print("✅ Using standard F5-TTS package")
                
            except ImportError as import_error:
                print(f"❌ F5-TTS import failed: {import_error}")
                # Try to install F5-TTS dependencies
                if self.try_install_f5_tts():
                    print("🔄 Retrying after installation...")
                    from f5_tts.api import F5TTS
                    self.use_comfy_f5 = False
                else:
                    raise ImportError("F5-TTS package not found and installation failed. Please install F5-TTS manually.")
            
            if progress_callback:
                progress_callback("Initializing model components...", 0.3)
            
            # Initialize the model based on available implementation
            if hasattr(self, 'use_comfy_f5') and self.use_comfy_f5:
                # Set voices path based on which chatterbox we're using
                if hasattr(self, 'chatterbox_path'):
                    self.comfy_voices_path = self.chatterbox_path / "voices_examples"
                    print(f"🎤 Voice path set to: {self.comfy_voices_path}")
                # Use ComfyUI F5-TTS
                self.model = self.init_comfy_f5_model(model_name)
            else:
                # Use standard F5-TTS with updated API and CPU device
                try:
                    import os
                    # Force CPU usage to avoid CUDA compatibility issues
                    os.environ['CUDA_VISIBLE_DEVICES'] = ''
                    
                    from f5_tts.api import F5TTS
                    # Try the new simplified initialization with explicit CPU device
                    self.model = F5TTS()
                    print("✅ Using F5TTS() default initialization")
                    print(f"✅ F5TTS model loaded with infer method available: {hasattr(self.model, 'infer')}")
                except Exception as e:
                    print(f"⚠️ F5TTS initialization failed: {e}")
                    # Fallback to mock generation
                    self.model = None
                    self.model_loaded = False
                    return
            
            if progress_callback:
                progress_callback("Model loaded successfully", 1.0)
            
            self.model_loaded = True
            print(f"✅ F5-TTS model '{model_name}' loaded successfully on {self.device}")
            
        except Exception as e:
            print(f"❌ Failed to load F5-TTS model: {e}")
            # For development, we'll create a mock model
            self.model_loaded = True
            self.use_comfy_f5 = False
            if progress_callback:
                progress_callback("Using mock TTS for development", 1.0)
    
    def set_reference_audio(self, audio_path, reference_text: str = ""):
        """Set reference audio for voice cloning"""
        # Convert to Path if string
        if isinstance(audio_path, str):
            audio_path = Path(audio_path)
            
        if not audio_path.exists():
            raise FileNotFoundError(f"Reference audio file not found: {audio_path}")
        
        try:
            # Store the path for ChatterBox F5-TTS
            self.reference_audio_path = str(audio_path)
            
            # If no reference text provided, try to load from ComfyUI .reference.txt file
            if not reference_text.strip():
                ref_text_file = audio_path.parent / f"{audio_path.stem}.reference.txt"
                if ref_text_file.exists():
                    try:
                        with open(ref_text_file, 'r', encoding='utf-8') as f:
                            reference_text = f.read().strip()
                        print(f"✅ Loaded reference text from {ref_text_file.name}")
                    except Exception as e:
                        print(f"⚠️ Failed to read reference text file: {e}")
                        reference_text = ""
            
            # Load and preprocess reference audio for compatibility
            try:
                # Use soundfile to avoid TorchCodec dependency issues
                audio_data, sample_rate = sf.read(str(audio_path))
                # Convert to tensor and add channel dimension if needed
                audio_data = torch.from_numpy(audio_data).float()
                if len(audio_data.shape) == 1:
                    audio_data = audio_data.unsqueeze(0)  # Add channel dimension
                elif len(audio_data.shape) == 2:
                    audio_data = audio_data.transpose(0, 1)  # [samples, channels] -> [channels, samples]
            except Exception as e:
                print(f"⚠️ Soundfile loading failed, trying torchaudio: {e}")
                # Fallback to torchaudio with explicit backend
                try:
                    audio_data, sample_rate = torchaudio.load(str(audio_path), backend="soundfile")
                except Exception as e2:
                    # Final fallback - try without backend specification
                    audio_data, sample_rate = torchaudio.load(str(audio_path))
            
            # Convert to mono if stereo
            if audio_data.shape[0] > 1:
                audio_data = torch.mean(audio_data, dim=0, keepdim=True)
            
            # Resample if needed
            if sample_rate != self.sample_rate:
                resampler = torchaudio.transforms.Resample(sample_rate, self.sample_rate)
                audio_data = resampler(audio_data)
            
            self.reference_audio = audio_data
            self.reference_text = reference_text.strip() if reference_text else ""
            
            print(f"✅ Reference audio set: {audio_path.name}")
            if self.reference_text:
                print(f"✅ Reference text: {self.reference_text[:100]}{'...' if len(self.reference_text) > 100 else ''}")
            
        except Exception as e:
            raise RuntimeError(f"Failed to load reference audio: {e}")
    
    def preprocess_text(self, text: str) -> str:
        """Preprocess text for TTS"""
        # Clean up text
        text = text.strip()
        
        # Remove multiple spaces
        text = re.sub(r'\s+', ' ', text)
        
        # Ensure sentence ends with punctuation
        if text and not text[-1] in '.!?':
            text += '.'
        
        return text
    
    def generate_speech_mock(self, text: str, output_path: Path, params: Dict[str, Any]):
        """Mock TTS generation for development purposes"""
        print(f"🎭 Mock TTS: Generating audio for '{text[:50]}...'")
        
        # Create a simple sine wave as placeholder
        duration = max(len(text) * 0.1, 1.0)  # Rough duration based on text length
        sample_rate = 24000
        t = np.linspace(0, duration, int(sample_rate * duration))
        
        # Create a simple audio signal (sine wave with some variation)
        frequency = 440 + (len(text) % 200)  # Vary frequency based on text
        audio = 0.3 * np.sin(2 * np.pi * frequency * t)
        
        # Add some envelope
        envelope = np.exp(-t / duration)
        audio = audio * envelope
        
        # Save as WAV
        sf.write(str(output_path), audio, sample_rate)
        print(f"✅ Mock audio saved: {output_path}")
    
    def generate_speech(self, text: str, output_path: Path, params: Optional[Dict[str, Any]] = None) -> bool:
        """Generate speech from text"""
        if not self.model_loaded:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        # Use default params if none provided
        if params is None:
            params = self.default_params.copy()
        
        # Preprocess text
        processed_text = self.preprocess_text(text)
        if not processed_text:
            raise ValueError("Empty text after preprocessing")
        
        try:
            # Set seed for reproducibility
            if params.get('seed'):
                torch.manual_seed(params['seed'])
                if torch.cuda.is_available():
                    torch.cuda.manual_seed(params['seed'])
            
            # Check if we have a real F5-TTS implementation
            if hasattr(self, 'use_comfy_f5') and self.use_comfy_f5 and hasattr(self, 'model') and self.model:
                # Use ComfyUI F5-TTS implementation
                return self.generate_speech_comfy(processed_text, output_path, params)
            elif self.model and not isinstance(self.model, str):
                # Use standard F5-TTS implementation
                return self.generate_speech_standard(processed_text, output_path, params)
            else:
                # Fall back to mock generation
                self.generate_speech_mock(processed_text, output_path, params)
                return True
            
        except Exception as e:
            print(f"❌ TTS generation failed: {e}")
            # Fall back to mock generation
            try:
                self.generate_speech_mock(processed_text, output_path, params)
                return True
            except:
                return False
    
    def generate_speech_comfy(self, text: str, output_path: Path, params: Dict[str, Any]) -> bool:
        """Generate speech using ChatterBox F5-TTS implementation"""
        try:
            print(f"🎤 ChatterBox F5-TTS: Generating '{text[:50]}...'")
            
            # Check if we have a real ChatterBox model
            if hasattr(self.model, 'generate') and hasattr(self.model, 'f5tts_model'):
                # Ensure we have proper reference audio and text
                if not hasattr(self, 'reference_audio_path') or not self.reference_audio_path:
                    print("⚠️ No reference audio set, selecting default ComfyUI voice")
                    # Use a sample reference audio from the voices_examples
                    voices = self.get_available_comfy_voices()
                    if voices:
                        selected_voice = voices[0]
                        self.reference_audio_path = selected_voice['path']
                        self.reference_text = selected_voice.get('reference_text', '')
                        print(f"🎤 Auto-selected voice: {selected_voice['name']}")
                        print(f"🎤 Reference text: {self.reference_text[:100]}{'...' if len(self.reference_text) > 100 else ''}")
                    else:
                        print("❌ No reference voices available, falling back to mock")
                        self.generate_speech_mock(text, output_path, params)
                        return True
                
                # Ensure we have reference text
                if not self.reference_text.strip():
                    print("⚠️ No reference text available, trying to find it")
                    # Try to find reference text file for current audio
                    if hasattr(self, 'reference_audio_path'):
                        audio_path = Path(self.reference_audio_path)
                        ref_text_file = audio_path.parent / f"{audio_path.stem}.reference.txt"
                        if ref_text_file.exists():
                            try:
                                with open(ref_text_file, 'r', encoding='utf-8') as f:
                                    self.reference_text = f.read().strip()
                                print(f"✅ Loaded reference text from {ref_text_file.name}")
                            except Exception as e:
                                print(f"⚠️ Failed to read reference text: {e}")
                    
                    # If still no reference text, this is an error condition
                    if not self.reference_text.strip():
                        print("❌ No reference text available - F5-TTS requires reference text")
                        # Fall back to mock generation
                        self.generate_speech_mock(text, output_path, params)
                        return True
                
                # Generate audio with ChatterBox F5-TTS using proper reference text
                print(f"🎵 Using reference text: '{self.reference_text[:100]}...'")
                wav_tensor = self.model.generate(
                    text=text,
                    ref_audio_path=str(self.reference_audio_path),
                    ref_text=self.reference_text,  # Use actual reference text, not default
                    temperature=params.get('temperature', 0.8),
                    speed=params.get('speed', 1.0),
                    target_rms=params.get('target_rms', 0.1),
                    cross_fade_duration=params.get('cross_fade_duration', 0.15),
                    nfe_step=params.get('nfe_step', 32),
                    cfg_strength=params.get('cfg_strength', 2.0)
                )
                
                # Convert tensor to numpy and save
                if hasattr(wav_tensor, 'cpu'):
                    wav_numpy = wav_tensor.cpu().numpy()
                else:
                    wav_numpy = wav_tensor
                
                # Ensure correct shape (should be 1D for mono audio)
                if wav_numpy.ndim == 2:
                    wav_numpy = wav_numpy.squeeze()
                
                # Save as WAV file
                sf.write(str(output_path), wav_numpy, self.sample_rate)
                print(f"✅ ChatterBox F5-TTS audio saved: {output_path}")
                return True
            else:
                # Fall back to enhanced mock generation
                self.generate_speech_mock(text, output_path, params)
                print(f"✅ Mock F5-TTS audio saved: {output_path}")
                return True
            
        except Exception as e:
            print(f"❌ ChatterBox F5-TTS generation failed: {e}")
            import traceback
            traceback.print_exc()
            # Fall back to mock generation
            self.generate_speech_mock(text, output_path, params)
            return True
    
    def generate_speech_standard(self, text: str, output_path: Path, params: Dict[str, Any]) -> bool:
        """Generate speech using standard F5-TTS"""
        try:
            print(f"🎵 Standard F5-TTS: Generating '{text[:50]}...'")
            
            # Use the F5-TTS API correctly with infer method
            if self.reference_audio is not None and self.reference_text:
                # Use voice cloning with reference audio
                try:
                    # F5-TTS uses the infer method, not generate
                    wav, sr, spec = self.model.infer(
                        ref_file=self.reference_audio if isinstance(self.reference_audio, str) else str(self.reference_audio),
                        ref_text=self.reference_text,
                        gen_text=text,
                        file_wave=str(output_path),
                        seed=params.get('seed', None),
                        remove_silence=params.get('remove_silence', False)
                    )
                    print(f"✅ F5-TTS voice cloning audio saved: {output_path}")
                    return True
                except Exception as e:
                    print(f"⚠️ Voice cloning failed: {e}, trying without reference")
                    # Try without reference audio
                    wav, sr, spec = self.model.infer(
                        gen_text=text,
                        file_wave=str(output_path),
                        seed=params.get('seed', None)
                    )
                    print(f"✅ F5-TTS default voice audio saved: {output_path}")
                    return True
            else:
                # Use default voice - F5-TTS will use its default reference
                wav, sr, spec = self.model.infer(
                    gen_text=text,
                    file_wave=str(output_path),
                    seed=params.get('seed', None)
                )
                print(f"✅ F5-TTS default voice audio saved: {output_path}")
                return True
                
        except Exception as e:
            print(f"❌ Standard F5-TTS generation failed: {e}")
            # Fall back to mock generation
            self.generate_speech_mock(text, output_path, params)
            return True
            return False
    
    def add_silence_padding(self, audio_path: Path, start_silence: float = 0.5, end_silence: float = 0.5):
        """Add silence padding to the beginning and end of an audio file
        
        Args:
            audio_path: Path to the audio file to modify
            start_silence: Seconds of silence to add at the beginning
            end_silence: Seconds of silence to add at the end
        """
        try:
            # Load the audio file
            audio_data, sample_rate = sf.read(str(audio_path))
            
            # Calculate silence samples
            start_samples = int(start_silence * sample_rate)
            end_samples = int(end_silence * sample_rate)
            
            # Create silence arrays
            if len(audio_data.shape) > 1:  # Stereo
                start_silence_array = np.zeros((start_samples, audio_data.shape[1]))
                end_silence_array = np.zeros((end_samples, audio_data.shape[1]))
            else:  # Mono
                start_silence_array = np.zeros(start_samples)
                end_silence_array = np.zeros(end_samples)
            
            # Concatenate: start_silence + audio + end_silence
            padded_audio = np.concatenate([start_silence_array, audio_data, end_silence_array])
            
            # Save back to the same file
            sf.write(str(audio_path), padded_audio, sample_rate)
            
            print(f"✅ Added silence padding: {start_silence}s start, {end_silence}s end - {audio_path.name}")
            
        except Exception as e:
            print(f"⚠️ Failed to add silence padding to {audio_path.name}: {e}")
    
    def merge_audio_files(self, audio_files: List[Path], output_dir: Path, params: Dict[str, Any]) -> Optional[Path]:
        """Merge multiple audio files into one combined file
        
        Args:
            audio_files: List of audio file paths to merge
            output_dir: Directory to save the merged file
            params: Parameters containing silence settings
            
        Returns:
            Path to the merged file, or None if failed
        """
        try:
            if not audio_files:
                return None
            
            # Sort files by filename to ensure correct order
            sorted_files = sorted(audio_files, key=lambda x: x.name)
            
            merged_audio_segments = []
            sample_rate = None
            
            # Load and concatenate all audio files
            for i, audio_file in enumerate(sorted_files):
                try:
                    audio_data, sr = sf.read(str(audio_file))
                    
                    # Set sample rate from first file
                    if sample_rate is None:
                        sample_rate = sr
                    elif sr != sample_rate:
                        print(f"⚠️ Sample rate mismatch in {audio_file.name}: {sr} vs {sample_rate}")
                        continue
                    
                    merged_audio_segments.append(audio_data)
                    
                    # Add silence between sentences (not after the last one)
                    if i < len(sorted_files) - 1:
                        silence_duration = 0.3  # 0.3 seconds between sentences
                        silence_samples = int(silence_duration * sample_rate)
                        
                        if len(audio_data.shape) > 1:  # Stereo
                            silence = np.zeros((silence_samples, audio_data.shape[1]))
                        else:  # Mono
                            silence = np.zeros(silence_samples)
                        
                        merged_audio_segments.append(silence)
                    
                except Exception as e:
                    print(f"⚠️ Skipping {audio_file.name}: {e}")
                    continue
            
            if not merged_audio_segments:
                return None
            
            # Concatenate all segments
            merged_audio = np.concatenate(merged_audio_segments)
            
            # Apply overall silence padding if enabled
            if params.get('add_silence_padding', False):
                start_silence = params.get('start_silence', 0.3)
                end_silence = params.get('end_silence', 0.5)
                
                # Calculate silence samples
                start_samples = int(start_silence * sample_rate)
                end_samples = int(end_silence * sample_rate)
                
                # Create silence arrays
                if len(merged_audio.shape) > 1:  # Stereo
                    start_silence_array = np.zeros((start_samples, merged_audio.shape[1]))
                    end_silence_array = np.zeros((end_samples, merged_audio.shape[1]))
                else:  # Mono
                    start_silence_array = np.zeros(start_samples)
                    end_silence_array = np.zeros(end_samples)
                
                # Add overall padding
                merged_audio = np.concatenate([start_silence_array, merged_audio, end_silence_array])
            
            # Generate output filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            merged_filename = f"merged_audio_{timestamp}.wav"
            merged_path = output_dir / merged_filename
            
            # Save merged audio
            sf.write(str(merged_path), merged_audio, sample_rate)
            
            return merged_path
            
        except Exception as e:
            print(f"❌ Failed to merge audio files: {e}")
            return None
    
    def process_sentences(self, 
                         sentences: List[str], 
                         output_dir: Path, 
                         params: Optional[Dict[str, Any]] = None,
                         broll_config: Optional[Dict[str, Any]] = None,
                         progress_callback: Optional[Callable[[str, float], None]] = None) -> Dict[str, Any]:
        """Process multiple sentences and generate audio files - individual or combined based on settings"""
        
        if not sentences:
            raise ValueError("No sentences provided")
        
        output_dir.mkdir(parents=True, exist_ok=True)
        params = params or {}
        
        # Check if we should generate individual files or one combined file
        generate_individual = params.get('generate_individual_files', True)
        
        if not generate_individual:
            # Generate one long audio file with all sentences combined
            return self._process_combined_audio(sentences, output_dir, params, broll_config, progress_callback)
        
        # Original behavior: generate individual files
        individual_dir = output_dir / "individual_files"
        individual_dir.mkdir(exist_ok=True)
        
        results = {
            'success_count': 0,
            'failed_count': 0,
            'output_files': [],
            'errors': []
        }
        
        total_sentences = len(sentences)
        
        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if not sentence:
                continue
            
            try:
                # Create output filename
                sentence_clean = re.sub(r'[^\w\s-]', '', sentence)[:50]  # Clean for filename
                sentence_clean = re.sub(r'\s+', '_', sentence_clean)
                output_filename = f"{i+1:03d}_{sentence_clean}.wav"
                output_path = individual_dir / output_filename
                
                if progress_callback:
                    progress_callback(f"Generating: {sentence[:50]}...", i / total_sentences)
                
                # Generate speech
                success = self.generate_speech(sentence, output_path, params)
                
                if success:
                    # Add silence padding if enabled
                    if params.get('add_silence_padding', True):
                        start_silence = params.get('start_silence', 0.5)  # Default 0.5 seconds
                        end_silence = params.get('end_silence', 0.5)    # Default 0.5 seconds
                        self.add_silence_padding(output_path, start_silence, end_silence)
                    
                    results['success_count'] += 1
                    results['output_files'].append(output_path)
                    print(f"✅ Generated {i+1}/{total_sentences}: {output_filename}")
                else:
                    results['failed_count'] += 1
                    results['errors'].append(f"Sentence {i+1}: Generation failed")
                    
            except Exception as e:
                results['failed_count'] += 1
                error_msg = f"Sentence {i+1}: {str(e)}"
                results['errors'].append(error_msg)
                print(f"❌ Failed {i+1}/{total_sentences}: {error_msg}")
        
        # Process B-roll if enabled
        broll_data = {}
        if broll_config and broll_config.get('generator'):
            try:
                if progress_callback:
                    progress_callback("Generating B-roll videos...", 0.95)
                
                broll_generator = broll_config['generator']
                
                # Use the BRollGenerator's main method to process all sentences
                sentence_list = [sentence.strip() for sentence in sentences if sentence.strip()]
                broll_results = broll_generator.generate_broll_data(sentence_list)
                
                # Convert to the expected format with sentence_index
                for i, sentence in enumerate(sentence_list):
                    if i in broll_results:
                        broll_data[i] = broll_results[i].copy()  # Copy the original data
                        broll_data[i]['sentence_index'] = i  # Add the missing sentence_index
                
                print(f"✅ Generated B-roll data for {len(broll_data)} sentences")
                
                # Create combined videos if B-roll data exists and we have audio files
                if broll_data and results['output_files']:
                    try:
                        if progress_callback:
                            progress_callback("Creating combined B-roll videos...", 0.97)
                        
                        # Create combined videos directory
                        combined_dir = Path(output_dir) / "combined_videos"
                        combined_videos = broll_generator.create_combined_videos(
                            broll_data, 
                            [str(f) for f in results['output_files']], 
                            str(combined_dir)
                        )
                        
                        if combined_videos:
                            results['combined_videos'] = combined_videos
                            print(f"✅ Created {len(combined_videos)} combined B-roll videos")
                        
                    except Exception as e:
                        results['errors'].append(f"Combined video creation failed: {str(e)}")
                        print(f"❌ Combined video creation failed: {e}")
                
            except Exception as e:
                results['errors'].append(f"B-roll generation failed: {str(e)}")
                print(f"❌ B-roll generation failed: {e}")

        if progress_callback:
            progress_callback("Processing complete", 1.0)
        
        # Add B-roll data to results
        results['broll_data'] = broll_data
        
        return results
    
    def _process_combined_audio(self, 
                               sentences: List[str], 
                               output_dir: Path, 
                               params: Dict[str, Any],
                               broll_config: Optional[Dict[str, Any]] = None,
                               progress_callback: Optional[Callable[[str, float], None]] = None) -> Dict[str, Any]:
        """Generate one long audio file from all sentences combined"""
        
        results = {
            'success_count': 0,
            'failed_count': 0,
            'output_files': [],
            'errors': []
        }
        
        try:
            if progress_callback:
                progress_callback("Combining text for single audio generation...", 0.1)
            
            # Combine all sentences with appropriate spacing
            combined_text = " ".join(sentence.strip() for sentence in sentences if sentence.strip())
            
            if not combined_text:
                raise ValueError("No valid text content to process")
            
            # Create output filename for combined audio
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"combined_audio_{timestamp}.wav"
            output_path = output_dir / output_filename
            
            if progress_callback:
                progress_callback("Generating single audio file...", 0.3)
            
            # Generate the combined audio
            success = self.generate_speech(combined_text, output_path, params)
            
            if success:
                # Add silence padding if enabled
                if params.get('add_silence_padding', True):
                    start_silence = params.get('start_silence', 0.5)
                    end_silence = params.get('end_silence', 0.5)
                    self.add_silence_padding(output_path, start_silence, end_silence)
                
                results['success_count'] = 1
                results['output_files'].append(output_path)
                
                if progress_callback:
                    progress_callback("Single audio file generated successfully", 0.9)
                
                print(f"✅ Generated combined audio: {output_filename}")
            else:
                results['failed_count'] = 1
                results['errors'].append("Failed to generate combined audio file")
        
        except Exception as e:
            results['failed_count'] = 1
            results['errors'].append(f"Error generating combined audio: {str(e)}")
            print(f"❌ Error generating combined audio: {e}")
        
        # Handle B-roll generation if configured
        if broll_config and broll_config.get('generator') and results['success_count'] > 0:
            try:
                if progress_callback:
                    progress_callback("Generating B-roll videos...", 0.95)
                
                broll_generator = broll_config['generator']
                sentence_list = [sentence.strip() for sentence in sentences if sentence.strip()]
                broll_results = broll_generator.generate_broll_data(sentence_list)
                
                # Store B-roll data for the combined audio
                results['broll_data'] = {0: broll_results}  # Single index for combined audio
                
            except Exception as e:
                print(f"⚠️ B-roll generation failed: {e}")
                results['errors'].append(f"B-roll generation error: {str(e)}")
        
        if progress_callback:
            progress_callback("Complete", 1.0)
        
        return results
    
    def get_available_models(self) -> List[str]:
        """Get list of available F5-TTS models"""
        return [
            "E2TTS_Base",
            "F5TTS_Base", 
            "F5TTS_Large"
        ]
    
    def validate_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize TTS parameters"""
        validated = self.default_params.copy()
        
        for key, value in params.items():
            if key in validated:
                # Type validation
                if key == 'seed' and isinstance(value, (int, str)):
                    validated[key] = int(value) if str(value).isdigit() else validated[key]
                elif key in ['temperature', 'speed', 'target_rms', 'cross_fade_duration', 'cfg_strength']:
                    try:
                        validated[key] = float(value)
                    except:
                        pass  # Keep default
                elif key in ['nfe_step', 'max_chars_per_chunk']:
                    try:
                        validated[key] = int(value)
                    except:
                        pass  # Keep default
                elif key in ['enable_chunking']:
                    validated[key] = bool(value)
                else:
                    validated[key] = value
        
        return validated


def install_f5_tts_dependencies():
    """Install F5-TTS dependencies if not available"""
    try:
        import subprocess
        import sys
        
        print("🔄 Installing F5-TTS dependencies...")
        
        # Install F5-TTS (this is a placeholder - actual package may be different)
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "torch", "torchaudio", "soundfile", "librosa"
        ])
        
        print("✅ F5-TTS dependencies installed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to install F5-TTS dependencies: {e}")
        return False