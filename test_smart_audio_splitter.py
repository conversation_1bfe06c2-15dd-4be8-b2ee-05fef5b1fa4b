"""
Test the new smart audio splitter with silence detection and word boundaries
"""
import sys
from pathlib import Path

# Add src to path so we can import modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.core.smart_audio_splitter import split_audio_smartly
    
    def test_smart_splitting():
        """Test the smart audio splitter"""
        print("🧠 Smart Audio Splitter Test")
        print("=" * 40)
        
        # Paths to your files
        original_audio_path = Path("C:/Users/<USER>/Downloads/7habits/autonoe_7habits.wav")
        
        # Create a simple text file with a few test sentences
        test_sentences = [
            "Let's get straight into The 7 Habits of Highly Effective People by <PERSON>.",
            "This book wants your life to run like a Swiss watch, but let's be real, most of us are still figuring out which batteries go where.",
            "Stick around—by the end you'll either be effective or really good at pretending."
        ]
        
        # Create test text file
        test_text_file = Path("test_smart_sentences.txt")
        with open(test_text_file, 'w', encoding='utf-8') as f:
            for sentence in test_sentences:
                f.write(sentence + "\n")
        
        output_dir = Path("smart_audio_test")
        
        # Verify original audio exists
        if not original_audio_path.exists():
            print(f"❌ Original audio file not found: {original_audio_path}")
            print("Please update the path in this script to match your audio file")
            return
        
        print(f"🎵 Audio file: {original_audio_path}")
        print(f"📄 Test sentences: {test_text_file}")
        print(f"📂 Output directory: {output_dir}")
        print()
        
        print("Test sentences:")
        for i, sentence in enumerate(test_sentences, 1):
            print(f"  {i}. {sentence}")
        print()
        
        def progress_update(message, progress):
            print(f"[{progress*100:6.1f}%] {message}")
        
        # Test smart splitting with different settings
        print("🧠 Starting smart audio splitting...")
        print("Features:")
        print("  - Silence detection for clean cuts")
        print("  - Word-level boundary awareness") 
        print("  - No mid-word cuts")
        print("  - Natural pause detection")
        print()
        
        success = split_audio_smartly(
            audio_path=original_audio_path,
            text_file_path=test_text_file,
            output_dir=output_dir,
            similarity_threshold=0.7,  # Slightly higher for accuracy
            output_format="wav",
            silence_threshold=0.01,    # Adjust for your audio (lower = more sensitive)
            progress_callback=progress_update
        )
        
        if success:
            print()
            print("🎉 SUCCESS! Smart audio splitting completed.")
            print(f"📂 Check the output directory: {output_dir}")
            print()
            print("Smart features applied:")
            print("✅ Silence regions detected and used for clean cuts")
            print("✅ Word boundaries respected (no mid-word cuts)")
            print("✅ Natural pauses found between sentences")
            print("✅ Detailed timing metadata saved")
            print()
            print("Files generated:")
            print("- smart_segment_001_*.wav (first sentence)")
            print("- smart_segment_002_*.wav (second sentence)")  
            print("- smart_segment_003_*.wav (third sentence)")
            print("- smart_alignment_info.json (detailed timing data)")
            print()
            print("🎧 Listen to the segments - they should have clean boundaries!")
            print("🔍 Check smart_alignment_info.json to see the timing adjustments")
        else:
            print("❌ Smart audio splitting failed. Check the error messages above.")
        
        # Cleanup
        if test_text_file.exists():
            test_text_file.unlink()
            
        return success
    
    if __name__ == "__main__":
        test_smart_splitting()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the Vid2Frames directory")
    print("Also ensure you have librosa installed: pip install librosa")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()