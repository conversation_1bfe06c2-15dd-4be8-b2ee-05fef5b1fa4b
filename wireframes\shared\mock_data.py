"""
Mock data for wireframe demonstrations
"""

# Sample video files
VIDEO_FILES = [
    {
        "name": "sample_video.mp4",
        "duration": 120.5,
        "width": 1920,
        "height": 1080,
        "fps": 30.0,
        "size_mb": 45.2
    },
    {
        "name": "presentation.avi",
        "duration": 300.8,
        "width": 1280,
        "height": 720,
        "fps": 25.0,
        "size_mb": 89.7
    }
]

# Sample extracted frames
SAMPLE_FRAMES = [
    {"timestamp": 5.2, "similarity_score": 0.95, "quality": 0.88},
    {"timestamp": 12.7, "similarity_score": 0.92, "quality": 0.91},
    {"timestamp": 25.1, "similarity_score": 0.89, "quality": 0.85},
    {"timestamp": 38.6, "similarity_score": 0.94, "quality": 0.89},
    {"timestamp": 52.3, "similarity_score": 0.87, "quality": 0.92},
    {"timestamp": 67.9, "similarity_score": 0.93, "quality": 0.86},
]

# Sample audio files
AUDIO_FILES = [
    {
        "name": "speech_recording.wav",
        "duration": 180.2,
        "format": "WAV",
        "size_mb": 18.5
    },
    {
        "name": "podcast_episode.mp3",
        "duration": 2400.0,
        "format": "MP3",
        "size_mb": 55.8
    }
]

# Sample text content
SAMPLE_TEXT_SENTENCES = [
    "Welcome to our comprehensive guide on artificial intelligence.",
    "Machine learning has revolutionized the way we process data.",
    "Natural language processing enables computers to understand human speech.",
    "Computer vision allows machines to interpret visual information.",
    "The future of AI promises exciting developments in automation.",
    "Ethical considerations must guide AI development and deployment.",
    "Thank you for your attention to this important topic."
]

# Sample split audio results
SAMPLE_SPLIT_RESULTS = [
    {"file": "sentence_001.wav", "duration": 3.2, "text": SAMPLE_TEXT_SENTENCES[0]},
    {"file": "sentence_002.wav", "duration": 4.1, "text": SAMPLE_TEXT_SENTENCES[1]},
    {"file": "sentence_003.wav", "duration": 3.8, "text": SAMPLE_TEXT_SENTENCES[2]},
    {"file": "sentence_004.wav", "duration": 3.5, "text": SAMPLE_TEXT_SENTENCES[3]},
]

# Sample F5-TTS results
SAMPLE_TTS_RESULTS = [
    {"file": "generated_001.wav", "duration": 4.2, "text": SAMPLE_TEXT_SENTENCES[0], "model": "F5TTS_v1_Base"},
    {"file": "generated_002.wav", "duration": 5.1, "text": SAMPLE_TEXT_SENTENCES[1], "model": "F5TTS_v1_Base"},
    {"file": "generated_003.wav", "duration": 4.8, "text": SAMPLE_TEXT_SENTENCES[2], "model": "F5TTS_v1_Base"},
]

# Sample B-roll videos
SAMPLE_BROLL_VIDEOS = [
    {
        "sentence_index": 0,
        "keywords": ["artificial intelligence", "technology", "computer"],
        "selected_video": {
            "id": "video_001",
            "title": "AI Technology Concept",
            "duration": 15.0,
            "quality": "HD",
            "url": "https://example.com/video1.mp4"
        }
    },
    {
        "sentence_index": 1,
        "keywords": ["machine learning", "data", "analysis"],
        "selected_video": {
            "id": "video_002", 
            "title": "Data Analysis Visualization",
            "duration": 12.5,
            "quality": "HD",
            "url": "https://example.com/video2.mp4"
        }
    }
]

# Sample processing states
PROCESSING_STATES = {
    "video": [
        {"step": "Loading video file...", "progress": 0.1},
        {"step": "Extracting frames...", "progress": 0.3},
        {"step": "Analyzing frame similarity...", "progress": 0.6},
        {"step": "Detecting scenes...", "progress": 0.8},
        {"step": "Generating transcriptions...", "progress": 0.9},
        {"step": "Saving results...", "progress": 1.0}
    ],
    "audio_split": [
        {"step": "Loading audio file...", "progress": 0.15},
        {"step": "Loading text file...", "progress": 0.25},
        {"step": "Analyzing text-audio alignment...", "progress": 0.5},
        {"step": "Splitting audio segments...", "progress": 0.8},
        {"step": "Applying audio buffers...", "progress": 0.9},
        {"step": "Saving split files...", "progress": 1.0}
    ],
    "f5_tts": [
        {"step": "Loading F5-TTS model...", "progress": 0.2},
        {"step": "Processing reference audio...", "progress": 0.3},
        {"step": "Generating speech for sentence 1/7...", "progress": 0.4},
        {"step": "Generating speech for sentence 3/7...", "progress": 0.6},
        {"step": "Generating speech for sentence 5/7...", "progress": 0.8},
        {"step": "Applying silence padding...", "progress": 0.9},
        {"step": "Saving audio files...", "progress": 1.0}
    ]
}