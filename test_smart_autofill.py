#!/usr/bin/env python3
"""
Test the smart auto-fill system for custom voice uploads
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.ui.f5_tts_view import F5TTSView
import flet as ft

def test_smart_reference_text():
    """Test the smart reference text generation"""
    f5_view = F5TTSView()
    
    # Test different filename patterns
    test_cases = [
        ("business_presentation.wav", "business"),
        ("narrator_story.wav", "storytelling"),
        ("news_report.wav", "news"),
        ("casual_chat.wav", "conversational"),
        ("formal_speech.wav", "formal"),
        ("random_voice.wav", "generic")
    ]
    
    print("🧪 Testing Smart Reference Text Generation")
    print("=" * 50)
    
    for filename, expected_type in test_cases:
        path = Path(filename)
        result = f5_view.generate_smart_reference_text(path)
        
        print(f"📁 File: {filename}")
        print(f"🎯 Expected type: {expected_type}")
        print(f"📝 Generated text: {result}")
        print("-" * 30)
    
def test_voice_storage():
    """Test voice storage functionality"""
    print("\n🧪 Testing Voice Storage System")
    print("=" * 50)
    
    # Check if user_voices directory can be created
    user_voices_dir = Path("user_voices")
    if user_voices_dir.exists():
        print(f"✅ user_voices directory exists: {user_voices_dir.absolute()}")
        
        # List any existing custom voices
        wav_files = list(user_voices_dir.glob("*.wav"))
        ref_files = list(user_voices_dir.glob("*.reference.txt"))
        
        print(f"📁 Custom voice files: {len(wav_files)}")
        print(f"📝 Reference text files: {len(ref_files)}")
        
        for wav_file in wav_files:
            ref_file = user_voices_dir / f"{wav_file.stem}.reference.txt"
            print(f"   🎤 {wav_file.name} -> {ref_file.name} ({'✅' if ref_file.exists() else '❌'})")
            
    else:
        print(f"📁 user_voices directory will be created when needed")
    
def test_comfy_voices_loading():
    """Test ComfyUI voices loading with custom voices"""
    print("\n🧪 Testing Voice Loading System")
    print("=" * 50)
    
    f5_view = F5TTSView()
    
    # Mock the F5-TTS processor for testing
    class MockF5TTS:
        def get_available_comfy_voices(self):
            return [
                {"name": "morgan_freeman", "path": "/fake/path/morgan_freeman.wav", "format": "wav"},
                {"name": "david_attenborough", "path": "/fake/path/david_attenborough.wav", "format": "wav"}
            ]
    
    f5_view.f5_tts = MockF5TTS()
    
    # Test loading
    f5_view.load_comfy_voices()
    
    print(f"✅ Total voices loaded: {len(f5_view.available_voices)}")
    for voice in f5_view.available_voices:
        voice_type = voice.get('type', 'builtin')
        print(f"   🎤 {voice['name']} ({voice_type})")

def test_ui_compatibility():
    """Test UI component compatibility"""
    print("\n🧪 Testing UI Component Compatibility")
    print("=" * 50)
    
    try:
        # Test if our new methods are properly integrated
        f5_view = F5TTSView()
        
        # Check if methods exist
        methods_to_check = [
            'generate_smart_reference_text',
            'show_save_voice_dialog',
            'save_custom_voice',
            'load_comfy_voices'
        ]
        
        for method_name in methods_to_check:
            if hasattr(f5_view, method_name):
                print(f"✅ Method exists: {method_name}")
            else:
                print(f"❌ Missing method: {method_name}")
        
        # Test smart reference text generation
        test_path = Path("test_business_voice.wav")
        if test_path.exists():
            smart_text = f5_view.generate_smart_reference_text(test_path)
            print(f"✅ Smart text generation works: '{smart_text[:50]}...'")
        else:
            print(f"⚠️ Test audio file not found: {test_path}")
            
    except Exception as e:
        print(f"❌ UI compatibility test failed: {e}")

if __name__ == "__main__":
    print("🚀 Testing Smart Auto-fill System for Custom Voice Uploads")
    print("=" * 60)
    
    test_smart_reference_text()
    test_voice_storage()
    test_comfy_voices_loading()
    test_ui_compatibility()
    
    print("\n" + "=" * 60)
    print("✅ Smart Auto-fill System Testing Complete!")
    print("\nNext steps:")
    print("1. Run the main app: python src/main.py")
    print("2. Navigate to F5-TTS section")
    print("3. Try uploading test_business_voice.wav")
    print("4. Check that reference text auto-fills with business context")
    print("5. Test the save voice dialog functionality")