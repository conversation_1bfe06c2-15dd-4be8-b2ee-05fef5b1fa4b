"""
Test script for the new audio splitting functionality
"""
import sys
from pathlib import Path

# Add src to path so we can import modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.core.audio_splitter import AudioSplitter, split_audio_file
    from src.ui.audio_split_view import AudioSplitView
    print("✅ Audio splitting modules imported successfully!")
    
    # Test basic functionality
    splitter = AudioSplitter()
    print(f"✅ AudioSplitter initialized with device: {splitter.device}")
    
    # Test view creation
    view = AudioSplitView()
    print("✅ AudioSplitView created successfully!")
    
    print("\n🎉 Audio splitting feature is ready to use!")
    print("\nFeatures:")
    print("- Upload audio files (MP3, WAV, M4A, FLAC, OGG)")
    print("- Upload text files with sentences (one per line)")
    print("- AI-powered text-audio alignment using Whisper")
    print("- Configurable similarity thresholds")
    print("- Multiple output formats (WAV, MP3, FLAC)")
    print("- Progress tracking and error handling")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required dependencies:")
    print("pip install -r requirements.txt")
    
except Exception as e:
    print(f"❌ Error: {e}")