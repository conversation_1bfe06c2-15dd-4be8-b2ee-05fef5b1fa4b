"""
Audio transcription using local Whisper models from ComfyUI
"""
import os
import tempfile
from pathlib import Path
from typing import List, Optional, Dict, Any, Callable
import subprocess
import json

try:
    import torch
    import transformers
    from transformers import (
        WhisperProcessor, 
        WhisperForConditionalGeneration,
        Wav2Vec2Processor,
        Wav2Vec2ForCTC
    )
    import torchaudio
    import librosa
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False


class TranscriptionData:
    """Data class for transcription results"""
    def __init__(self, text: str, start_time: float, end_time: float, confidence: float = 0.0):
        self.text = text
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        self.confidence = confidence
        
    def __str__(self):
        return f"[{self.start_time:.1f}s - {self.end_time:.1f}s]: {self.text}"


class LocalTranscriber:
    """Handles audio transcription using local ComfyUI models"""
    
    def __init__(self, comfyui_models_path: Optional[Path] = None):
        self.comfyui_models_path = comfyui_models_path or self._find_comfyui_models()
        self.model = None
        self.processor = None
        self.model_type = None
        self.device = self._select_device()
        
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch and transformers are required for local transcription")
    
    def _select_device(self) -> str:
        """Select the best available device for transcription"""
        if not torch.cuda.is_available():
            print("🖥️  CUDA not available, using CPU for transcription")
            return "cpu"
            
        # Check if current PyTorch supports the GPU
        try:
            # Get GPU info
            gpu_name = torch.cuda.get_device_name()
            device_props = torch.cuda.get_device_properties(0)
            compute_cap = f"sm_{device_props.major}{device_props.minor}"
            memory_gb = device_props.total_memory / 1024**3
            
            print(f"🔍 Detected GPU: {gpu_name}")
            print(f"   Compute capability: {compute_cap}")
            print(f"   Memory: {memory_gb:.1f} GB")
            
            # Test GPU functionality with actual tensor operations
            print("🧪 Testing GPU compatibility...")
            test_tensor = torch.randn(1000, 1000, device='cuda')
            result = torch.matmul(test_tensor, test_tensor)
            result.cpu()  # Move result to CPU to free GPU memory
            del test_tensor, result
            torch.cuda.empty_cache()
            
            print("✅ GPU test successful - using CUDA for transcription")
            print(f"🚀 GPU acceleration enabled! Transcription will be ~10x faster")
            return "cuda"
            
        except RuntimeError as e:
            if "no kernel image is available" in str(e).lower():
                print("⚠️  GPU not compatible with current PyTorch version")
                print("   Using CPU for transcription (will be slower but reliable)")
                print("   To use GPU: Update PyTorch to support your GPU architecture")
                return "cpu"
            else:
                print(f"❌ CUDA error: {e}")
                print("🔄 Falling back to CPU for transcription")
                return "cpu"
        except Exception as e:
            print(f"❌ Unexpected GPU error: {e}")
            print("🔄 Falling back to CPU for transcription")
            return "cpu"
    
    def _find_comfyui_models(self) -> Optional[Path]:
        """Find ComfyUI models directory"""
        possible_paths = [
            Path.home() / "Documents" / "ComfyUI" / "models",
            Path.home() / "ComfyUI" / "models",
            Path("ComfyUI") / "models",
            Path("models")
        ]
        
        for path in possible_paths:
            if path.exists() and (path / "audio_encoders").exists():
                print(f"Found ComfyUI models at: {path}")
                return path
        
        return None
    
    def list_available_models(self) -> Dict[str, Path]:
        """List available audio models in ComfyUI"""
        available_models = {}
        
        if not self.comfyui_models_path:
            return available_models
            
        audio_encoders_path = self.comfyui_models_path / "audio_encoders"
        if audio_encoders_path.exists():
            for model_file in audio_encoders_path.glob("*.safetensors"):
                model_name = model_file.stem
                available_models[model_name] = model_file
                
        return available_models
    
    def load_whisper_model(self, model_path: Optional[Path] = None) -> bool:
        """Load local Whisper model"""
        try:
            if model_path is None:
                # Look for Whisper models
                available_models = self.list_available_models()
                whisper_models = {k: v for k, v in available_models.items() if "whisper" in k.lower()}
                
                if not whisper_models:
                    print("No Whisper models found in ComfyUI models directory")
                    return False
                    
                # Prefer large models for better accuracy
                model_name = next((k for k in whisper_models.keys() if "large" in k.lower()), 
                                list(whisper_models.keys())[0])
                model_path = whisper_models[model_name]
                print(f"Using Whisper model: {model_name}")
            
            # For now, we'll use the standard Whisper model from Hugging Face
            # and later we can implement loading from the safetensors file
            model_name = "openai/whisper-large-v3"
            
            print(f"Loading Whisper model on {self.device}...")
            self.processor = WhisperProcessor.from_pretrained(model_name)
            
            # Optimize loading based on device and available VRAM
            if self.device == "cuda":
                # Use float16 for GPU to save memory and increase speed
                torch_dtype = torch.float16
                print("🚀 Using float16 precision for GPU acceleration")
                
                # Check available GPU memory
                gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
                if gpu_memory_gb >= 16:
                    print(f"✅ GPU has {gpu_memory_gb:.1f}GB VRAM - using optimized settings")
                else:
                    print(f"⚠️  GPU has {gpu_memory_gb:.1f}GB VRAM - may need memory optimization")
            else:
                # Use float32 for CPU for better precision
                torch_dtype = torch.float32
                print("🖥️  Using float32 precision for CPU")
                
            self.model = WhisperForConditionalGeneration.from_pretrained(
                model_name,
                torch_dtype=torch_dtype,
                low_cpu_mem_usage=True,
                use_safetensors=True
            )
            
            if self.device == "cuda":
                self.model = self.model.to(self.device)
                print(f"📱 Model moved to GPU")
            
            self.model_type = "whisper"
            print(f"✅ Whisper model loaded successfully on {self.device}")
            
            # Show memory usage if on GPU
            if self.device == "cuda":
                memory_used = torch.cuda.memory_allocated() / 1024**3
                memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
                print(f"📊 GPU Memory: {memory_used:.1f}GB / {memory_total:.1f}GB used")
            
            return True
            
        except Exception as e:
            print(f"Failed to load Whisper model: {e}")
            return False
    
    def load_wav2vec2_model(self, model_path: Optional[Path] = None) -> bool:
        """Load local Wav2Vec2 model"""
        try:
            if model_path is None:
                available_models = self.list_available_models()
                wav2vec_models = {k: v for k, v in available_models.items() if "wav2vec2" in k.lower()}
                
                if not wav2vec_models:
                    print("No Wav2Vec2 models found")
                    return False
                    
                model_name = list(wav2vec_models.keys())[0]
                model_path = wav2vec_models[model_name]
                print(f"Using Wav2Vec2 model: {model_name}")
            
            # Use a compatible Wav2Vec2 model from Hugging Face for now
            model_name = "facebook/wav2vec2-large-960h-lv60-self"
            
            print(f"Loading Wav2Vec2 model on {self.device}...")
            self.processor = Wav2Vec2Processor.from_pretrained(model_name)
            self.model = Wav2Vec2ForCTC.from_pretrained(model_name)
            
            if self.device == "cuda":
                self.model = self.model.to(self.device)
            
            self.model_type = "wav2vec2"
            print(f"Wav2Vec2 model loaded successfully on {self.device}")
            return True
            
        except Exception as e:
            print(f"Failed to load Wav2Vec2 model: {e}")
            return False
    
    def extract_audio_from_video(self, video_path: Path, start_time: float, end_time: float) -> Optional[Path]:
        """Extract audio segment from video using ffmpeg"""
        try:
            # Create temporary audio file
            temp_audio = Path(tempfile.mktemp(suffix=".wav"))
            
            # Use ffmpeg to extract audio segment
            cmd = [
                "ffmpeg", "-y",
                "-i", str(video_path),
                "-ss", str(start_time),
                "-t", str(end_time - start_time),
                "-vn",  # No video
                "-acodec", "pcm_s16le",  # PCM 16-bit
                "-ar", "16000",  # 16kHz sample rate (good for speech)
                "-ac", "1",  # Mono
                str(temp_audio)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"FFmpeg error: {result.stderr}")
                return None
                
            if temp_audio.exists() and temp_audio.stat().st_size > 0:
                return temp_audio
            else:
                print("No audio extracted (silent segment)")
                return None
                
        except Exception as e:
            print(f"Error extracting audio: {e}")
            return None
    
    def transcribe_audio_file(self, audio_path: Path) -> Optional[str]:
        """Transcribe audio file using loaded model"""
        if not self.model or not self.processor:
            print("No model loaded. Please load a model first.")
            return None
            
        try:
            # Load audio using librosa (more reliable than torchaudio)
            audio_array, sample_rate = librosa.load(audio_path, sr=16000, mono=True)
            
            # Convert to torch tensor
            waveform = torch.tensor(audio_array).unsqueeze(0)
                
            # Convert to numpy for processing
            audio_array = waveform.squeeze().numpy()
            
            if self.model_type == "whisper":
                return self._transcribe_with_whisper(audio_array)
            elif self.model_type == "wav2vec2":
                return self._transcribe_with_wav2vec2(audio_array)
            else:
                print(f"Unknown model type: {self.model_type}")
                return None
                
        except Exception as e:
            print(f"Error transcribing audio: {e}")
            return None
        finally:
            # Clean up temp file
            if audio_path.exists():
                try:
                    audio_path.unlink()
                except:
                    pass
    
    def _transcribe_with_whisper(self, audio_array) -> Optional[str]:
        """Transcribe using Whisper model with GPU optimization"""
        try:
            # Process audio
            inputs = self.processor(
                audio_array, 
                sampling_rate=16000, 
                return_tensors="pt"
            )
            
            # Move inputs to device
            if self.device == "cuda":
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Generate transcription with optimized parameters
            with torch.no_grad():
                if self.device == "cuda":
                    # GPU optimization: use autocast for mixed precision
                    with torch.cuda.amp.autocast():
                        predicted_ids = self.model.generate(
                            inputs["input_features"],
                            forced_decoder_ids=self.processor.get_decoder_prompt_ids(language="en", task="transcribe"),
                            max_length=448,  # Reasonable max length
                            num_beams=1,     # Faster inference with beam search = 1
                            do_sample=False  # Deterministic output
                        )
                else:
                    # CPU transcription
                    predicted_ids = self.model.generate(
                        inputs["input_features"],
                        forced_decoder_ids=self.processor.get_decoder_prompt_ids(language="en", task="transcribe")
                    )
                
            # Decode
            transcription = self.processor.batch_decode(
                predicted_ids, 
                skip_special_tokens=True
            )[0]
            
            # Clean up GPU memory
            if self.device == "cuda":
                del inputs, predicted_ids
                torch.cuda.empty_cache()
            
            return transcription.strip()
            
        except Exception as e:
            print(f"❌ Whisper transcription error: {e}")
            # Clean up on error
            if self.device == "cuda":
                torch.cuda.empty_cache()
            return None
    
    def _transcribe_with_wav2vec2(self, audio_array) -> Optional[str]:
        """Transcribe using Wav2Vec2 model"""
        try:
            # Process audio
            inputs = self.processor(
                audio_array, 
                sampling_rate=16000, 
                return_tensors="pt", 
                padding=True
            )
            
            if self.device == "cuda":
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get logits
            with torch.no_grad():
                logits = self.model(inputs.input_values).logits
                
            # Decode
            predicted_ids = torch.argmax(logits, dim=-1)
            transcription = self.processor.batch_decode(predicted_ids)[0]
            
            return transcription.strip().lower()
            
        except Exception as e:
            print(f"Wav2Vec2 transcription error: {e}")
            return None
    
    def transcribe_video_segments(self, video_path: Path, segments: List[Dict[str, float]], 
                                 progress_callback: Optional[Callable] = None) -> List[TranscriptionData]:
        """Transcribe multiple video segments with progress reporting"""
        transcriptions = []
        total_segments = len(segments)
        
        print(f"Transcribing {total_segments} segments...")
        
        for i, segment in enumerate(segments, 1):
            start_time = segment['start_time']
            end_time = segment['end_time']
            
            print(f"Transcribing segment {i}/{total_segments}: {start_time:.1f}s - {end_time:.1f}s")
            
            # Report progress
            if progress_callback:
                progress_callback({
                    'stage': 'transcribing',
                    'progress': (i - 1) / total_segments,
                    'transcriptions_completed': i - 1,
                    'total_to_transcribe': total_segments,
                    'current_segment': i,
                    'current_start_time': start_time,
                    'current_end_time': end_time
                })
            
            # Extract audio
            audio_path = self.extract_audio_from_video(video_path, start_time, end_time)
            if not audio_path:
                # Create empty transcription for silent segments
                transcriptions.append(TranscriptionData(
                    text="[No audio]",
                    start_time=start_time,
                    end_time=end_time,
                    confidence=0.0
                ))
                print(f"  No audio found for segment {i}")
                continue
            
            # Transcribe audio
            text = self.transcribe_audio_file(audio_path)
            if text:
                transcriptions.append(TranscriptionData(
                    text=text,
                    start_time=start_time,
                    end_time=end_time,
                    confidence=1.0  # We don't have confidence scores from these models
                ))
                print(f"  ✅ Transcribed: '{text[:50]}...' ({len(text)} chars)")
            else:
                transcriptions.append(TranscriptionData(
                    text="[Transcription failed]",
                    start_time=start_time,
                    end_time=end_time,
                    confidence=0.0
                ))
                print(f"  ❌ Transcription failed for segment {i}")
            
            # Report completion of this segment
            if progress_callback:
                progress_callback({
                    'stage': 'transcribing',
                    'progress': i / total_segments,
                    'transcriptions_completed': i,
                    'total_to_transcribe': total_segments,
                    'current_segment': i
                })
        
        # Final progress report
        if progress_callback:
            progress_callback({
                'stage': 'transcription_complete',
                'progress': 1.0,
                'total_transcribed': len(transcriptions),
                'successful_transcriptions': len([t for t in transcriptions if t.text not in ["[No audio]", "[Transcription failed]"]])
            })
        
        return transcriptions
    
    def save_transcriptions_to_file(self, transcriptions: List[TranscriptionData], output_path: Path):
        """Save transcriptions to various formats"""
        # Save as JSON
        json_data = []
        for trans in transcriptions:
            json_data.append({
                "start_time": trans.start_time,
                "end_time": trans.end_time,
                "duration": trans.duration,
                "text": trans.text,
                "confidence": trans.confidence
            })
        
        json_path = output_path.with_suffix('.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        # Save as SRT (subtitle format)
        srt_path = output_path.with_suffix('.srt')
        with open(srt_path, 'w', encoding='utf-8') as f:
            for i, trans in enumerate(transcriptions, 1):
                start_srt = self._seconds_to_srt_time(trans.start_time)
                end_srt = self._seconds_to_srt_time(trans.end_time)
                f.write(f"{i}\n{start_srt} --> {end_srt}\n{trans.text}\n\n")
        
        # Save as plain text
        txt_path = output_path.with_suffix('.txt')
        with open(txt_path, 'w', encoding='utf-8') as f:
            for trans in transcriptions:
                f.write(f"[{trans.start_time:.1f}s - {trans.end_time:.1f}s]: {trans.text}\n")
        
        print(f"Transcriptions saved to:")
        print(f"  JSON: {json_path}")
        print(f"  SRT: {srt_path}")
        print(f"  TXT: {txt_path}")
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"


# Convenience functions
def transcribe_scenes(video_path: Path, scenes_data: List[Dict], output_dir: Path, progress_callback: Optional[Callable] = None) -> List[TranscriptionData]:
    """
    Transcribe all scenes in a video
    
    Args:
        video_path: Path to the video file
        scenes_data: List of scene dictionaries with start_time and end_time
        output_dir: Directory to save transcription files
        progress_callback: Optional callback function for progress updates
    
    Returns:
        List of TranscriptionData objects
    """
    if not TORCH_AVAILABLE:
        print("PyTorch and transformers are required for transcription")
        return []
    
    transcriber = LocalTranscriber()
    
    # Try to load Whisper first (better quality), then Wav2Vec2
    if not transcriber.load_whisper_model():
        if not transcriber.load_wav2vec2_model():
            print("No compatible models found")
            return []
    
    # Convert scenes to the expected format
    segments = [
        {
            'start_time': scene['start_time'],
            'end_time': scene['end_time']
        }
        for scene in scenes_data
    ]
    
    # Transcribe with progress callback
    transcriptions = transcriber.transcribe_video_segments(video_path, segments, progress_callback)
    
    # Save results
    output_file = output_dir / f"{video_path.stem}_transcriptions"
    transcriber.save_transcriptions_to_file(transcriptions, output_file)
    
    return transcriptions