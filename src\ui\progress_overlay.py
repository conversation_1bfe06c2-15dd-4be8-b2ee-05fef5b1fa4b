"""
Alternative progress dialog implementation using Container overlay instead of AlertDialog
"""

import flet as ft

class ProgressOverlay:
    """Custom progress overlay that doesn't rely on AlertDialog"""
    
    def __init__(self, page):
        self.page = page
        self.overlay_container = None
        self.progress_text = None
        self.progress_bar = None
        
    def show(self):
        """Show the progress overlay"""
        print("🔄 Showing progress overlay...")
        
        # Create progress components
        self.progress_text = ft.Text("Initializing...", size=14, color=ft.Colors.WHITE)
        self.progress_bar = ft.ProgressBar(value=0, width=300, color=ft.Colors.BLUE)
        progress_info = ft.Text(
            "This may take several minutes depending on audio length", 
            size=12, color=ft.Colors.WHITE70
        )
        
        # Create semi-transparent overlay
        self.overlay_container = ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Column([
                        ft.Text("🎵 Splitting Audio", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE),
                        self.progress_text,
                        self.progress_bar,
                        progress_info,
                        ft.ElevatedButton(
                            "Cancel",
                            on_click=self.hide,
                            bgcolor=ft.Colors.RED,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=15, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    bgcolor=ft.Colors.GREY_800 if (self.page and hasattr(self.page, 'theme_mode') and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.WHITE,
                    border_radius=10,
                    padding=30,
                    border=ft.border.all(2, ft.Colors.BLUE)
                )
            ], alignment=ft.MainAxisAlignment.CENTER, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            bgcolor=ft.Colors.with_opacity(0.8, ft.Colors.BLACK),
            expand=True,
            alignment=ft.alignment.center
        )
        
        # Add to page overlay
        self.page.overlay.append(self.overlay_container)
        self.page.update()
        print("✅ Progress overlay shown")
        
    def update_progress(self, message: str, progress: float):
        """Update progress"""
        if self.progress_text and self.progress_bar:
            self.progress_text.value = message
            self.progress_bar.value = progress
            self.page.update()
            print(f"📊 Overlay Progress: {message} ({progress:.1%})")
    
    def hide(self, e=None):
        """Hide the progress overlay"""
        if self.overlay_container in self.page.overlay:
            self.page.overlay.remove(self.overlay_container)
            self.page.update()
            print("✅ Progress overlay hidden")