"""
Audio splitter that uses existing alignment data (JSON format)
This bypasses the AI alignment step and uses pre-computed timing data
"""
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Callable, Optional
import re
from datetime import datetime

from ..utils.config import config


class AudioSplitterFromAlignment:
    """Handles audio splitting using pre-existing alignment data"""
    
    def __init__(self):
        # Load settings from config
        self.output_format = config.processing.audio_output_format
        self.buffer_ms = config.processing.audio_buffer_ms
    
    def load_alignment_data(self, alignment_json_path: Path) -> List[Dict]:
        """Load alignment data from JSON file"""
        try:
            with open(alignment_json_path, 'r', encoding='utf-8') as f:
                alignment_data = json.load(f)
            
            print(f"✅ Loaded {len(alignment_data)} segments from alignment file")
            return alignment_data
        
        except Exception as e:
            print(f"❌ Error loading alignment data: {e}")
            raise
    
    def extract_audio_segment(self, audio_path: Path, start_time: float, end_time: float, 
                            output_path: Path, output_format: str = "wav", 
                            buffer_ms: float = 100) -> bool:
        """Extract a segment of audio using FFmpeg with optional buffer padding"""
        try:
            # Add buffer (convert milliseconds to seconds)
            buffer_seconds = buffer_ms / 1000.0
            buffered_start = max(0, start_time - buffer_seconds)  # Don't go below 0
            buffered_end = end_time + buffer_seconds
            
            # Build FFmpeg command
            cmd = [
                "ffmpeg", "-y",  # -y to overwrite existing files
                "-i", str(audio_path),
                "-ss", str(buffered_start),  # Start time with buffer
                "-t", str(buffered_end - buffered_start),  # Duration with buffer
            ]
            
            # Set codec based on output format
            if output_format.lower() == "mp3":
                cmd.extend(["-acodec", "mp3", "-b:a", "192k"])
            elif output_format.lower() == "flac":
                cmd.extend(["-acodec", "flac"])
            elif output_format.lower() == "wav":
                cmd.extend(["-acodec", "pcm_s16le"])
            else:
                # Default to WAV for unknown formats
                cmd.extend(["-acodec", "pcm_s16le"])
                output_format = "wav"
            
            cmd.append(str(output_path))
            
            # Run FFmpeg
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"FFmpeg error: {result.stderr}")
                return False
                
            return output_path.exists() and output_path.stat().st_size > 0
            
        except Exception as e:
            print(f"Error extracting audio segment: {e}")
            return False
    
    def split_audio_from_alignment(self, audio_path: Path, alignment_json_path: Path, 
                                 config: Dict[str, Any], 
                                 progress_callback: Optional[Callable] = None) -> bool:
        """Split audio file using existing alignment data"""
        
        try:
            # Validate inputs first
            if not audio_path:
                raise ValueError("audio_path cannot be None")
            if not alignment_json_path:
                raise ValueError("alignment_json_path cannot be None")
            
            print(f"🎯 Starting alignment-based audio splitting...")
            print(f"   Audio path: {audio_path}")
            print(f"   Alignment path: {alignment_json_path}")
            print(f"   Audio path type: {type(audio_path)}")
            print(f"   Alignment path type: {type(alignment_json_path)}")
            
            output_dir = config["output_dir"]
            output_format = config.get("output_format", "wav")
            buffer_ms = config.get("buffer_ms", 100)  # Default 100ms buffer
            
            # Step 1: Load alignment data
            if progress_callback:
                progress_callback("Loading alignment data...", 0.1)
            
            alignment_data = self.load_alignment_data(alignment_json_path)
            
            if not alignment_data:
                raise Exception("No alignment data found in the JSON file")
            
            total_segments = len(alignment_data)
            
            # Step 2: Create output directory structure
            if progress_callback:
                progress_callback("Preparing output directory...", 0.2)
            
            output_dir.mkdir(exist_ok=True)
            
            # Step 3: Extract audio segments
            successful_extractions = 0
            
            for i, segment in enumerate(alignment_data):
                if progress_callback:
                    progress = 0.2 + (0.8 * (i + 1) / total_segments)
                    segment_text = segment.get("text", f"segment_{i+1}")[:30]
                    progress_callback(
                        f"Extracting segment {i+1}/{total_segments}: '{segment_text}...'",
                        progress
                    )
                
                # Extract timing information
                start_time = segment.get("start_time", 0.0)
                end_time = segment.get("end_time", start_time + 3.0)
                text = segment.get("text", f"segment_{i+1}")
                segment_number = segment.get("segment_number", i+1)
                
                # Generate output filename
                # Sanitize filename
                safe_text = re.sub(r'[^\w\s-]', '', text[:50]).strip()
                safe_text = re.sub(r'\s+', '_', safe_text)
                
                output_filename = f"segment_{segment_number:03d}_{safe_text}.{output_format}"
                output_path = output_dir / output_filename
                
                # Extract segment with configurable buffer
                success = self.extract_audio_segment(
                    audio_path, start_time, end_time, 
                    output_path, output_format, buffer_ms=buffer_ms
                )
                
                if success:
                    successful_extractions += 1
                    print(f"  ✅ Extracted: {output_path.name}")
                else:
                    print(f"  ❌ Failed to extract: {output_filename}")
            
            # Step 4: Copy alignment info to output directory
            output_alignment_path = output_dir / "alignment_info.json"
            with open(output_alignment_path, 'w', encoding='utf-8') as f:
                json.dump(alignment_data, f, indent=2, ensure_ascii=False)
            
            # Summary
            print(f"\n✅ Audio splitting complete!")
            print(f"   Total segments: {total_segments}")
            print(f"   Successful extractions: {successful_extractions}")
            print(f"   Output directory: {output_dir}")
            print(f"   Alignment data: {output_alignment_path}")
            
            if progress_callback:
                progress_callback(f"Complete! Extracted {successful_extractions}/{total_segments} segments", 1.0)
            
            return successful_extractions > 0
            
        except Exception as e:
            error_msg = f"Error during audio splitting: {e}"
            print(f"❌ {error_msg}")
            if progress_callback:
                progress_callback(f"Error: {e}", 1.0)
            return False
    
    def update_settings(self, buffer_ms: int = None, output_format: str = None):
        """Update audio splitting settings and save to config"""
        if buffer_ms is not None:
            self.buffer_ms = buffer_ms
            config.processing.audio_buffer_ms = buffer_ms
        
        if output_format is not None:
            self.output_format = output_format
            config.processing.audio_output_format = output_format
        
        # Save config
        config.save()


# Convenience function
def split_audio_from_alignment_file(audio_path: Path, alignment_json_path: Path, 
                                  output_dir: Path, output_format: str = "wav",
                                  buffer_ms: float = 100, 
                                  progress_callback: Optional[Callable] = None) -> bool:
    """
    Convenience function to split audio file using existing alignment data
    
    Args:
        audio_path: Path to the audio file
        alignment_json_path: Path to the alignment JSON file
        output_dir: Directory to save the split audio files
        output_format: Output audio format ("wav", "mp3", "flac")
        buffer_ms: Buffer duration in milliseconds to add at start/end of each segment
        progress_callback: Optional callback for progress updates
    
    Returns:
        True if splitting was successful, False otherwise
    """
    try:
        # Validate inputs
        if not audio_path:
            raise ValueError("audio_path cannot be None")
        if not alignment_json_path:
            raise ValueError("alignment_json_path cannot be None")
        if not output_dir:
            raise ValueError("output_dir cannot be None")
            
        print(f"🔧 Convenience function called with:")
        print(f"   Audio: {audio_path} (type: {type(audio_path)})")
        print(f"   Alignment: {alignment_json_path} (type: {type(alignment_json_path)})")
        print(f"   Output: {output_dir} (type: {type(output_dir)})")
        
        splitter = AudioSplitterFromAlignment()
        
        config_dict = {
            'output_dir': output_dir,
            'output_format': output_format,
            'buffer_ms': buffer_ms
        }
        
        return splitter.split_audio_from_alignment(
            audio_path, alignment_json_path, config_dict, progress_callback
        )
        
    except Exception as e:
        print(f"❌ Error in split_audio_from_alignment_file: {e}")
        import traceback
        traceback.print_exc()
        return False