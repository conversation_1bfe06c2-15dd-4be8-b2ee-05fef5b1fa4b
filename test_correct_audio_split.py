"""
Test script to re-split the original audio using the existing alignment data
This will help verify if the alignment data is correct
"""
import sys
import json
from pathlib import Path

# Add src to path so we can import modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.core.audio_splitter_from_alignment import split_audio_from_alignment_file
    
    def test_resplit_with_alignment():
        """Test re-splitting the original audio using alignment data"""
        print("🔄 Re-splitting Audio with Alignment Data")
        print("=" * 45)
        
        # Paths
        original_audio_path = Path("C:/Users/<USER>/Downloads/7habits/autonoe_7habits.wav")
        alignment_json_path = Path("C:/Users/<USER>/Downloads/7habits/autonoe_7habits_split_20250930_171245/alignment_info.json")
        output_dir = Path("test_corrected_segments")
        
        # Verify files exist
        if not original_audio_path.exists():
            print(f"❌ Original audio file not found: {original_audio_path}")
            return
            
        if not alignment_json_path.exists():
            print(f"❌ Alignment file not found: {alignment_json_path}")
            return
        
        print(f"📁 Original audio: {original_audio_path}")
        print(f"📄 Alignment data: {alignment_json_path}")
        print(f"📂 Output directory: {output_dir}")
        print()
        
        def progress_update(message, progress):
            print(f"[{progress*100:6.1f}%] {message}")
        
        # Re-split using the alignment data
        print("🎯 Re-splitting audio using the alignment data...")
        success = split_audio_from_alignment_file(
            audio_path=original_audio_path,
            alignment_json_path=alignment_json_path,
            output_dir=output_dir,
            output_format="wav",
            buffer_ms=100,  # 100ms buffer
            progress_callback=progress_update
        )
        
        if success:
            print()
            print("🎉 SUCCESS! Audio has been re-split using alignment data.")
            print(f"📂 Check the output directory: {output_dir}")
            print()
            print("Now compare these new segments with your original split:")
            print()
            print("Expected content:")
            print("- segment_001_*.wav should contain: 'Let's get straight into The 7 Habits of Highly Effective People by Stephen R. Covey.'")
            print("- segment_002_*.wav should contain: 'This book wants your life to run like a Swiss watch, but let's be real, most of us are still figuring out which batteries go where.'")
            print("- segment_003_*.wav should contain: 'Stick around—by the end you'll either be effective or really good at pretending.'")
            print()
            print("🎧 Listen to the first few segments to verify they match the expected timing!")
        else:
            print("❌ Re-splitting failed. Check the error messages above.")
            
        return success
    
    if __name__ == "__main__":
        test_resplit_with_alignment()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the Vid2Frames directory")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()