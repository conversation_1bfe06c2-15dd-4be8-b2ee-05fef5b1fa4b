# F5-TTS Voice Generation Feature

## Overview

The F5-TTS feature in Vid2Frames allows you to convert text sentences into individual audio files using advanced text-to-speech technology. This feature is inspired by the ComfyUI chatterbox_srt_voice node and provides similar functionality within the Vid2Frames desktop application.

## Features

### Text Input Options
- **Manual Text Entry**: Type or paste text directly into the text area
- **File Loading**: Load text from .txt files (one sentence per line)
- **Sentence Processing**: Each line/sentence becomes a separate audio file

### Voice Customization
- **Built-in Voice Library**: 10 high-quality voices included (no setup required):
  - **Celebrity Voices**: <PERSON>, <PERSON>, <PERSON>, <PERSON>
  - **Generic Voices**: Professional male/female voices for any content
  - **Variety**: Multiple accents, styles, and speaking patterns
- **Custom Reference Audio**: Upload your own audio file to clone any voice
- **Model Selection**: Choose from different F5-TTS models:
  - E2TTS_Base (default)
  - F5TTS_Base
  - F5TTS_Large

### Advanced Parameters
Based on the ComfyUI implementation, you can adjust:

- **Seed**: Random seed for reproducible generation (default: 1193103530)
- **Temperature**: Controls randomness/creativity (0.1-2.0, default: 0.8)
- **Speed**: Speech speed multiplier (0.5-2.0, default: 1.0)
- **Target RMS**: Audio loudness level (0.01-0.5, default: 0.10)
- **NFE Steps**: Number of flow estimation steps (16-64, default: 32)
- **CFG Strength**: Classifier-free guidance strength (1.0-5.0, default: 2.0)
- **Enable Chunking**: Split long text into chunks for processing
- **Max Chars per Chunk**: Maximum characters per chunk (100-800, default: 400)

## How to Use

### Basic Usage

1. **Navigate to F5-TTS Tab**: Click the "F5-TTS" tab in the navigation rail
2. **Enter Text**: Either:
   - Type text directly in the text area (one sentence per line)
   - Click "Load from File" to select a .txt file
3. **Configure Settings**: Adjust parameters as needed (defaults work well)
4. **Generate**: Click "Generate Speech" to start processing

### Voice Cloning (Optional)

1. **Select Reference Audio**: Click "Browse Audio Files" to select a WAV, MP3, M4A, or FLAC file
2. **Enter Reference Text**: Type the text that matches your reference audio
3. **Generate**: The system will use this voice for all generated audio

### Example Text File Format

Create a .txt file with one sentence per line:

```
Welcome to our text-to-speech system.
This is the second sentence.
Each line becomes a separate audio file.
You can have as many sentences as you need.
```

## Output

- **Individual Files**: Each sentence generates a separate WAV file
- **Organized Naming**: Files are numbered and named based on content
  - Example: `001_Welcome_to_our_text_to_speech_system.wav`
- **Timestamped Folders**: Output saved in folders like `f5_tts_output_20241225_143022`
- **Automatic Opening**: Output folder opens automatically when complete

## Installation

### ✅ **Bundled Voice Library**

Vid2Frames includes **10 professional voices** out-of-the-box:
- **No additional downloads required**
- **Celebrity voices**: Clint Eastwood, David Attenborough, Morgan Freeman, Sophie Anderson
- **Professional voices**: Male/female generic voices for any content
- **Total size**: ~3.6MB (minimal installer impact)

### Required Dependencies

The F5-TTS feature requires additional Python packages:

```bash
# Core audio processing
pip install soundfile librosa numpy scipy

# F5-TTS (if available)
pip install git+https://github.com/SWivid/F5-TTS.git
```

### Automatic Installation

Run the installation script:

```bash
python install_f5_tts.py
```

### Manual Installation

If automatic installation fails:

1. Install core dependencies:
   ```bash
   pip install soundfile librosa numpy scipy torch torchaudio
   ```

2. Try installing F5-TTS from source:
   ```bash
   git clone https://github.com/SWivid/F5-TTS.git
   cd F5-TTS
   pip install -e .
   ```

## Troubleshooting

### Mock Generation Mode

If F5-TTS packages aren't available, the system automatically falls back to "mock generation" mode:
- Creates simple sine wave audio as placeholders
- Maintains all functionality for testing
- Files are generated with proper naming and structure

### GPU Compatibility

- **RTX 5090 Users**: Current PyTorch may not support RTX 5090
- **Automatic Fallback**: System automatically uses CPU if GPU unavailable
- **Performance**: CPU generation is slower but fully functional

### Common Issues

1. **"No text entered"**: Make sure to enter text or load a file
2. **"Model loading failed"**: Dependencies may not be installed correctly
3. **"Reference audio error"**: Check audio file format and encoding

## ComfyUI Integration

This feature is based on the ComfyUI `chatterbox_srt_voice` node and maintains compatibility with:
- Similar parameter ranges and defaults
- Voice reference workflows
- Output file structures

**📖 For detailed technical information about the ComfyUI integration, see:**
- **[ComfyUI F5-TTS Integration Architecture](COMFYUI_F5TTS_INTEGRATION.md)** - Complete technical documentation
- **[ComfyUI ChatterBox README](chatterbox_srt_voice/README.md)** - Original ComfyUI node documentation

### Integration Benefits

- **Self-Contained Voice Library**: 10 high-quality voices bundled with installer
- **ComfyUI Compatibility**: Uses the same voice format as ComfyUI workflows
- **No External Dependencies**: Works without ComfyUI installation
- **Hybrid Architecture**: Automatic fallback between ComfyUI → Standard F5-TTS → Mock generation
- **Expandable**: Users can add more voices to the local library

## Development Notes

### Current Implementation

- **Mock TTS**: Uses sine wave generation for development/testing
- **Real F5-TTS**: Will integrate actual F5-TTS models when available
- **Threading**: All processing runs in background threads
- **Progress Tracking**: Real-time progress updates during generation

### Future Enhancements

- **More Models**: Support for additional TTS models
- **Batch Processing**: Enhanced batch processing capabilities
- **Voice Library**: Built-in collection of reference voices
- **Export Options**: Multiple audio format exports

## File Structure

```
src/
├── core/
│   └── f5_tts.py           # Core F5-TTS processing logic
├── ui/
│   └── f5_tts_view.py      # User interface for F5-TTS
└── ...

# Output structure
f5_tts_output_[timestamp]/
├── 001_First_sentence.wav
├── 002_Second_sentence.wav
└── 003_Third_sentence.wav
```

## Credits

- **F5-TTS**: Based on the F5-TTS project and research
- **ComfyUI Integration**: Inspired by the chatterbox_srt_voice node
- **Vid2Frames**: Integrated into the Vid2Frames desktop application