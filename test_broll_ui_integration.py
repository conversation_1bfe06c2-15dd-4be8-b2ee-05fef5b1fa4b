#!/usr/bin/env python3
"""
Test B-roll Integration in F5-TTS UI
"""

import sys
from pathlib import Path
import flet as ft

# Add src to path  
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main(page: ft.Page):
    page.title = "F5-TTS B-Roll Integration Test"
    page.theme_mode = ft.ThemeMode.DARK
    page.window.width = 1200
    page.window.height = 800
    
    # Mock main window for testing
    class MockMainWindow:
        def __init__(self):
            self.progress_view = None
    
    try:
        print("🔄 Creating F5-TTS View with B-roll integration...")
        from src.ui.f5_tts_view import F5TTSView
        
        # Create main window mock
        main_window = MockMainWindow()
        
        # Create F5-TTS view
        view = F5TTSView()
        view.page = page
        
        # Build the UI
        print("🔧 Building F5-TTS UI with B-roll panel...")
        view_content = view.build()
        
        # Test sample business analyst text
        sample_text = """So you typed into Google, what is a business analyst.
A business analyst is the bridge between people who dream and the people who code.
They translate messy business problems into requirements that developers can understand.
They interview stakeholders, gather requirements, maybe even create some wireframes.
They sit in meetings all day and argue gently with developers about scope.
They write user stories, process flow charts, acceptance criteria, and lots of documents.
You get to shape solutions and become the person who translates business dreams into digital reality."""
        
        view.text_input.value = sample_text
        
        print("🎬 Testing B-roll functionality...")
        
        # Enable B-roll
        view.broll_enabled.value = True
        print(f"   B-roll enabled: {view.broll_enabled.value}")
        
        # Test keyword extraction
        if hasattr(view, 'update_broll_keywords_preview'):
            view.update_broll_keywords_preview(sample_text)
            print(f"   Keywords preview: {view.broll_keywords_preview.value}")
        
        # Test B-roll settings
        print(f"   Quality setting: {view.broll_quality_dropdown.value}")
        print(f"   Results per keyword: {int(view.broll_results_slider.value)}")
        print(f"   B-roll status: {view.broll_status_text.value}")
        
        # Set up page
        page.main_window = main_window
        
        # Create layout
        header = ft.Container(
            content=ft.Column([
                ft.Text(
                    "🎬 F5-TTS + B-roll Integration Test",
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE
                ),
                ft.Text(
                    "Testing the new B-roll video generation feature in F5-TTS tab",
                    size=14,
                    color=ft.Colors.GREY_400
                ),
                ft.Divider(height=20, color=ft.Colors.GREY_600)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
            padding=20,
            margin=ft.margin.only(bottom=10)
        )
        
        # Main content
        content = ft.Column([
            header,
            view_content
        ], scroll=ft.ScrollMode.AUTO, expand=True)
        
        page.add(content)
        
        print("✅ B-roll integration test completed successfully!")
        print("\nℹ️  Instructions:")
        print("   1. The sample business analyst text is pre-loaded")
        print("   2. Enable the 'Auto-generate B-roll videos' checkbox")
        print("   3. Click the expand button to see B-roll settings")
        print("   4. Keywords will be automatically extracted and previewed")
        print("   5. The B-roll system is ready for TTS generation")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        # Show error in UI
        page.add(ft.Text(f"Error: {e}", color=ft.Colors.RED))

if __name__ == "__main__":
    ft.app(target=main)