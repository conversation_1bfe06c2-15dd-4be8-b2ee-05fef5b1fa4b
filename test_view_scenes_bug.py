#!/usr/bin/env python3
"""
Test script to debug the view_scenes functionality
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from ui.results_view import ResultsView

# Create a simple mock SceneData class for testing
class MockSceneData:
    def __init__(self, start_time, end_time, start_frame, end_frame, frame_count):
        self.start_time = start_time
        self.end_time = end_time
        self.start_frame = start_frame  
        self.end_frame = end_frame
        self.frame_count = frame_count
        self.duration = end_time - start_time

# Create a mock processing result with scene data
def create_mock_processing_result():
    """Create mock processing result with scene data"""
    scene1 = MockSceneData(
        start_time=0.0,
        end_time=5.2, 
        start_frame=0,
        end_frame=156,
        frame_count=157
    )
    
    scene2 = MockSceneData(
        start_time=5.2,
        end_time=10.8,
        start_frame=157,
        end_frame=324,
        frame_count=168
    )
    
    return {
        'success': True,
        'job_dir': Path('c:/temp/test_output'),
        'extracted_frames': [],
        'saved_paths': [],
        'detected_scenes': [scene1, scene2],
        'scene_video_paths': [Path('c:/temp/scene_1.mp4'), Path('c:/temp/scene_2.mp4')],
        'processing_time': 45.2
    }

def test_view_scenes():
    """Test the view_scenes functionality"""
    print("Testing view_scenes functionality...")
    
    # Create a mock main window
    class MockMainWindow:
        def __init__(self):
            pass
    
    # Create results view
    main_window = MockMainWindow()
    results_view = ResultsView(main_window)
    
    # Create mock processing result
    mock_result = create_mock_processing_result()
    print(f"Mock result keys: {mock_result.keys()}")
    print(f"Mock scenes: {len(mock_result['detected_scenes'])}")
    
    # Set the results
    results_view.set_results(mock_result)
    
    # Test the detected scenes data
    print(f"Results view detected_scenes: {len(results_view.detected_scenes)}")
    print(f"Results view scene_video_paths: {len(results_view.scene_video_paths)}")
    
    # Test what happens when view_scenes is called
    try:
        # Mock event object
        class MockEvent:
            pass
        
        mock_event = MockEvent()
        results_view.view_scenes(mock_event)
        print("view_scenes called successfully")
        
    except Exception as e:
        print(f"ERROR calling view_scenes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_view_scenes()