# F5-TTS User Guide - New Features

## 🎯 Quick Start Guide

### Silence Padding Feature
**What it does**: Adds professional silence at the start and end of audio files

**How to use**:
1. ✅ Keep "Add Silence Padding" checked (recommended)
2. Adjust sliders:
   - **Start Silence**: 0.3s (default) - Brief pause before speech
   - **End Silence**: 0.5s (default) - Natural conclusion pause
3. Generate audio as normal

**Professional Tips**:
- **0.3s start** = Standard for audiobooks/podcasts
- **0.5s end** = Clean finish without abrupt cutoffs
- **Longer durations** = Dramatic pauses for emphasis

### Audio Merge Feature
**What it does**: Creates both individual sentence files AND one combined file

**How to use**:
1. Check "Merge Audio Files" checkbox
2. Generate your text
3. Get organized output:
   ```
   Your_Output_Folder/
   ├── individual_files/    ← Edit individual sentences
   └── merged/             ← Final combined audio
   ```

**When to use**:
- ✅ **Checked**: Need final audio for sharing/publishing
- ⭕ **Unchecked**: Only need individual files for editing

## 🔧 Settings Explained

### Default Settings (Recommended)
```
✅ Add Silence Padding: ON
   ├── Start Silence: 0.3s
   └── End Silence: 0.5s

⭕ Merge Audio Files: OFF (choose based on need)
```

### Professional Use Cases

| Scenario | Start Silence | End Silence | Merge Files |
|----------|---------------|-------------|-------------|
| **Audiobook Chapter** | 0.5s | 1.0s | ✅ Yes |
| **Podcast Intro** | 0.3s | 0.5s | ✅ Yes |
| **Voice Messages** | 0.2s | 0.3s | ⭕ No |
| **Commercial Audio** | 0.5s | 0.8s | ✅ Yes |
| **Quick Testing** | 0.1s | 0.1s | ⭕ No |

## 📁 Understanding Output Files

### Individual Files Mode (Default)
```
F5_TTS_Output_20250928_143052/
└── individual_files/
    ├── 001_hello_world.wav
    ├── 002_welcome_message.wav
    └── 003_thank_you.wav
```
- Each sentence = separate file
- Easy to re-generate individual parts
- Good for editing and review

### Merged Files Mode (When Enabled)
```
F5_TTS_Output_20250928_143052/
├── individual_files/          ← Still created for backup
│   ├── 001_hello_world.wav
│   ├── 002_welcome_message.wav
│   └── 003_thank_you.wav
└── merged/                    ← NEW: Combined audio
    └── merged_audio_20250928_143052.wav
```
- Individual files + combined file
- 0.3s silence automatically added between sentences
- Overall start/end silence applied to merged file
- Perfect for final delivery

## 🎵 Audio Quality Features

### Automatic Silence Management
- **Between sentences** (merged only): 0.3s gaps for natural flow
- **Start padding**: Your chosen duration before first word
- **End padding**: Your chosen duration after last word
- **Sample rate consistency**: All files use same audio quality

### File Naming Convention
- **Individual**: `001_sentence_preview.wav`, `002_next_sentence.wav`
- **Merged**: `merged_audio_YYYYMMDD_HHMMSS.wav`
- **Timestamps**: Prevent accidental overwrites

## ⚡ Quick Tips

### For Best Results
1. **Keep default silence settings** for most use cases
2. **Use merge for final output** when sharing audio
3. **Leave merge off during testing** to save processing time
4. **Longer silence for dramatic effect** in storytelling
5. **Shorter silence for rapid delivery** in instructions

### Troubleshooting
- **Button appears blue with no text**: Restart the application
- **Sliders show "{value:.1f}"**: Move sliders to refresh display
- **No merged file created**: Check that merge checkbox is enabled
- **Audio cuts off abruptly**: Increase end silence duration

### Performance Notes
- **Merge enabled**: ~10% longer processing time
- **Large texts**: Individual files process faster than one big file
- **Memory usage**: Minimal impact due to smart processing
- **Storage**: Merge doubles file count but provides flexibility

---

**Need help?** All features have tooltips - hover over controls for quick explanations!