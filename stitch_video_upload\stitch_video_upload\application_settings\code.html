<!DOCTYPE html>
<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Vid2Frames - Settings</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            colors: {
              "primary": "#137fec",
              "background-light": "#f6f7f8",
              "background-dark": "#101922",
            },
            fontFamily: {
              "display": ["Inter"],
            },
            borderRadius: {
              "DEFAULT": "0.25rem",
              "lg": "0.5rem",
              "xl": "0.75rem",
              "full": "9999px"
            },
          },
        },
      };
    </script>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
</head>
<body class="bg-background-light dark:bg-background-dark font-display text-gray-800 dark:text-gray-200">
<div class="flex h-full min-h-screen w-full flex-col">
<header class="sticky top-0 z-10 flex h-16 items-center justify-between border-b border-black/10 bg-background-light/80 px-10 backdrop-blur-sm dark:border-white/10 dark:bg-background-dark/80">
<div class="flex items-center gap-4">
<div class="size-6 text-primary">
<svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4H17.3334V17.3334H30.6666V30.6666H44V44H4V4Z" fill="currentColor"></path>
</svg>
</div>
<h1 class="text-xl font-bold text-gray-900 dark:text-white">Vid2Frames</h1>
</div>
<nav class="flex items-center gap-6">
<a class="text-sm font-medium text-gray-600 transition-colors hover:text-primary dark:text-gray-400 dark:hover:text-primary" href="#">Dashboard</a>
<a class="text-sm font-medium text-gray-600 transition-colors hover:text-primary dark:text-gray-400 dark:hover:text-primary" href="#">Projects</a>
<a class="text-sm font-medium text-primary" href="#">Settings</a>
</nav>
<div class="flex items-center gap-4">
<button class="group flex size-10 items-center justify-center rounded-full bg-transparent text-gray-500 transition-colors hover:bg-primary/10 hover:text-primary dark:text-gray-400 dark:hover:bg-primary/20">
<span class="material-symbols-outlined">help</span>
</button>
<div class="size-10 rounded-full bg-cover bg-center" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuByxyCsB0F0ZSGLvyhXHXaFLOCE_BEgFcNXm13f10elInlUb4GaQders1KkCZpT_qAxzuns3MmVYgUuHqBgyQSkGa9Eq1h3sk6TCUfNZLoFpIbgHGAbEzUBcHaHzgYy3pMAcm3ne_OrBzMeX006Q7itLGZ32w7h88AI1YhVXTrbX8Uz-Ee0lCUjmn2gso0P8vJrX8RXdPa0553gX-amdi2zRXdszb3Zs0eC276VfujCVODhJvlUDEzbDbydQoP_WRcQOVO_mRJkQ1g");'></div>
</div>
</header>
<main class="container mx-auto flex-1 px-4 py-12 md:px-8 lg:px-16">
<div class="mx-auto max-w-4xl">
<div class="mb-12">
<h2 class="text-4xl font-extrabold tracking-tighter text-gray-900 dark:text-white">Settings</h2>
<p class="mt-2 text-lg text-gray-500 dark:text-gray-400">Manage your account settings and preferences.</p>
</div>
<div class="space-y-12">
<div class="relative border-t border-black/10 pt-8 dark:border-white/10">
<h3 class="absolute -top-3.5 left-0 bg-background-light px-2 text-lg font-bold text-gray-900 dark:bg-background-dark dark:text-white">Account</h3>
<div class="space-y-6">
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
<label class="group flex flex-col gap-2">
<span class="text-sm font-medium text-gray-700 dark:text-gray-300">Name</span>
<input class="rounded-lg border border-black/10 bg-white/50 px-4 py-2 text-gray-900 transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 dark:border-white/10 dark:bg-black/20 dark:text-white" type="text" value="John Doe"/>
</label>
<label class="group flex flex-col gap-2">
<span class="text-sm font-medium text-gray-700 dark:text-gray-300">Email</span>
<input class="rounded-lg border border-black/10 bg-white/50 px-4 py-2 text-gray-900 transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 dark:border-white/10 dark:bg-black/20 dark:text-white" type="email" value="<EMAIL>"/>
</label>
</div>
<label class="group flex flex-col gap-2">
<span class="text-sm font-medium text-gray-700 dark:text-gray-300">Password</span>
<input class="rounded-lg border border-black/10 bg-white/50 px-4 py-2 text-gray-900 transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 dark:border-white/10 dark:bg-black/20 dark:text-white" type="password" value="••••••••••••"/>
</label>
<div class="flex justify-start">
<button class="rounded-lg bg-primary px-5 py-2.5 text-sm font-bold text-white shadow-md transition-transform duration-200 ease-in-out hover:scale-[1.03] hover:shadow-primary/40 active:scale-[0.98]">Update Account</button>
</div>
</div>
</div>
<div class="relative border-t border-black/10 pt-8 dark:border-white/10">
<h3 class="absolute -top-3.5 left-0 bg-background-light px-2 text-lg font-bold text-gray-900 dark:bg-background-dark dark:text-white">Preferences</h3>
<div class="space-y-6">
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
<label class="group flex flex-col gap-2">
<span class="text-sm font-medium text-gray-700 dark:text-gray-300">Theme</span>
<select class="rounded-lg border border-black/10 bg-white/50 px-4 py-2 text-gray-900 transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 dark:border-white/10 dark:bg-black/20 dark:text-white">
<option value="dark">Dark</option>
<option value="light">Light</option>
<option value="system">System</option>
</select>
</label>
<label class="group flex flex-col gap-2">
<span class="text-sm font-medium text-gray-700 dark:text-gray-300">Language</span>
<select class="rounded-lg border border-black/10 bg-white/50 px-4 py-2 text-gray-900 transition-colors focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 dark:border-white/10 dark:bg-black/20 dark:text-white">
<option value="en">English</option>
<option value="es">Español</option>
<option value="fr">Français</option>
</select>
</label>
</div>
<div class="flex justify-start">
<button class="rounded-lg bg-primary px-5 py-2.5 text-sm font-bold text-white shadow-md transition-transform duration-200 ease-in-out hover:scale-[1.03] hover:shadow-primary/40 active:scale-[0.98]">Save Preferences</button>
</div>
</div>
</div>
<div class="relative border-t border-black/10 pt-8 dark:border-white/10">
<h3 class="absolute -top-3.5 left-0 bg-background-light px-2 text-lg font-bold text-gray-900 dark:bg-background-dark dark:text-white">Notifications</h3>
<div class="space-y-4">
<div class="flex items-center justify-between rounded-lg p-4 transition-colors hover:bg-black/5 dark:hover:bg-white/5">
<div>
<p class="font-medium text-gray-800 dark:text-gray-200">Email Notifications</p>
<p class="text-sm text-gray-500 dark:text-gray-400">Receive emails about your projects and account.</p>
</div>
<label class="relative h-6 w-11 cursor-pointer">
<input checked="" class="peer sr-only" type="checkbox"/>
<div class="h-full rounded-full bg-gray-300 transition-colors peer-checked:bg-primary dark:bg-gray-700"></div>
<div class="absolute left-0.5 top-0.5 h-5 w-5 rounded-full bg-white shadow transition-transform peer-checked:translate-x-full"></div>
</label>
</div>
<div class="flex items-center justify-between rounded-lg p-4 transition-colors hover:bg-black/5 dark:hover:bg-white/5">
<div>
<p class="font-medium text-gray-800 dark:text-gray-200">In-App Notifications</p>
<p class="text-sm text-gray-500 dark:text-gray-400">Get alerts for important updates in the app.</p>
</div>
<label class="relative h-6 w-11 cursor-pointer">
<input class="peer sr-only" type="checkbox"/>
<div class="h-full rounded-full bg-gray-300 transition-colors peer-checked:bg-primary dark:bg-gray-700"></div>
<div class="absolute left-0.5 top-0.5 h-5 w-5 rounded-full bg-white shadow transition-transform peer-checked:translate-x-full"></div>
</label>
</div>
<div class="pt-2">
<button class="rounded-lg bg-primary px-5 py-2.5 text-sm font-bold text-white shadow-md transition-transform duration-200 ease-in-out hover:scale-[1.03] hover:shadow-primary/40 active:scale-[0.98]">Update Notifications</button>
</div>
</div>
</div>
</div>
</div>
</main>
</div>

</body></html>