#!/usr/bin/env python3
"""
Test to reproduce the exact issue with View Scenes button
"""

import flet as ft
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from ui.main_window import MainWindow

def main(page: ft.Page):
    """Test the main window and View Scenes functionality"""
    page.title = "Vid2Frames - Testing View Scenes"
    page.window_width = 1200
    page.window_height = 800
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    
    # Create main window
    main_window = MainWindow()
    main_window.set_page(page)
    
    # Add main window to page
    page.add(main_window.build())
    
    # Create a mock processing result
    class MockSceneData:
        def __init__(self, start_time, end_time, start_frame, end_frame, frame_count):
            self.start_time = start_time
            self.end_time = end_time
            self.start_frame = start_frame  
            self.end_frame = end_frame
            self.frame_count = frame_count
            self.duration = end_time - start_time

    mock_result = {
        'success': True,
        'job_dir': Path('c:/temp/test_output'),
        'extracted_frames': [],
        'saved_paths': [],
        'detected_scenes': [
            MockSceneData(0.0, 5.2, 0, 156, 157),
            MockSceneData(5.2, 10.8, 157, 324, 168)
        ],
        'scene_video_paths': [Path('c:/temp/scene_1.mp4'), Path('c:/temp/scene_2.mp4')],
        'processing_time': 45.2
    }
    
    # Simulate processing completion
    main_window.on_processing_complete(mock_result)
    
    print("App initialized with mock scene data. Click 'View Scenes' to test.")

if __name__ == "__main__":
    ft.app(target=main, view=ft.AppView.FLET_APP)