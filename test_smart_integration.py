"""
Quick test to verify the smart audio splitter integration
"""
import sys
from pathlib import Path

# Add src to path so we can import modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.core.smart_audio_splitter import SmartAudioSplitter
    from src.ui.audio_split_view import AudioSplitView
    
    def test_integration():
        """Test that smart audio splitter integrates properly"""
        print("🔧 Testing Smart Audio Splitter Integration")
        print("=" * 50)
        
        # Test smart splitter creation
        try:
            smart_splitter = SmartAudioSplitter()
            print("✅ SmartAudioSplitter created successfully")
            print(f"   Device: {smart_splitter.device}")
            print(f"   Silence threshold: {smart_splitter.silence_threshold}")
        except Exception as e:
            print(f"❌ Error creating SmartAudioSplitter: {e}")
            return False
        
        # Test UI view creation
        try:
            audio_view = AudioSplitView()
            print("✅ AudioSplitView created successfully")
            print(f"   Processing mode: {audio_view.processing_mode}")
            print(f"   Has smart splitter: {hasattr(audio_view, 'smart_splitter')}")
        except Exception as e:
            print(f"❌ Error creating AudioSplitView: {e}")
            return False
        
        print()
        print("🎉 Integration test passed!")
        print()
        print("Smart Audio Splitter Features:")
        print("🔇 Silence detection for natural cut points")
        print("🎙️ Word-level timestamp analysis")
        print("✂️ Clean boundaries (no mid-word cuts)")
        print("🧠 Intelligent pause detection")
        print("📊 Detailed timing metadata")
        print()
        print("Available in the Audio Split tab with three modes:")
        print("1. 🧠 Smart Mode (Recommended) - Silence + word boundaries")
        print("2. 📝 Text Mode - Basic AI alignment")
        print("3. 📊 JSON Mode - Pre-aligned data")
        
        return True
    
    if __name__ == "__main__":
        test_integration()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you have all required dependencies installed:")
    print("pip install librosa soundfile")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()