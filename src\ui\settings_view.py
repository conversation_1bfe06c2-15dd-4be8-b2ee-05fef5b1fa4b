import flet as ft


class SettingsView:
    """Settings view for configuring application preferences"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.settings = self.load_default_settings()
        self.page = None  # Will be set by main window
        
    def load_default_settings(self):
        """Load default application settings"""
        return {
            'similarity_threshold': 0.85,
            'quality_threshold': 0.7,
            'output_format': 'PNG',
            'max_frames': None,
            'resize_width': None,
            'scene_detection': True,
            'split_scenes': True,  # Enable scene splitting by default
            'enable_transcription': True,  # Audio transcription setting - enabled by default since local models are available
            'min_scene_duration': 1.0,  # New setting for minimum scene duration
            'max_worker_threads': 4,
            'ui_theme': 'dark'
        }
        
    def build(self):
        """Build the settings view UI"""
        # Header
        header = ft.Container(
            content=ft.Column([
                ft.Text(
                    "Application Settings",
                    size=24,
                    weight=ft.FontWeight.BOLD
                ),
                ft.Text(
                    "Configure processing options and preferences",
                    size=14,
                    color=ft.Colors.ON_SURFACE_VARIANT
                )
            ]),
            padding=20
        )
        
        # Processing settings section
        processing_section = ft.Container(
            content=ft.Column([
                ft.Text("Processing Options", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                
                # Similarity threshold
                ft.Row([
                    ft.Text("Similarity Threshold:", width=200),
                    ft.Slider(
                        min=0.1,
                        max=1.0,
                        divisions=9,
                        value=self.settings['similarity_threshold'],
                        label="0.85",
                        on_change=self.on_similarity_change,
                        expand=True
                    )
                ]),
                ft.Text("Higher values = fewer, more distinct frames", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
                
                # Quality threshold
                ft.Row([
                    ft.Text("Quality Threshold:", width=200),
                    ft.Slider(
                        min=0.1,
                        max=1.0,
                        divisions=9,
                        value=self.settings['quality_threshold'],
                        label="0.7",
                        on_change=self.on_quality_change,
                        expand=True
                    )
                ]),
                ft.Text("Minimum frame quality to keep", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
                
                # Output format
                ft.Row([
                    ft.Text("Output Format:", width=200),
                    ft.Dropdown(
                        options=[
                            ft.dropdown.Option("PNG"),
                            ft.dropdown.Option("JPEG"),
                            ft.dropdown.Option("WebP")
                        ],
                        value=self.settings['output_format'],
                        on_change=self.on_format_change,
                        width=150
                    )
                ]),
                
                # Max frames
                ft.Row([
                    ft.Text("Max Frames (optional):", width=200),
                    ft.TextField(
                        hint_text="Leave empty for no limit",
                        width=150,
                        on_change=self.on_max_frames_change
                    )
                ]),
                
                # Scene detection
                ft.Row([
                    ft.Text("Enable Scene Detection:", width=200),
                    ft.Switch(
                        value=self.settings['scene_detection'],
                        on_change=self.on_scene_detection_change
                    )
                ]),
                
                # Scene splitting
                ft.Row([
                    ft.Text("Split Video into Scenes:", width=200),
                    ft.Switch(
                        value=self.settings['split_scenes'],
                        on_change=self.on_split_scenes_change
                    )
                ]),
                ft.Text("Creates separate video files for each detected scene", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
                
                # Audio transcription
                ft.Row([
                    ft.Text("Enable Audio Transcription:", width=200),
                    ft.Switch(
                        value=self.settings['enable_transcription'],
                        on_change=self.on_transcription_change
                    )
                ]),
                ft.Text("Generate text transcription for each scene using local AI models", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
                
                # Minimum scene duration
                ft.Row([
                    ft.Text("Min Scene Duration (sec):", width=200),
                    ft.Slider(
                        min=0.5,
                        max=10.0,
                        divisions=19,
                        value=self.settings['min_scene_duration'],
                        label="1.0s",
                        on_change=self.on_min_scene_duration_change,
                        width=200
                    )
                ]),
                ft.Text("Scenes shorter than this will be merged", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
                
            ], spacing=15),
            padding=20,
            border_radius=8,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border=ft.border.all(1, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300)
        )
        
        # Performance settings section
        performance_section = ft.Container(
            content=ft.Column([
                ft.Text("Performance Settings", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                
                # Worker threads
                ft.Row([
                    ft.Text("Worker Threads:", width=200),
                    ft.Slider(
                        min=1,
                        max=8,
                        divisions=7,
                        value=self.settings['max_worker_threads'],
                        label="4",
                        on_change=self.on_threads_change,
                        width=200
                    )
                ]),
                ft.Text("More threads = faster processing (uses more CPU)", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
                
                # Resize width
                ft.Row([
                    ft.Text("Resize Width (optional):", width=200),
                    ft.TextField(
                        hint_text="e.g., 1920 (keeps aspect ratio)",
                        width=200,
                        on_change=self.on_resize_change
                    )
                ]),
                
            ], spacing=15),
            padding=20,
            border_radius=8,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border=ft.border.all(1, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300)
        )
        
        # UI settings section
        ui_section = ft.Container(
            content=ft.Column([
                ft.Text("User Interface", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                
                # Theme selection
                ft.Row([
                    ft.Text("Theme:", width=200),
                    ft.Dropdown(
                        options=[
                            ft.dropdown.Option("system", "System"),
                            ft.dropdown.Option("light", "Light"),
                            ft.dropdown.Option("dark", "Dark")
                        ],
                        value=self.settings['ui_theme'],
                        on_change=self.on_theme_change,
                        width=150
                    )
                ]),
                
            ], spacing=15),
            padding=20,
            border_radius=8,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border=ft.border.all(1, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300)
        )
        
        # Action buttons
        action_buttons = ft.Row([
            ft.ElevatedButton(
                "Reset to Defaults",
                icon=ft.Icons.RESTORE,
                on_click=self.reset_to_defaults
            ),
            ft.ElevatedButton(
                "Save Settings",
                icon=ft.Icons.SAVE,
                on_click=self.save_settings,
                style=ft.ButtonStyle(
                    bgcolor=ft.Colors.GREEN_400,
                    color=ft.Colors.WHITE
                )
            )
        ], alignment=ft.MainAxisAlignment.CENTER, spacing=10)
        
        return ft.Column([
            header,
            processing_section,
            performance_section,
            ui_section,
            ft.Container(content=action_buttons, padding=20)
        ], spacing=20, scroll=ft.ScrollMode.AUTO)
        
    def on_similarity_change(self, e):
        """Handle similarity threshold change"""
        self.settings['similarity_threshold'] = e.control.value
        e.control.label = f"{e.control.value:.2f}"
        self.main_window.page.update()
        
    def on_quality_change(self, e):
        """Handle quality threshold change"""
        self.settings['quality_threshold'] = e.control.value
        e.control.label = f"{e.control.value:.2f}"
        self.main_window.page.update()
        
    def on_format_change(self, e):
        """Handle output format change"""
        self.settings['output_format'] = e.control.value
        
    def on_max_frames_change(self, e):
        """Handle max frames change"""
        try:
            value = int(e.control.value) if e.control.value else None
            self.settings['max_frames'] = value
        except ValueError:
            pass
            
    def on_scene_detection_change(self, e):
        """Handle scene detection change"""
        self.settings['scene_detection'] = e.control.value
        
    def on_split_scenes_change(self, e):
        """Handle scene splitting change"""
        self.settings['split_scenes'] = e.control.value
        print(f"Scene splitting setting changed to: {e.control.value}")
        if e.control.value:
            print("✅ Scene splitting will create individual video files for each detected scene")
        else:
            print("❌ Scene splitting disabled - only frame extraction will be performed")
    
    def on_transcription_change(self, e):
        """Handle audio transcription change"""
        self.settings['enable_transcription'] = e.control.value
        print(f"Audio transcription setting changed to: {e.control.value}")
        if e.control.value:
            print("🎤 Audio transcription enabled - will generate text for each scene")
        else:
            print("🔇 Audio transcription disabled")
        
    def on_min_scene_duration_change(self, e):
        """Handle minimum scene duration change"""
        self.settings['min_scene_duration'] = e.control.value
        e.control.label = f"{e.control.value:.1f}s"
        self.main_window.page.update()
        
    def on_threads_change(self, e):
        """Handle worker threads change"""
        self.settings['max_worker_threads'] = int(e.control.value)
        e.control.label = str(int(e.control.value))
        self.main_window.page.update()
        
    def on_resize_change(self, e):
        """Handle resize width change"""
        try:
            value = int(e.control.value) if e.control.value else None
            self.settings['resize_width'] = value
        except ValueError:
            pass
            
    def on_theme_change(self, e):
        """Handle theme change"""
        self.settings['ui_theme'] = e.control.value
        # Apply theme change
        if e.control.value == "light":
            self.main_window.page.theme_mode = ft.ThemeMode.LIGHT
        elif e.control.value == "dark":
            self.main_window.page.theme_mode = ft.ThemeMode.DARK
        else:
            self.main_window.page.theme_mode = ft.ThemeMode.SYSTEM
        # Title bar theme update disabled - using system title bar
        # try:
        #     self.main_window.update_title_bar_theme()
        # except Exception:
        #     pass
        self.main_window.page.update()
        
    def reset_to_defaults(self, e):
        """Reset all settings to defaults"""
        self.settings = self.load_default_settings()
        # TODO: Update UI controls with default values
        print("Settings reset to defaults")
        
    def save_settings(self, e):
        """Save current settings"""
        # TODO: Implement settings persistence
        print("Settings saved:", self.settings)
