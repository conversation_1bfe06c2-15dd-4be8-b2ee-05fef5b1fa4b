#Enhanced Coding Vibe Guidelines

## 🚀 Server & Environment Management
- After making changes, ALWAYS make sure to start up a new server so I can test it.
- Always kill all existing related servers that may have been created in previous testing before trying to start a new server.
- Write code that takes into account the different environments: dev, test, and prod.
- Never overwrite my .env file without first asking and confirming.
- Always use .venv for Python virtual environments.
- Use environment-specific configuration files and never hardcode environment values.

## 🔄 Code Evolution & Patterns
- Always look for existing code to iterate on instead of creating new code.
- Do not drastically change the patterns before trying to iterate on existing patterns.
- When fixing an issue or bug, do not introduce a new pattern or technology without first exhausting all options for the existing implementation. If you must, ensure the old implementation is removed to avoid duplicate logic.
- Avoid major changes to the architecture of a working feature unless explicitly instructed.
- Follow established naming conventions and project structure consistently.

## 🎯 Simplicity & Focus
- Always prefer simple solutions.
- Focus on the areas of code relevant to the task. Do not touch unrelated code.
- You are careful to only make changes that are requested or you are confident are well understood and related to the change being requested.
- Write self-documenting code with clear variable and function names.
- Implement the minimum viable solution first, then iterate.

## 🧹 Code Quality & Organization
- Avoid duplication of code. Check for existing functionality before writing new code.
- Keep the codebase very clean and organized.
- Avoid having files over 200-300 lines of code. Refactor at that point.
- Extract reusable components and utilities into separate modules.
- Use consistent indentation and formatting throughout the project.
- Remove commented-out code and unused imports regularly.

## 📝 Scripts & File Management
- Avoid writing scripts in files if possible, especially if the script is likely only to be run once.
- Create a dedicated `scripts` directory for one-time utilities if absolutely necessary.
- Document any scripts with clear usage instructions and purpose.

## 🧪 Testing & Data Management
- Mocking data is only for tests. Never mock data for dev or prod environments.
- Never add stubbing or fake data patterns to code that affects the dev or prod environments.
- Write thorough tests for all major functionality.
- Use proper test data factories and fixtures for consistent testing.
- Implement integration tests for critical user flows.
- Always write test automation for parts of your codebase where the cost of failure is high (e.g., payments, subscription management, usage tracking).
- When writing tests for third-party integrations like Stripe, ALWAYS reference official examples and docs.

## 🔍 Impact Assessment & Dependencies
- Always think about what other methods and areas of code might be affected by code changes.
- Run the full test suite before committing changes.
- Consider backward compatibility when modifying existing APIs.
- Document breaking changes clearly in commit messages.

## 📚 Documentation & Communication
- Write clear commit messages that explain the "why," not just the "what."
- Add inline comments for complex business logic.
- Update README files when adding new features or changing setup procedures.
- Keep API documentation in sync with code changes.

## 🔧 Performance & Optimization
- Profile before optimizing—measure actual performance bottlenecks.
- Implement lazy loading where appropriate.
- Use database indexing strategically.
- Cache expensive operations thoughtfully.

## 🛡️ Security & Best Practices
- Validate all user inputs at entry points.
- Use parameterized queries to prevent SQL injection.
- Implement proper error handling without exposing sensitive information.
- Follow the principle of least privilege.
- Rate limit all API endpoints.
- Use Row-Level Security (RLS) always.
- Use Captcha on all auth routes/signup pages.
- If using a hosting solution like Vercel, enable Attack Challenge on their WAF.
- Do not share environment variables in videos or streams; know the difference between client & server-side variables.
- Before deploying a Node.js/web app, always run `npm run audit` and resolve vulnerabilities.
- Do not roll your own auth; use managed solutions like Clerk and read their documentation carefully.
- Always set up IP + user-based rate limiting, DDoS protection, firewalls, monitoring, and analytics.

## 🔄 API & Backend Specific (FastAPI/Flask)
- Always validate request/response schemas explicitly.
- Implement proper HTTP status codes for all endpoints.
- Use dependency injection patterns for database connections and external services.
- Log meaningful information for debugging without exposing sensitive data.

## 🌐 Frontend Integration (Next.js)
- Keep API calls centralized in service layers, not scattered throughout components.
- Implement proper error boundaries for graceful failure handling.
- Use TypeScript interfaces consistently between frontend and backend.
- Cache API responses appropriately to reduce server load.

## 📊 Business Logic & Analytics
- Separate business logic from framework-specific code.
- Document business rules and requirements clearly in code comments.
- Implement feature flags for gradual rollouts.
- Track key metrics and user interactions for data-driven decisions.

## 🔀 Version Control & Collaboration
- Make atomic commits: one logical change per commit.
- Use meaningful branch names that reflect the feature or fix.
- Squash commits before merging to keep history clean.
- Review your own code before requesting reviews from others.

---

# Copilot Instructions for Vid2Frames

## Project Overview

Vid2Frames is a cross-platform desktop application that extracts distinct frames from video files using computer vision algorithms. Built with Python and Flet (Flutter-based UI), it provides a native desktop experience with CSS-like styling capabilities.

## Architecture Patterns

### Desktop Application Structure
- **UI Layer**: Flet framework with Material Design and CSS-like styling
- **Business Logic**: Python classes for video processing and file management
- **Processing Engine**: OpenCV + scikit-image for frame extraction and similarity detection
- **Data Layer**: SQLite for local data storage, diskcache for frame caching
- **File System**: Local file operations with pathlib for cross-platform compatibility

### Key Processing Pipeline
1. File selection via native dialog → File validation → Video metadata extraction (FFmpeg)
2. Background frame extraction (OpenCV) → Similarity detection → Deduplication
3. Quality filtering → Format conversion → Local storage → UI updates with progress

## Development Workflow

### Essential Commands
```bash
# Activate .venv virtual environment
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # macOS/Linux

# Development setup
pip install -r requirements.txt

# Run application
python src/main.py

# Run with hot reload (Flet)
flet run src/main.py

# Testing
pytest tests/ --cov=src

# Build executable
pyinstaller vid2frames.spec
```

### Database Operations
- Use SQLite with context managers for connection handling
- Migrations handled through simple SQL scripts in `database/migrations/`
- Always use parameterized queries to prevent SQL injection

## Project-Specific Conventions

### File Structure
```
src/
├── ui/              # Flet UI components and views
├── core/            # Business logic and video processing
├── models/          # Data classes and SQLite models
├── database/        # Database connection and repositories
└── utils/           # Configuration and helper utilities
```

### Video Processing Patterns
- **Frame Extraction**: Use `cv2.VideoCapture()` with threading for non-blocking UI
- **Similarity Detection**: SSIM and perceptual hashing with configurable thresholds (default 0.85)
- **Error Handling**: Graceful degradation with user-friendly error messages
- **Memory Management**: Process frames in batches, explicit cleanup of OpenCV objects
- **Progress Tracking**: Real-time UI updates using Flet's reactive state management

### UI Design Conventions
- Material Design 3 components with consistent theming
- CSS-like styling: `padding=20`, `margin=10`, `border_radius=10`
- Responsive layouts that adapt to window resizing  
- Dark/light mode support following system preferences
- Smooth animations for state transitions

## Integration Points

### External Dependencies
- **FFmpeg**: Video metadata extraction and format validation
- **OpenCV**: Frame extraction and computer vision operations
- **Flet**: UI framework with CSS-like styling capabilities
- **SQLite**: Embedded database for job history and settings

### Critical Configuration
```python
# Processing settings in src/utils/config.py
DEFAULT_SIMILARITY_THRESHOLD = 0.85    # Frame deduplication sensitivity
MAX_FILE_SIZE_MB = 1000               # Desktop app can handle larger files
PROCESSING_BATCH_SIZE = 50            # Frames processed per batch
MAX_WORKER_THREADS = 4                # Parallel processing threads
```

## Common Development Tasks

### Adding New Video Formats
1. Update `SUPPORTED_FORMATS` in `src/utils/constants.py`
2. Add format validation in `src/core/file_manager.py`
3. Test with sample files in `tests/fixtures/videos/`

### UI Component Development
```python
# Example Flet component with CSS-like styling
import flet as ft

def create_upload_area():
    return ft.Container(
        content=ft.Column([
            ft.Icon(ft.icons.CLOUD_UPLOAD, size=48),
            ft.Text("Drop video files here")
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        bgcolor=ft.colors.BLUE_GREY_50,
        border_radius=12,
        padding=40,
        margin=20,
        border=ft.border.all(2, ft.colors.BLUE_GREY_200),
        animate=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
    )
```

### Background Processing
```python
# Use threading for non-blocking operations
import threading
from concurrent.futures import ThreadPoolExecutor

class VideoProcessor:
    def __init__(self, progress_callback):
        self.progress_callback = progress_callback
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    def process_video_async(self, video_path):
        future = self.executor.submit(self._process_video, video_path)
        return future
```

### Database Operations
```python
# Use context managers for SQLite operations
from contextlib import contextmanager

@contextmanager
def get_db_connection():
    conn = sqlite3.connect('vid2frames.db')
    try:
        yield conn
        conn.commit()
    except Exception:
        conn.rollback()
        raise
    finally:
        conn.close()
```

## Quick Reference

- **Documentation**: See `IMPLEMENTATION/` directory for comprehensive specs
- **UI Development**: Use `flet run` for hot reload during development
- **Packaging**: PyInstaller creates cross-platform executables
- **Testing**: Focus on video processing algorithms and UI state management
- **Performance**: Monitor memory usage and implement proper cleanup for large videos