#!/usr/bin/env python3
"""
Test F5-TTS ComfyUI Voice Loading
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.core.f5_tts import F5TTSProcessor
    
    print("🔄 Testing F5-TTS ComfyUI Voice Loading...")
    
    # Create processor
    processor = F5TTSProcessor()
    print("✅ F5TTSProcessor created successfully")
    
    # Try to get voices
    voices = processor.get_available_comfy_voices()
    print(f"🎤 Found {len(voices)} ComfyUI voices:")
    
    for i, voice in enumerate(voices[:5]):  # Show first 5
        print(f"  {i+1}. {voice['name']} ({voice['format']}) - {voice['path']}")
        if 'reference_text' in voice and voice['reference_text']:
            print(f"     Ref text: {voice['reference_text'][:50]}...")
    
    if len(voices) > 5:
        print(f"  ... and {len(voices) - 5} more voices")
        
    if voices:
        print("✅ ComfyUI voice loading works correctly")
    else:
        print("⚠️ No ComfyUI voices found - check ComfyUI installation")
        
except Exception as e:
    print(f"❌ Error testing voices: {e}")
    import traceback
    traceback.print_exc()