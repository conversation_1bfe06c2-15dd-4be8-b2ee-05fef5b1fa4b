"""
Tab registry system for managing application tabs dynamically
"""
from typing import Dict, List, Callable, Optional
import flet as ft


class TabInfo:
    """Information about a tab that can be registered"""
    
    def __init__(self, tab_id: str, name: str, icon: str, 
                 view_factory: Callable[[], ft.Control], 
                 order: int = 100,
                 tooltip: str = None):
        self.tab_id = tab_id
        self.name = name
        self.icon = icon
        self.view_factory = view_factory
        self.order = order
        self.tooltip = tooltip or f"Switch to {name} tab"


class TabRegistry:
    """Registry for all application tabs - manages tabs dynamically"""
    
    def __init__(self):
        self._tabs: Dict[str, TabInfo] = {}
    
    def register_tab(self, tab_info: TabInfo):
        """Register a new tab"""
        self._tabs[tab_info.tab_id] = tab_info
        print(f"✅ Registered tab: {tab_info.name} ({tab_info.tab_id})")
    
    def unregister_tab(self, tab_id: str):
        """Unregister a tab"""
        if tab_id in self._tabs:
            del self._tabs[tab_id]
            print(f"❌ Unregistered tab: {tab_id}")
    
    def get_tabs(self) -> List[TabInfo]:
        """Get all tabs sorted by order"""
        return sorted(self._tabs.values(), key=lambda t: t.order)
    
    def get_tab(self, tab_id: str) -> Optional[TabInfo]:
        """Get specific tab info"""
        return self._tabs.get(tab_id)
    
    def has_tab(self, tab_id: str) -> bool:
        """Check if tab is registered"""
        return tab_id in self._tabs
    
    def list_tab_ids(self) -> List[str]:
        """Get list of all tab IDs in order"""
        return [tab.tab_id for tab in self.get_tabs()]
    
    def load_builtin_tabs(self):
        """Load built-in tabs"""
        print("📁 Loading built-in tabs...")
        
        # Core tabs with lazy imports to avoid circular dependencies
        self.register_tab(TabInfo(
            "upload", "Upload", "upload_file", 
            lambda: self._create_upload_view(), 
            order=10, tooltip="Upload and configure files for processing"
        ))
        
        self.register_tab(TabInfo(
            "progress", "Progress", "timeline", 
            lambda: self._create_progress_view(), 
            order=20, tooltip="View processing progress and status"
        ))
        
        self.register_tab(TabInfo(
            "results", "Results", "folder", 
            lambda: self._create_results_view(), 
            order=30, tooltip="View and manage processing results"
        ))
        
        self.register_tab(TabInfo(
            "audio_split", "Audio Split", "audiotrack", 
            lambda: self._create_audio_split_view(), 
            order=40, tooltip="Split audio files using text alignment"
        ))
        
        self.register_tab(TabInfo(
            "f5_tts", "F5-TTS", "record_voice_over", 
            lambda: self._create_f5_tts_view(), 
            order=50, tooltip="Generate speech using F5-TTS"
        ))
        
        self.register_tab(TabInfo(
            "settings", "Settings", "settings", 
            lambda: self._create_settings_view(), 
            order=90, tooltip="Application settings and preferences"
        ))
    
    def _create_upload_view(self) -> ft.Control:
        """Create upload view (lazy import)"""
        try:
            from ..upload_view import UploadView
            return UploadView()
        except ImportError as e:
            return self._create_error_view(f"Upload view not available: {e}")
    
    def _create_progress_view(self) -> ft.Control:
        """Create progress view (lazy import)"""
        try:
            from ..universal_progress_view import UniversalProgressView
            # Create with dummy callback for now
            return UniversalProgressView(lambda x: None)
        except ImportError as e:
            return self._create_error_view(f"Progress view not available: {e}")
    
    def _create_results_view(self) -> ft.Control:
        """Create results view (lazy import)"""
        try:
            from ..results_view import ResultsView
            return ResultsView()
        except ImportError as e:
            return self._create_error_view(f"Results view not available: {e}")
    
    def _create_audio_split_view(self) -> ft.Control:
        """Create audio split view (lazy import)"""
        try:
            from ..audio_split_view import AudioSplitView
            return AudioSplitView()
        except ImportError as e:
            return self._create_error_view(f"Audio split view not available: {e}")
    
    def _create_f5_tts_view(self) -> ft.Control:
        """Create F5-TTS view (lazy import)"""
        try:
            from ..f5_tts_view import F5TTSView
            return F5TTSView()
        except ImportError as e:
            return self._create_error_view(f"F5-TTS view not available: {e}")
    
    def _create_settings_view(self) -> ft.Control:
        """Create settings view (lazy import)"""
        try:
            from ..settings_view import SettingsView
            return SettingsView()
        except ImportError as e:
            return self._create_error_view(f"Settings view not available: {e}")
    
    def _create_error_view(self, error_message: str) -> ft.Control:
        """Create error view when tab cannot be loaded"""
        return ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.ERROR_OUTLINE, size=64, color=ft.Colors.RED_400),
                ft.Text("Tab Not Available", size=20, weight=ft.FontWeight.BOLD),
                ft.Text(error_message, size=14, color=ft.Colors.ON_SURFACE_VARIANT),
                ft.Text("This tab will be available once the feature is implemented.", 
                       size=12, color=ft.Colors.ON_SURFACE_VARIANT)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
            alignment=ft.alignment.center,
            expand=True
        )


# Global registry
tab_registry = TabRegistry()