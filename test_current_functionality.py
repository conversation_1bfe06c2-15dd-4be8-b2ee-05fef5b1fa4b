#!/usr/bin/env python3
"""
Test script to verify current Vid2Frames functionality
Tests core components without requiring FFmpeg or actual video files
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all modules can be imported successfully"""
    print("🧪 Testing imports...")
    
    try:
        import flet as ft
        print("✅ Flet imported successfully")
    except ImportError as e:
        print(f"❌ Flet import failed: {e}")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        from skimage.metrics import structural_similarity
        print("✅ scikit-image (SSIM)")
    except ImportError as e:
        print(f"❌ scikit-image import failed: {e}")
        return False
    
    try:
        from src.core.video_processor import VideoProcessor
        print("✅ VideoProcessor")
    except ImportError as e:
        print(f"❌ VideoProcessor import failed: {e}")
        return False
    
    try:
        from src.ui.main_window import MainWindow
        print("✅ MainWindow")
    except ImportError as e:
        print(f"❌ MainWindow import failed: {e}")
        return False
    
    try:
        from src.utils.config import config
        print("✅ Configuration")
    except ImportError as e:
        print(f"❌ Configuration import failed: {e}")
        return False
    
    return True

def test_video_processor():
    """Test video processor initialization"""
    print("\n🧪 Testing VideoProcessor...")
    
    try:
        from src.core.video_processor import VideoProcessor
        
        def progress_callback(info):
            print(f"Progress: {info}")
        
        processor = VideoProcessor(progress_callback)
        print("✅ VideoProcessor initialized successfully")
        
        # Test that it's not processing initially
        assert not processor.is_processing, "Processor should not be processing initially"
        print("✅ Initial state correct")
        
        return True
        
    except Exception as e:
        print(f"❌ VideoProcessor test failed: {e}")
        return False

def test_config():
    """Test configuration system"""
    print("\n🧪 Testing Configuration...")
    
    try:
        from src.utils.config import config
        
        # Check that config has expected attributes
        assert hasattr(config, 'processing'), "Config should have processing section"
        assert hasattr(config.processing, 'similarity_threshold'), "Config should have similarity_threshold"
        
        print(f"✅ Similarity threshold: {config.processing.similarity_threshold}")
        print(f"✅ Quality threshold: {config.processing.quality_threshold}")
        print(f"✅ Max frames: {config.processing.max_frames}")
        print(f"✅ Output format: {config.processing.output_format}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_ui_components():
    """Test UI component initialization"""
    print("\n🧪 Testing UI Components...")
    
    try:
        from src.ui.main_window import MainWindow
        from src.ui.upload_view import UploadView
        from src.ui.progress_view import ProgressView
        from src.ui.results_view import ResultsView
        from src.ui.settings_view import SettingsView
        
        # Test basic initialization (without page context)
        main_window = MainWindow()
        print("✅ MainWindow created")
        
        # Create dummy callbacks
        def dummy_file_callback(file_path):
            pass
        
        def dummy_complete_callback():
            pass
        
        upload_view = UploadView(dummy_file_callback)
        print("✅ UploadView created")
        
        progress_view = ProgressView(dummy_complete_callback)
        print("✅ ProgressView created")
        
        results_view = ResultsView(main_window)
        print("✅ ResultsView created")
        
        settings_view = SettingsView(main_window)
        print("✅ SettingsView created")
        
        return True
        
    except Exception as e:
        print(f"❌ UI components test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Vid2Frames Functionality Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("VideoProcessor", test_video_processor),
        ("Configuration", test_config),
        ("UI Components", test_ui_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The app is ready to run.")
        print("\n🔧 Next steps:")
        print("1. Install FFmpeg for complete video metadata extraction")
        print("2. Test with actual video files")
        print("3. Run the app with: python src/main.py")
    else:
        print("❌ Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)