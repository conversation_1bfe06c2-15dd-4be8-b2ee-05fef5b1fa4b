# Original Vid2Frames Implementation - Simple Frame & Scene Processing

**Documentation of the original implementation (commit eb35928) before audio splitting features were added**

This document captures how Vid2Frames worked when it was focused purely on frame extraction and scene detection, providing a reference for understanding the core architecture.

---

## 🏗️ Original Architecture Overview

### Core Philosophy
The original implementation was **simple and focused**:
- **Single purpose**: Extract distinct frames from video files
- **Clean UI**: Four-tab interface (Upload → Progress → Results → Settings)
- **Synchronous flow**: Linear progression through the processing pipeline
- **Direct processing**: No plugin system, direct VideoProcessor integration

### Project Structure
```
src/
├── core/
│   └── video_processor.py     # Core video processing logic
├── ui/
│   ├── main_window.py         # Main application window & navigation
│   ├── upload_view.py         # Video file selection
│   ├── progress_view.py       # Processing progress display
│   ├── results_view.py        # Frame gallery & export
│   └── settings_view.py       # Application preferences
├── utils/
│   ├── config.py              # Configuration management
│   └── file_manager.py        # File operations
└── main.py                    # Application entry point
```

---

## 🎯 Core Processing Pipeline

### VideoProcessor Class
**Location**: `src/core/video_processor.py`

#### Key Methods:
1. **`process_video()`** - Main processing entry point
2. **`_extract_frames()`** - Frame extraction with similarity detection
3. **`_save_frames()`** - Save frames to disk
4. **`_calculate_similarity()`** - SSIM-based frame comparison
5. **`_calculate_quality()`** - Laplacian variance quality assessment

#### Processing Stages:
```python
# Stage 1: Analyzing
- Video validation
- Property extraction (FPS, duration, codec)
- Quality assessment setup

# Stage 2: Extracting  
- Frame-by-frame analysis
- Similarity comparison with previous frame
- Quality threshold filtering
- Frame data collection

# Stage 3: Saving
- Frame export to PNG files
- Metadata generation
- Job directory creation
```

#### Frame Data Structure:
```python
class FrameData:
    def __init__(self, frame: np.ndarray, timestamp: float, frame_number: int):
        self.frame = frame              # OpenCV image data
        self.timestamp = timestamp      # Time position in video
        self.frame_number = frame_number # Sequential frame number
        self.similarity_score = 0.0     # Calculated similarity score
        self.quality_score = 0.0        # Calculated quality score
```

---

## 🎨 User Interface Design

### Main Window Navigation
**Location**: `src/ui/main_window.py`

#### Navigation Structure:
```python
# 4-tab NavigationRail design:
1. Upload   - File selection (always accessible)
2. Progress - Processing status (only during processing) 
3. Results  - Frame gallery (only after completion)
4. Settings - Configuration (always accessible)
```

#### State Management:
```python
class MainWindow:
    def __init__(self):
        self.current_view = "upload"           # Current active view
        self.processing_result = None          # Stores processing outcome
        
        # View instances
        self.upload_view = UploadView(on_video_selected=self.start_video_processing)
        self.progress_view = ProgressView(on_processing_complete=self.on_processing_complete)
        self.results_view = ResultsView(main_window=self)
        self.settings_view = SettingsView(main_window=self)
```

---

## 📊 Progress Tracking System

### ProgressView Class
**Location**: `src/ui/progress_view.py`

#### Visual Components:
1. **Overall Progress Bar** - Shows completion percentage
2. **Stage Indicators** - 3-stage visual pipeline (Analyzing → Extracting → Saving)  
3. **Statistics Display** - Live processing stats
4. **Frame Preview** - Placeholder for current frame (icon-based)
5. **Cancel Button** - Stop processing capability

#### Progress Flow:
```python
def on_progress_update(self, progress_info: dict):
    stage = progress_info.get('stage', '')
    progress = progress_info.get('progress', 0.0)
    
    if stage == 'analyzing':
        # Stage 1: Video analysis
        self._set_stage_active(0)
        self.progress_text.value = "Analyzing video properties..."
        
    elif stage == 'extracting': 
        # Stage 2: Frame extraction
        self._set_stage_complete(0)
        self._set_stage_active(1) 
        extracted = progress_info.get('extracted_count', 0)
        self.progress_text.value = f"Extracting frames... {extracted} unique frames found"
        
    elif stage == 'saving':
        # Stage 3: Saving to disk
        self._set_stage_complete(1)
        self._set_stage_active(2)
        saved = progress_info.get('saved_count', 0)
        self.progress_text.value = f"Saving frames to disk... {saved} saved"
        
    elif stage == 'complete':
        # All stages complete
        self._set_all_stages_complete()
        extracted = progress_info.get('extracted_count', 0)
        self.progress_text.value = f"Processing complete! {extracted} frames extracted"
```

#### Statistics Tracked:
- **Frames Analyzed** - Total frames processed
- **Frames Extracted** - Unique frames found  
- **Quality Score** - Current frame quality
- **Duration** - Video length

---

## 🖼️ Results Display System

### ResultsView Class  
**Location**: `src/ui/results_view.py`

#### Components:
1. **Summary Statistics** - Total frames, processing time, file size
2. **Action Buttons** - Open folder, export ZIP, new processing
3. **Frame Grid** - 4-column grid layout for frame thumbnails
4. **Empty State** - Placeholder when no results available

#### Frame Display:
```python
def add_frame(self, frame_path, timestamp, similarity_score):
    """Add a frame to the results grid"""
    frame_card = ft.Container(
        content=ft.Column([
            # Frame thumbnail (icon placeholder in original)
            ft.Container(
                content=ft.Icon(ft.Icons.IMAGE, size=32),
                width=180, height=120,
                bgcolor=ft.colors.GREY_200,
                border_radius=8
            ),
            # Timestamp display
            ft.Text(f"{timestamp:.1f}s", size=10),
            # Similarity score
            ft.Text(f"Similarity: {similarity_score:.2f}", size=8)
        ], spacing=5),
        on_click=lambda e, path=frame_path: self.view_frame(path)
    )
```

---

## ⚙️ Configuration System

### Processing Parameters:
```python
# Default settings in config.py
similarity_threshold = 0.85    # SSIM threshold for duplicate detection
quality_threshold = 0.3       # Minimum quality score (Laplacian variance)
max_frames = None             # Maximum frames to extract (no limit)
output_format = 'png'         # Frame export format
```

### File Management:
- **Job Directories** - Timestamped folders for each processing job
- **Metadata Storage** - JSON files with processing details
- **Frame Naming** - `frame_XXXXXX_TT.TTs.png` format

---

## 🔄 Processing Flow Summary

### Complete User Journey:
```
1. UPLOAD TAB
   └─ User selects video file
   └─ File validation occurs
   └─ "Process Video" button appears

2. PROGRESS TAB (auto-navigation)
   └─ VideoProcessor starts in background thread
   └─ Real-time progress updates via callback
   └─ Stage indicators show current phase
   └─ Statistics update live
   └─ User can cancel processing

3. RESULTS TAB (auto-navigation on completion)
   └─ Frame grid populates with thumbnails
   └─ Summary statistics displayed
   └─ Export and folder actions available
   └─ User can start new processing
```

### Threading Model:
- **Main Thread** - UI updates and user interaction
- **Background Thread** - Video processing (VideoProcessor.process_video)
- **Progress Callback** - Cross-thread communication via `page.run_thread()`

---

## 🧰 Key Algorithms

### Frame Similarity Detection (SSIM):
```python
def _calculate_similarity(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
    # Convert to grayscale for comparison
    gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
    
    # Resize to standard size for consistent comparison
    gray1 = cv2.resize(gray1, (256, 256))
    gray2 = cv2.resize(gray2, (256, 256))
    
    # Calculate SSIM (Structural Similarity Index)
    similarity_score, _ = ssim(gray1, gray2, full=True)
    return similarity_score
```

### Frame Quality Assessment (Laplacian Variance):
```python 
def _calculate_quality(self, frame: np.ndarray) -> float:
    # Convert to grayscale
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # Calculate Laplacian variance (measures sharpness/focus)
    laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
    
    # Normalize to 0-1 range (typical values: 0-2000, good quality > 100)
    normalized_quality = min(laplacian_var / 500.0, 1.0)
    return normalized_quality
```

---

## 📝 Implementation Characteristics

### Strengths of Original Design:
✅ **Simple and focused** - Clear single purpose
✅ **Intuitive UI flow** - Linear progression through tabs
✅ **Robust processing** - Good error handling and cancellation
✅ **Quality algorithms** - SSIM + Laplacian variance work well
✅ **Clean architecture** - Clear separation of concerns

### Limitations:
❌ **No real frame previews** - Only icon placeholders
❌ **Limited export options** - Basic folder/ZIP only  
❌ **No batch processing** - Single video at a time
❌ **Fixed parameters** - Limited runtime configuration
❌ **No extensibility** - Monolithic design

---

## 🔄 Evolution to Current State

### What Was Added Later:
1. **Audio Processing** - Whisper transcription, audio splitting
2. **Scene Detection** - Automatic scene boundary detection
3. **F5-TTS Integration** - Text-to-speech generation
4. **Universal Progress System** - Multi-operation progress handling
5. **Plugin Architecture** - Modular operation system
6. **Enhanced UI** - Real frame previews, better theming

### Core Concepts Preserved:
- **Tab-based navigation** - Still uses the same 4+ tab structure
- **Background processing** - Still uses threading for non-blocking operations
- **Progress callbacks** - Enhanced but same basic concept
- **Results display** - Frame grid concept maintained
- **File management** - Job directory concept preserved

---

## 🎯 Key Takeaways for Documentation

### What Made It Work Well:
1. **Clear user flow** - Upload → Process → View Results
2. **Real-time feedback** - Progress updates and cancellation
3. **Quality algorithms** - SSIM similarity + Laplacian quality  
4. **Clean data structures** - FrameData class, clear result format
5. **Separation of concerns** - UI, processing, file management separate

### Design Patterns Used:
- **Observer Pattern** - Progress callbacks for UI updates
- **Command Pattern** - Processing operations as discrete units
- **MVC Architecture** - Views, data processing, and control logic separated
- **Thread Safety** - Proper locking and cross-thread communication

This original implementation provides a solid foundation that demonstrates how to build a focused, user-friendly video processing application with clean architecture and robust processing capabilities.