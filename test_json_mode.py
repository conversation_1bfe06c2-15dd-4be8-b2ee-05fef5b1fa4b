"""
Test script to verify JSON mode functionality
"""
import sys
from pathlib import Path

# Add src to path so we can import modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.core.audio_splitter_from_alignment import split_audio_from_alignment_file
    
    def test_json_mode():
        """Test JSON alignment mode functionality"""
        print("📊 Testing JSON Alignment Mode")
        print("=" * 40)
        
        # Test paths (adjust these to match your files)
        audio_path = Path("C:/Users/<USER>/Downloads/7habits/autonoe_7habits.wav")
        alignment_json_path = Path("C:/Users/<USER>/Downloads/7habits/autonoe_7habits_split_20250930_171245/alignment_info.json")
        output_dir = Path("test_json_mode_output")
        
        print(f"📁 Audio file: {audio_path}")
        print(f"📊 Alignment file: {alignment_json_path}")
        print(f"📂 Output directory: {output_dir}")
        print()
        
        # Test with None values to see if our validation works
        print("🧪 Testing validation with None values...")
        
        def test_callback(msg, progress):
            print(f"[{progress*100:6.1f}%] {msg}")
        
        # Test 1: None audio path
        print("Test 1: None audio path")
        result = split_audio_from_alignment_file(None, alignment_json_path, output_dir, progress_callback=test_callback)
        print(f"Result: {result} (should be False)")
        print()
        
        # Test 2: None alignment path  
        print("Test 2: None alignment path")
        result = split_audio_from_alignment_file(audio_path, None, output_dir, progress_callback=test_callback)
        print(f"Result: {result} (should be False)")
        print()
        
        # Check if files exist
        if not audio_path.exists():
            print("❌ Audio file not found - update the path in this script")
            print("   Current path:", audio_path)
            return False
            
        if not alignment_json_path.exists():
            print("❌ Alignment JSON file not found - update the path in this script")
            print("   Current path:", alignment_json_path)
            return False
        
        print("✅ Both files found!")
        print()
        
        print("🚀 Starting JSON mode audio splitting with real files...")
        success = split_audio_from_alignment_file(
            audio_path=audio_path,
            alignment_json_path=alignment_json_path,
            output_dir=output_dir,
            output_format="wav",
            buffer_ms=100,
            progress_callback=test_callback
        )
        
        if success:
            print()
            print("🎉 JSON mode test SUCCESSFUL!")
            print(f"📂 Check output: {output_dir}")
            print()
            print("This proves JSON mode works correctly.")
            print("If you're getting errors in the UI, it might be:")
            print("1. File picker not showing alignment files")
            print("2. Mode switching not updating UI properly") 
            print("3. File path not being set correctly")
        else:
            print("❌ JSON mode test FAILED!")
            print("Check the error messages above for details.")
        
        return success
    
    if __name__ == "__main__":
        test_json_mode()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()