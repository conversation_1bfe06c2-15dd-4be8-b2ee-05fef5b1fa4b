"""
Test script to demonstrate audio transcription functionality
This will test the transcription system with your local models
"""
from pathlib import Path
from src.core.transcription import LocalTranscriber

def test_local_transcription():
    """Test the local transcription setup"""
    print("🎤 Testing Local Audio Transcription")
    print("=" * 50)
    
    try:
        # Initialize transcriber
        transcriber = LocalTranscriber()
        
        # Check if ComfyUI models are found
        if not transcriber.comfyui_models_path:
            print("❌ ComfyUI models directory not found")
            print("   Please ensure ComfyUI is installed and models are available")
            return False
        
        print(f"✅ Found ComfyUI models at: {transcriber.comfyui_models_path}")
        
        # List available models
        models = transcriber.list_available_models()
        print(f"\n📦 Available Audio Models ({len(models)}):")
        for name, path in models.items():
            size_mb = path.stat().st_size / (1024 * 1024)
            print(f"   • {name} ({size_mb:.1f} MB)")
        
        if not models:
            print("❌ No audio models found in ComfyUI")
            return False
        
        # Try to load Whisper model
        print(f"\n🔄 Loading Whisper model...")
        whisper_success = transcriber.load_whisper_model()
        
        if whisper_success:
            print(f"✅ Whisper model loaded successfully on {transcriber.device}")
            model_type = "Whisper Large v3"
        else:
            print("❌ Whisper model failed to load")
            print("🔄 Trying Wav2Vec2 model...")
            wav2vec_success = transcriber.load_wav2vec2_model()
            
            if wav2vec_success:
                print(f"✅ Wav2Vec2 model loaded successfully on {transcriber.device}")
                model_type = "Wav2Vec2 Large English"
            else:
                print("❌ Both Whisper and Wav2Vec2 models failed to load")
                return False
        
        print(f"\n🎯 Transcription System Ready!")
        print(f"   Model: {model_type}")
        print(f"   Device: {transcriber.device}")
        print(f"   PyTorch: {'✅ Available' if torch.cuda.is_available() else '✅ CPU Only'}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("   To install required packages, run:")
        print("   pip install torch torchaudio transformers")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_transcription_info():
    """Show information about the transcription feature"""
    print("\n🎤 Audio Transcription Feature")
    print("=" * 50)
    print("This feature will:")
    print("• Extract audio from each detected video scene")
    print("• Use your local Whisper Large v3 model for transcription")
    print("• Generate text files (JSON, SRT, TXT) with the transcribed speech")
    print("• Work completely offline with your ComfyUI models")
    print("• Support multiple languages (depending on model)")
    
    print(f"\n📁 Your Models:")
    print(f"   Whisper Large v3: ~3GB - Excellent quality, multiple languages")
    print(f"   Wav2Vec2 English: ~1.3GB - Good quality, English only")
    
    print(f"\n⚡ Performance:")
    print(f"   GPU Acceleration: {'✅ Available' if torch.cuda.is_available() else '❌ Not Available'}")
    print(f"   Processing: ~2-5x faster than real-time on GPU")
    print(f"   Quality: Professional-grade transcription accuracy")
    
    print(f"\n📄 Output Formats:")
    print(f"   • JSON: Machine-readable with timestamps and metadata")
    print(f"   • SRT: Standard subtitle format for video players")
    print(f"   • TXT: Human-readable transcript with timestamps")

if __name__ == "__main__":
    try:
        import torch
        print(f"PyTorch Version: {torch.__version__}")
        print(f"CUDA Available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA Device: {torch.cuda.get_device_name()}")
    except ImportError:
        pass
    
    show_transcription_info()
    
    print(f"\n" + "=" * 50)
    success = test_local_transcription()
    
    if success:
        print(f"\n🎉 Transcription system is ready to use!")
        print(f"   Enable it in Settings → Processing Options → Enable Audio Transcription")
    else:
        print(f"\n❌ Transcription system needs setup")
        print(f"   Install missing dependencies: pip install torch torchaudio transformers")