#!/usr/bin/env python3
"""
Complete test of the View Scenes workflow
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_complete_workflow():
    """Test the complete View Scenes workflow"""
    from ui.main_window import MainWindow
    from core.video_processor import SceneData
    import flet as ft
    
    # Create a mock page
    class MockPage:
        def __init__(self):
            self.dialog = None
            self.controls = []
        
        def update(self):
            print("Page updated")
        
        def add(self, control):
            self.controls.append(control)
    
    # Create main window and mock page
    mock_page = MockPage()
    main_window = MainWindow()
    main_window.set_page(mock_page)
    
    # Build the main window
    ui = main_window.build()
    
    print(f"Main window page: {main_window.page}")
    print(f"Results view page: {main_window.results_view.page}")
    
    # Create mock processing result with scene data
    scene1 = SceneData(
        start_time=0.0,
        end_time=5.2,
        start_frame=0,
        end_frame=156,
        frame_count=157
    )
    
    scene2 = SceneData(
        start_time=5.2,
        end_time=10.8,
        start_frame=157,
        end_frame=324,
        frame_count=168
    )
    
    mock_result = {
        'success': True,
        'job_dir': Path('/tmp/test'),
        'extracted_frames': [],
        'saved_paths': [],
        'detected_scenes': [scene1, scene2],
        'scene_video_paths': [],
        'processing_time': 30.0
    }
    
    # Simulate processing completion
    main_window.on_processing_complete(mock_result)
    
    print(f"After processing completion:")
    print(f"Results view detected_scenes: {len(main_window.results_view.detected_scenes)}")
    print(f"Results view page: {main_window.results_view.page}")
    print(f"View scenes button exists: {hasattr(main_window.results_view, 'view_scenes_button')}")
    
    if hasattr(main_window.results_view, 'view_scenes_button'):
        print(f"View scenes button visible: {main_window.results_view.view_scenes_button.visible}")
        
        # Test clicking the button
        print("\nTesting button click...")
        try:
            # Create mock event
            class MockEvent:
                pass
            
            main_window.results_view.view_scenes(MockEvent())
            print("Button click succeeded!")
            
        except Exception as e:
            print(f"Button click failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_complete_workflow()