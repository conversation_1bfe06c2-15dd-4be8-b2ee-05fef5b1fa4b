import flet as ft
from typing import Dict, List, Callable

class BrollPreviewCard(ft.Container):
    def __init__(self, sentence_data: Dict, on_select_alternative: Callable, on_custom_search: Callable):
        super().__init__()
        self.sentence_data = sentence_data
        self.on_select_alternative = on_select_alternative
        self.on_custom_search = on_custom_search
        self.content = self._build()

    def _build(self):
        selected_video = self.sentence_data.get('selected_video')
        
        thumbnail = ft.Image(
            src=selected_video['thumbnail'] if selected_video else "https://via.placeholder.com/150?text=No+Video",
            width=150,
            height=90,
            fit=ft.ImageFit.COVER,
            border_radius=ft.border_radius.all(8)
        )

        return ft.Card(
            content=ft.Container(
                padding=10,
                content=ft.Row(
                    [
                        thumbnail,
                        ft.Column(
                            [
                                ft.Text(f"\"{self.sentence_data['sentence']}\"", italic=True, size=12),
                                ft.Text(f"Keywords: {', '.join(self.sentence_data['keywords'])}", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
                                ft.Row(
                                    [
                                        ft.ElevatedButton("Change Video", icon="swap_horiz", on_click=self._handle_select_alternative),
                                        ft.ElevatedButton("Custom Search", icon="search", on_click=self._handle_custom_search),
                                    ]
                                )
                            ],
                            expand=True,
                            spacing=5
                        ),
                    ],
                    spacing=10
                )
            )
        )
    
    def _handle_select_alternative(self, e):
        self.on_select_alternative(self.sentence_data)

    def _handle_custom_search(self, e):
        self.on_custom_search(self.sentence_data)
