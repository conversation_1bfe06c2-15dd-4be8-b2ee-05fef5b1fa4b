"""
Registry system for managing all available operations
"""
from typing import Dict, Type, List, Optional
from .base_operation import BaseOperation


class OperationRegistry:
    """Registry for all available operations - manages plugins dynamically"""
    
    def __init__(self):
        self._operations: Dict[str, Type[BaseOperation]] = {}
        self._instances: Dict[str, BaseOperation] = {}
    
    def register_operation(self, operation_class: Type[BaseOperation]):
        """Register a new operation type"""
        # Create instance to get metadata
        instance = operation_class()
        operation_id = instance.operation_id
        
        # Store both class and instance
        self._operations[operation_id] = operation_class
        self._instances[operation_id] = instance
        
        print(f"✅ Registered operation: {instance.display_name} ({operation_id})")
    
    def get_operation_class(self, operation_id: str) -> Type[BaseOperation]:
        """Get operation class by ID"""
        if operation_id not in self._operations:
            raise ValueError(f"Unknown operation: {operation_id}")
        return self._operations[operation_id]
    
    def get_operation_instance(self, operation_id: str) -> BaseOperation:
        """Get cached operation instance by ID"""
        if operation_id not in self._instances:
            raise ValueError(f"Unknown operation: {operation_id}")
        return self._instances[operation_id]
    
    def create_operation_instance(self, operation_id: str) -> BaseOperation:
        """Create new operation instance by ID"""
        operation_class = self.get_operation_class(operation_id)
        return operation_class()
    
    def list_operations(self) -> List[Dict[str, str]]:
        """List all available operations with metadata"""
        result = []
        for instance in self._instances.values():
            result.append({
                'id': instance.operation_id,
                'name': instance.display_name,
                'description': instance.description,
                'icon': instance.icon
            })
        return result
    
    def has_operation(self, operation_id: str) -> bool:
        """Check if operation is registered"""
        return operation_id in self._operations
    
    def unregister_operation(self, operation_id: str):
        """Unregister an operation (for plugins/testing)"""
        if operation_id in self._operations:
            del self._operations[operation_id]
        if operation_id in self._instances:
            del self._instances[operation_id]
    
    def load_builtin_operations(self):
        """Load built-in operations (called after view classes are available)"""
        try:
            # Import operations here to avoid circular imports
            from .video_operation import VideoOperation
            self.register_operation(VideoOperation)
        except ImportError as e:
            print(f"⚠️ Could not load VideoOperation: {e}")
        
        try:
            from .audio_operation import AudioSplittingOperation
            self.register_operation(AudioSplittingOperation)
        except ImportError as e:
            print(f"⚠️ Could not load AudioSplittingOperation: {e}")
        
        try:
            from .f5tts_operation import F5TTSOperation
            self.register_operation(F5TTSOperation)
        except ImportError as e:
            print(f"⚠️ Could not load F5TTSOperation: {e}")


# Global registry instance
operation_registry = OperationRegistry()