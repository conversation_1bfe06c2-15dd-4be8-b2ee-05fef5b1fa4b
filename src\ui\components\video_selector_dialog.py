import flet as ft
from typing import Dict, List, Callable

class VideoSelectorDialog(ft.AlertDialog):
    def __init__(self, sentence_data: Dict, alternatives: List[Dict], on_video_selected: Callable):
        super().__init__()
        self.modal = True
        self.title = ft.Text("Select an Alternative Video")
        self.sentence_data = sentence_data
        self.alternatives = alternatives
        self.on_video_selected = on_video_selected

        self.content = self._build_content()
        self.actions = [
            ft.TextButton("Cancel", on_click=self._handle_close),
        ]

    def _build_content(self):
        grid = ft.GridView(
            expand=False,
            runs_count=3,
            max_extent=180,
            child_aspect_ratio=1.0,
            spacing=10,
            run_spacing=10,
        )

        for video in self.alternatives:
            card = ft.Card(
                content=ft.Container(
                    content=ft.Column(
                        [
                            ft.Image(
                                src=video['thumbnail'],
                                width=150,
                                height=90,
                                fit=ft.ImageFit.COVER,
                                border_radius=ft.border_radius.all(8)
                            ),
                            ft.Text(f"Duration: {video['duration']}s", size=10)
                        ],
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER
                    ),
                    padding=10,
                    on_click=lambda e, v=video: self._handle_video_selection(v)
                )
            )
            grid.controls.append(card)

        return ft.Container(
            content=grid,
            width=600,
            height=400,
        )

    def _handle_video_selection(self, video: Dict):
        self.on_video_selected(self.sentence_data, video)
        self.open = False
        self.page.update()

    def _handle_close(self, e):
        self.open = False
        self.page.update()
