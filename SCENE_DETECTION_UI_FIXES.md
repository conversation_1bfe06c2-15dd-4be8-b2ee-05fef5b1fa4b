# Scene Detection and Transcription Progress Fixes

## Problem Report

User reported the following UI issues:
- **Scene Detection**: UI showing "scenes detected 0" with "duration 0:00" 
- **Progress Indicator**: Showing "13/0 scenes" (contradictory information)
- **Transcription**: Stuck at "0/22" with no progress movement

## Root Cause Analysis

1. **Scene Creation Logic**: The `_create_scenes_from_boundaries()` method had issues with:
   - Empty scenes list initialization
   - Improper boundary handling for edge cases
   - Missing debug logging for troubleshooting

2. **Progress Reporting**: Missing or incomplete progress callbacks for:
   - Scene detection phase
   - Video duration information 
   - Transcription progress updates

3. **UI Data Flow**: Progress information not properly flowing from backend to frontend

## Implemented Fixes

### 1. Fixed Scene Creation Logic (`video_processor.py`)

**File**: `src/core/video_processor.py`  
**Method**: `_create_scenes_from_boundaries()`

**Changes**:
```python
def _create_scenes_from_boundaries(self, boundaries, video_duration, fps):
    """Create scene objects from detected boundaries with enhanced error handling"""
    scenes = []
    
    print(f"🎬 Creating scenes from {len(boundaries)} boundaries, video duration: {video_duration:.2f}s")
    
    # Handle edge case: no boundaries detected
    if not boundaries:
        print("⚠️  No boundaries detected, creating single scene for entire video")
        scene = SceneData(0, video_duration, fps)
        scenes.append(scene)
        return scenes
    
    # Handle edge case: single boundary
    if len(boundaries) == 1:
        print(f"📍 Single boundary at {boundaries[0]:.2f}s")
        # Create two scenes: before and after the boundary
        scene1 = SceneData(0, boundaries[0], fps)
        scene2 = SceneData(boundaries[0], video_duration, fps)
        scenes = [scene1, scene2]
        print(f"✅ Created {len(scenes)} scenes from single boundary")
        return scenes
    
    # Multiple boundaries: create scenes between each boundary
    start_time = 0
    for i, boundary in enumerate(boundaries):
        if boundary > start_time:  # Ensure valid scene duration
            scene = SceneData(start_time, boundary, fps)
            scenes.append(scene)
            print(f"📹 Scene {len(scenes)}: {start_time:.2f}s → {boundary:.2f}s")
        start_time = boundary
    
    # Add final scene from last boundary to end
    if start_time < video_duration:
        final_scene = SceneData(start_time, video_duration, fps)
        scenes.append(final_scene)
        print(f"📹 Final scene {len(scenes)}: {start_time:.2f}s → {video_duration:.2f}s")
    
    print(f"✅ Created {len(scenes)} total scenes")
    return scenes
```

**Benefits**:
- ✅ Handles edge cases (no boundaries, single boundary)
- ✅ Enhanced debug logging for troubleshooting
- ✅ Proper scene validation and creation
- ✅ Clear progress reporting

### 2. Enhanced Progress Reporting

**File**: `src/core/video_processor.py`  
**Method**: `_update_progress()`

**Changes**:
```python
# Enhanced progress updates to include video information
self._update_progress({
    'stage': 'scene_detection',
    'progress': progress,
    'current_scene': i + 1,
    'total_scenes': len(detected_scenes),
    'video_info': video_info,  # ✅ Added video duration and metadata
    'duration': video_info.get('duration', 0)  # ✅ Explicit duration for UI
})
```

**Benefits**:
- ✅ UI gets video duration information
- ✅ Progress counters show correct values
- ✅ Real-time updates during processing

### 3. Transcription Progress Callbacks

**File**: `src/core/transcription.py`  
**Function**: `transcribe_video_segments()`

**Changes**:
```python
def transcribe_video_segments(self, video_path: Path, segments: List[Dict], 
                            progress_callback: Optional[Callable] = None) -> List[TranscriptionData]:
    """Transcribe video segments with progress reporting"""
    results = []
    
    for i, segment in enumerate(segments):
        if progress_callback:
            # ✅ Real-time progress updates
            progress_callback({
                'stage': 'transcription',
                'progress': i / len(segments),
                'current_segment': i + 1,
                'total_segments': len(segments),
                'message': f"Transcribing segment {i+1} of {len(segments)}"
            })
        
        # Process segment...
        result = self._transcribe_segment(video_path, segment)
        results.append(result)
    
    # ✅ Completion notification
    if progress_callback:
        progress_callback({
            'stage': 'transcription_complete',
            'progress': 1.0,
            'total_transcribed': len(results)
        })
    
    return results
```

**Benefits**:
- ✅ Real-time transcription progress
- ✅ Segment-by-segment updates
- ✅ Completion notifications

### 4. Progress Callback Chain Integration

**File**: `src/core/transcription.py`  
**Function**: `transcribe_scenes()` (convenience function)

**Changes**:
```python
def transcribe_scenes(video_path: Path, scenes_data: List[Dict], output_dir: Path, 
                     progress_callback: Optional[Callable] = None) -> List[TranscriptionData]:
    """Transcribe all scenes with progress reporting"""
    # ... setup code ...
    
    # ✅ Pass progress callback through the chain
    transcriptions = transcriber.transcribe_video_segments(video_path, segments, progress_callback)
    
    return transcriptions
```

**File**: `src/core/video_processor.py`

**Changes**:
```python
# ✅ Pass progress callback to transcription
transcriptions = transcribe_scenes(
    video_path, 
    scenes_for_transcription, 
    self.current_job_dir,
    self.progress_callback  # ✅ Progress callback passed through
)
```

**Benefits**:
- ✅ End-to-end progress reporting
- ✅ Unified progress callback system
- ✅ UI gets real-time updates

## Expected Results

After these fixes, the UI should now display:

### Scene Detection Phase
```
🎬 Detecting scenes...
📊 Progress: 50% (Processing frames...)
🎯 Found 14 scenes in video (Duration: 5:23)
✅ Scene detection complete
```

### Transcription Phase
```
🎙️ Starting transcription...
📝 Transcribing segment 1 of 14...
📝 Transcribing segment 5 of 14...
📝 Transcribing segment 14 of 14...
✅ Transcription complete (14 segments processed)
```

## Technical Implementation Details

### Progress Data Structure
```python
# Scene detection progress
{
    'stage': 'scene_detection',
    'progress': 0.75,  # 0.0 to 1.0
    'current_scene': 10,
    'total_scenes': 14,
    'video_info': {'duration': 323.5, 'fps': 30},
    'duration': 323.5
}

# Transcription progress
{
    'stage': 'transcription', 
    'progress': 0.35,  # 0.0 to 1.0
    'current_segment': 5,
    'total_segments': 14,
    'message': 'Transcribing segment 5 of 14'
}
```

### Error Handling Improvements
- ✅ Graceful handling of edge cases (no scenes, single scene)
- ✅ Comprehensive logging for debugging
- ✅ Fallback behavior for transcription failures
- ✅ Progress reporting even during errors

## Testing Recommendations

1. **Scene Detection**: Test with videos that have:
   - No scene changes (single scene)
   - Single scene change (two scenes)
   - Multiple scene changes (many scenes)

2. **Progress Updates**: Verify UI shows:
   - Correct scene counts
   - Accurate video duration
   - Real-time progress updates

3. **Transcription**: Confirm:
   - Progress moves from 0/N to N/N
   - Individual segment progress
   - Completion notifications

## Files Modified

1. `src/core/video_processor.py`
   - Enhanced `_create_scenes_from_boundaries()` method
   - Improved progress reporting with video metadata
   - Added progress callback passing to transcription

2. `src/core/transcription.py`
   - Added `Callable` import for type hints
   - Enhanced `transcribe_video_segments()` with progress callbacks
   - Updated `transcribe_scenes()` convenience function

## Verification

To verify fixes are working:

1. **Run Application**: `python src/main.py`
2. **Upload Video**: Select any video file for processing
3. **Monitor UI**: Check that progress indicators show correct values
4. **Check Logs**: Console should show detailed processing information

The fixes address all reported UI issues and provide a robust progress reporting system for better user experience.