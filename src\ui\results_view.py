import flet as ft
from pathlib import Path
import subprocess
import platform
from typing import Dict, List

from .components.broll_preview_card import BrollPreviewCard
from .components.video_selector_dialog import VideoSelectorDialog
from .components.custom_search_dialog import CustomSearchDialog
from ..core.broll.broll_generator import BRollGenerator


class ResultsView:
    """Results view for displaying extracted frames and B-roll previews."""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.page = None  # Will be set by main window
        
        # Data stores
        self.processing_result = {}
        self.broll_data = {}
        self.broll_generator: BRollGenerator = None

        # UI Components
        self.results_tabs = None
        self.frame_grid = None
        self.broll_preview_area = None
        self.summary_container = None
        self.total_frames_text = ft.Text("0", size=24, weight=ft.FontWeight.BOLD)
        self.scenes_count_text = ft.Text("0", size=24, weight=ft.FontWeight.BOLD)
        self.transcriptions_count_text = ft.Text("0", size=24, weight=ft.FontWeight.BOLD)
        self.processing_time_text = ft.Text("00:00", size=24, weight=ft.FontWeight.BOLD)
        self.total_size_text = ft.Text("0 MB", size=24, weight=ft.FontWeight.BOLD)
        self.broll_videos_text = ft.Text("0", size=24, weight=ft.FontWeight.BOLD)
        
    def build(self):
        """Build the results view UI"""
        # Header
        header = ft.Container(
            content=ft.Column([
                ft.Text("Processing Results", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("Review your generated content", size=14, color=ft.Colors.ON_SURFACE_VARIANT)
            ]),
            padding=20
        )

        # Summary Stats
        self.summary_container = self._build_summary_container()

        # Action Buttons
        action_buttons = ft.Row([
            ft.ElevatedButton("Open Folder", icon="folder_open", on_click=self.open_output_folder),
            ft.ElevatedButton("Export as ZIP", icon="archive", on_click=self.export_as_zip),
            ft.ElevatedButton("New Process", icon="add", on_click=self.start_new_processing, bgcolor=ft.Colors.PRIMARY, color=ft.Colors.ON_PRIMARY),
        ], spacing=10, alignment=ft.MainAxisAlignment.CENTER)

        # Frame Extraction Tab
        self.frame_grid = ft.GridView(expand=True, runs_count=5, max_extent=200, child_aspect_ratio=1.0, spacing=10, run_spacing=10)
        frame_tab_content = ft.Container(self.frame_grid, expand=True)

        # B-roll Preview Tab
        self.broll_preview_area = ft.ListView(expand=True, spacing=10, padding=20)
        broll_tab_content = ft.Container(self.broll_preview_area, expand=True)

        # Combined Videos Tab
        self.combined_videos_area = ft.ListView(expand=True, spacing=10, padding=20)
        combined_tab_content = ft.Container(self.combined_videos_area, expand=True)

        # Tabs for different results
        self.results_tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(text="Frame Extraction", content=frame_tab_content),
                ft.Tab(text="B-Roll Preview", content=broll_tab_content),
                ft.Tab(text="Combined Videos", content=combined_tab_content),
            ],
            expand=True,
        )

        return ft.Column([
            header,
            self.summary_container,
            action_buttons,
            ft.Container(self.results_tabs, expand=True, padding=10)
        ], expand=True)

    def _build_summary_container(self):
        return ft.Container(
            content=ft.Row([
                self._create_stat("Frames Extracted", self.total_frames_text),
                self._create_stat("Videos Created", self.scenes_count_text),
                self._create_stat("B-Roll Videos", self.broll_videos_text),
                self._create_stat("Processing Time", self.processing_time_text),
                self._create_stat("Total Size", self.total_size_text),
            ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
            padding=20,
            border_radius=8,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            opacity=0.8
        )

    def _create_stat(self, title: str, control: ft.Text):
        return ft.Column([
            ft.Text(title, size=12, color=ft.Colors.ON_SURFACE_VARIANT),
            control
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)

    def set_results(self, processing_result: dict):
        """Set processing results and update the view"""
        self.processing_result = processing_result
        self.broll_data = processing_result.get('broll_data', {})
        
        if 'broll_config' in processing_result and processing_result['broll_config'].get('generator'):
            self.broll_generator = processing_result['broll_config']['generator']

        self._update_summary_stats()
        self._populate_frame_grid()
        self._populate_broll_previews()
        self._populate_combined_videos()
        
        # Switch to appropriate tab based on available data
        if self.processing_result.get('combined_videos'):
            self.results_tabs.selected_index = 2  # Combined Videos tab
        elif self.broll_data:
            self.results_tabs.selected_index = 1  # B-roll Preview tab
        else:
            self.results_tabs.selected_index = 0  # Frame Extraction tab

        if self.page:
            self.page.update()

    def _update_summary_stats(self):
        # Frame extraction stats
        self.total_frames_text.value = str(len(self.processing_result.get('extracted_frames', [])))
        self.scenes_count_text.value = str(len(self.processing_result.get('scene_video_paths', [])))
        
        # B-roll stats
        combined_videos_count = len(self.processing_result.get('combined_videos', []))
        if combined_videos_count > 0:
            self.broll_videos_text.value = f"{combined_videos_count} Combined"
        else:
            self.broll_videos_text.value = str(len([s for s in self.broll_data.values() if s.get('selected_video')]))

        # General stats
        processing_time = self.processing_result.get('processing_time', 0)
        self.processing_time_text.value = f"{int(processing_time // 60):02d}:{int(processing_time % 60):02d}"
        
        # Calculate total size
        total_size_bytes = 0
        for path_list_key in ['saved_paths', 'scene_video_paths']:
            for path_str in self.processing_result.get(path_list_key, []):
                path = Path(path_str)
                if path.exists():
                    total_size_bytes += path.stat().st_size
        
        if total_size_bytes > 1024 * 1024:
            self.total_size_text.value = f"{total_size_bytes / (1024*1024):.2f} MB"
        else:
            self.total_size_text.value = f"{total_size_bytes / 1024:.2f} KB"

    def _populate_frame_grid(self):
        self.frame_grid.controls.clear()
        saved_paths = self.processing_result.get('saved_paths', [])
        for i, frame_data in enumerate(self.processing_result.get('extracted_frames', [])):
            if i < len(saved_paths):
                card = self._create_frame_card(Path(saved_paths[i]), frame_data)
                self.frame_grid.controls.append(card)

    def _create_frame_card(self, path: Path, data: Dict):
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Image(src=str(path), width=180, height=120, fit=ft.ImageFit.COVER, border_radius=ft.border_radius.all(8)),
                    ft.Text(f"Time: {data['timestamp']:.2f}s", size=10),
                    ft.Text(f"Similarity: {data['similarity_score']:.2f}", size=10),
                ]),
                padding=10
            )
        )

    def _populate_broll_previews(self):
        self.broll_preview_area.controls.clear()
        if not self.broll_data:
            self.broll_preview_area.controls.append(ft.Text("No B-roll was generated for this content.", style=ft.TextThemeStyle.HEADLINE_SMALL))
            return

        for i, sentence_data in self.broll_data.items():
            card = BrollPreviewCard(
                sentence_data=sentence_data,
                on_select_alternative=self.show_alternatives_dialog,
                on_custom_search=self.show_custom_search_dialog
            )
            self.broll_preview_area.controls.append(card)

    def show_alternatives_dialog(self, sentence_data: Dict):
        if not self.broll_generator:
            return

        exclude_ids = [sentence_data['selected_video']['id']] if sentence_data.get('selected_video') else []
        alternatives = self.broll_generator.get_alternatives(sentence_data['sentence_index'], sentence_data['keywords'], exclude_ids)
        
        dialog = VideoSelectorDialog(sentence_data, alternatives, self.on_alternative_selected)
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()

    def on_alternative_selected(self, sentence_data: Dict, new_video: Dict):
        # Find the sentence index and update the b-roll data
        sentence_index = sentence_data['sentence_index']
        self.broll_data[sentence_index]['selected_video'] = new_video
        self._populate_broll_previews() # Refresh UI
        self.page.update()

    def _populate_combined_videos(self):
        """Populate the combined videos tab"""
        self.combined_videos_area.controls.clear()
        
        combined_videos = self.processing_result.get('combined_videos', [])
        if not combined_videos:
            # Show message if no combined videos
            no_videos_message = ft.Container(
                content=ft.Column([
                    ft.Icon("movie_outlined", size=64, color=ft.Colors.GREY_400),
                    ft.Text("No Combined Videos", size=18, weight=ft.FontWeight.BOLD),
                    ft.Text("Combined videos will appear here when B-roll is enabled", 
                           size=14, color=ft.Colors.ON_SURFACE_VARIANT),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
                padding=50,
                alignment=ft.alignment.center
            )
            self.combined_videos_area.controls.append(no_videos_message)
            return
        
        # Display combined videos
        for video_data in combined_videos:
            video_card = self._create_combined_video_card(video_data)
            self.combined_videos_area.controls.append(video_card)

    def _create_combined_video_card(self, video_data: Dict):
        """Create a card for displaying combined video information"""
        video_file = Path(video_data['video_file'])
        sentence_text = video_data['sentence'][:100] + "..." if len(video_data['sentence']) > 100 else video_data['sentence']
        
        # Play button
        play_button = ft.IconButton(
            icon="play_circle_outline",
            tooltip="Play Video",
            icon_color=ft.Colors.PRIMARY,
            icon_size=32,
            on_click=lambda e, path=video_file: self._play_video(path)
        )
        
        # Open folder button
        folder_button = ft.IconButton(
            icon="folder_open",
            tooltip="Open in File Explorer",
            icon_color=ft.Colors.GREY_600,
            on_click=lambda e, path=video_file: self._open_video_location(path)
        )
        
        # Video info
        file_size = ""
        if video_file.exists():
            size_bytes = video_file.stat().st_size
            if size_bytes > 1024 * 1024:
                file_size = f"{size_bytes / (1024*1024):.1f} MB"
            else:
                file_size = f"{size_bytes / 1024:.1f} KB"
        
        card_content = ft.Container(
            content=ft.Row([
                # Video icon and info
                ft.Container(
                    content=ft.Column([
                        ft.Icon("video_file", size=40, color=ft.Colors.BLUE_400),
                        ft.Text(f"#{video_data['sentence_index'] + 1}", 
                               size=12, color=ft.Colors.ON_SURFACE_VARIANT)
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    width=60
                ),
                
                # Text content
                ft.Expanded(
                    child=ft.Column([
                        ft.Text(sentence_text, size=14, weight=ft.FontWeight.W_500),
                        ft.Text(f"Keywords: {', '.join(video_data.get('keywords', []))}", 
                               size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                        ft.Text(f"File: {video_file.name}", 
                               size=11, color=ft.Colors.ON_SURFACE_VARIANT),
                        ft.Text(f"Size: {file_size}", 
                               size=11, color=ft.Colors.ON_SURFACE_VARIANT) if file_size else ft.Container(),
                    ], spacing=5)
                ),
                
                # Action buttons
                ft.Column([
                    play_button,
                    folder_button,
                ], spacing=5)
            ], spacing=15),
            
            padding=15,
            margin=5,
            border_radius=10,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.WHITE,
            border=ft.border.all(1, ft.Colors.OUTLINE)
        )
        
        return card_content

    def _play_video(self, video_path: Path):
        """Play video file with default system player"""
        try:
            if platform.system() == "Windows":
                subprocess.Popen(["start", "", str(video_path)], shell=True)
            elif platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", str(video_path)])
            else:  # Linux
                subprocess.Popen(["xdg-open", str(video_path)])
        except Exception as e:
            print(f"Error opening video: {e}")

    def _open_video_location(self, video_path: Path):
        """Open the folder containing the video"""
        try:
            folder_path = video_path.parent
            if platform.system() == "Windows":
                subprocess.Popen(f'explorer /select,"{video_path}"')
            elif platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", "-R", str(video_path)])
            else:  # Linux
                subprocess.Popen(["xdg-open", str(folder_path)])
        except Exception as e:
            print(f"Error opening folder: {e}")

    def show_custom_search_dialog(self, sentence_data: Dict):
        dialog = CustomSearchDialog(sentence_data, self.on_custom_search)
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()

    def on_custom_search(self, sentence_data: Dict, query: str):
        if not self.broll_generator:
            return
        
        # Perform search and show alternatives dialog
        videos = self.broll_generator.search_custom_videos(query)
        dialog = VideoSelectorDialog(sentence_data, videos, self.on_alternative_selected)
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()

    def open_output_folder(self, e):
        output_dir = self.processing_result.get('output_dir')
        if output_dir and Path(output_dir).exists():
            if platform.system() == "Windows":
                subprocess.run(['explorer', str(output_dir)])
            elif platform.system() == "Darwin":
                subprocess.run(['open', str(output_dir)])
            else:
                subprocess.run(['xdg-open', str(output_dir)])

    def export_as_zip(self, e):
        # This would need to be implemented to zip all generated files
        # (frames, videos, b-roll data, etc.)
        self.page.show_snack_bar(ft.SnackBar(ft.Text("Export to ZIP not implemented yet."), open=True))

    def start_new_processing(self, e):
        self.main_window.show_upload_view()
