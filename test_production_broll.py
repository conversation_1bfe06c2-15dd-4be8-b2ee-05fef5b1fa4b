#!/usr/bin/env python3
"""
Test the production B-roll system
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.broll import BRollGenerator

def test_broll_system():
    """Test the complete B-roll system"""
    
    # Sample sentences from the business analyst script
    test_sentences = [
        "So you typed into Google, what is a business analyst.",
        "A business analyst is the bridge between people who dream and people who code.",
        "They translate messy business problems into requirements that developers can actually build.",
        "They interview stakeholders, gather requirements, maybe even draw some diagrams.",
        "They sit in meetings all day and argue gently with developers.",
        "They write user stories, process flow charts, acceptance criteria.",
        "You get to shape solutions and become the person who translates ideas into real working systems.",
    ]
    
    # Test with the provided API key
    api_key = "uKxpgNExFg7eaG0HxnWMQejOtIR8XR5vc3yNlEMq6BiOQbz9dwHn8Yd3"
    
    print("🎬 Testing Production B-Roll System")
    print("=" * 60)
    
    try:
        # Initialize generator
        print("🔧 Initializing B-roll generator...")
        generator = BRollGenerator(api_key)
        
        # Test connection
        print("🌐 Testing API connection...")
        if not generator.test_connection():
            print("❌ API connection failed!")
            return False
        print("✅ API connection successful!")
        
        # Generate B-roll data
        print(f"\n🎯 Processing {len(test_sentences)} sentences...")
        broll_data = generator.generate_broll_data(test_sentences)
        
        # Show results
        print("\n📊 B-ROLL GENERATION RESULTS")
        print("=" * 60)
        
        for i, data in broll_data.items():
            sentence = data['sentence'][:60] + "..." if len(data['sentence']) > 60 else data['sentence']
            keywords = ", ".join(data['keywords'])
            selected = data['selected_video']
            
            print(f"\n{i+1:2d}. \"{sentence}\"")
            print(f"    🔍 Keywords: {keywords}")
            
            if selected:
                print(f"    ✅ Selected video: {selected['quality']} {selected['duration']}s")
                print(f"       👤 By: {selected['author']} | 📏 {selected['width']}x{selected['height']}")
                print(f"       🔗 {selected['url'][:50]}...")
            else:
                print(f"    ❌ No video found")
        
        # Generate summary
        summary = generator.get_generation_summary(broll_data)
        print(f"\n📈 GENERATION SUMMARY")
        print("=" * 60)
        print(f"🎯 Success rate: {summary['success_rate']:.1f}% ({summary['successful_matches']}/{summary['total_sentences']})")
        print(f"🔤 Total keywords: {summary['total_keywords']} ({summary['unique_keywords']} unique)")
        print(f"⏱️  Avg video duration: {summary['average_video_duration']:.1f}s")
        print(f"📊 Most common keywords: {dict(summary['most_common_keywords'])}")
        
        # Test alternatives for first sentence
        if broll_data and 0 in broll_data:
            print(f"\n🔄 Testing alternatives for sentence 1...")
            first_sentence = broll_data[0]
            alternatives = generator.get_alternatives(
                0, 
                first_sentence['keywords'],
                exclude_video_ids=[first_sentence['selected_video']['id']] if first_sentence['selected_video'] else []
            )
            
            print(f"   Found {len(alternatives)} alternatives:")
            for j, alt in enumerate(alternatives[:3], 1):
                print(f"     {j}. {alt['quality']} {alt['duration']}s by {alt['author']}")
        
        # Test custom search
        print(f"\n🔍 Testing custom search...")
        custom_results = generator.search_custom_videos("office meeting professional", 5.0)
        print(f"   Custom search found {len(custom_results)} results")
        
        print(f"\n🎉 B-roll system test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_broll_system()
    sys.exit(0 if success else 1)