"""
F5-TTS View for text-to-speech generation
Converts text sentences to individual audio files using F5-TTS
"""

import flet as ft
from pathlib import Path
from typing import Optional, List
import threading
import tempfile
import json
from datetime import datetime
import re

from ..core.f5_tts import F5TTSProcessor
from ..utils.file_manager import FileManager
from .universal_progress_view import OperationType
from ..core.broll.broll_generator import BRollGenerator


class F5TTSView:
    """F5-TTS text-to-speech view"""
    
    def __init__(self):
        self.page = None  # Set by main window
        self.f5_tts = F5TTSProcessor()
        self.file_manager = FileManager()
        
        # Selected files
        self.selected_text_file = None
        self.selected_reference_audio = None
        
        # File pickers
        self.text_picker = ft.FilePicker(on_result=self.on_text_picker_result)
        self.audio_picker = ft.FilePicker(on_result=self.on_audio_picker_result)
        
        # Processing state
        self.is_processing = False
        self.progress_dialog = None
        
        # Settings controls
        self.model_dropdown = ft.Dropdown(
            label="F5-TTS Model",
            value="F5TTS_v1_Base",
            options=[],  # Will be populated dynamically
            width=200
        )
        
        # Load available models dynamically
        self.load_available_models()
        
        self.seed_field = ft.TextField(
            label="Seed",
            value="1193103530",
            width=150,
            input_filter=ft.NumbersOnlyInputFilter(),
            helper_text="Random seed for generation"
        )
        
        self.temperature_slider = ft.Slider(
            min=0.1,
            max=2.0,
            value=0.8,
            divisions=19,
            label="{value}",
            width=200
        )
        
        self.speed_slider = ft.Slider(
            min=0.5,
            max=2.0,
            value=1.0,
            divisions=15,
            label="{value}",
            width=200
        )
        
        self.target_rms_slider = ft.Slider(
            min=0.01,
            max=0.5,
            value=0.10,
            divisions=49,
            label="{value}",
            width=200
        )
        
        self.nfe_step_slider = ft.Slider(
            min=16,
            max=64,
            value=32,
            divisions=12,
            label="{value}",
            width=200
        )
        
        self.cfg_strength_slider = ft.Slider(
            min=1.0,
            max=5.0,
            value=2.0,
            divisions=8,
            label="{value}",
            width=200
        )
        
        self.enable_chunking = ft.Checkbox(
            label="Enable Chunking",
            value=True
        )
        
        self.max_chars_slider = ft.Slider(
            min=100,
            max=800,
            value=400,
            divisions=14,
            label="{value}",
            width=200
        )
        
        # Silence padding controls
        self.enable_silence_padding = ft.Checkbox(
            label="Add Silence Padding",
            value=True,
            tooltip="Add brief silence at the start and end of each audio file"
        )
        
        self.generate_individual_files = ft.Checkbox(
            label="Generate Individual Files",
            value=True,
            tooltip="Create separate audio files for each line. Uncheck to generate one long audio file."
        )
        
        self.start_silence_slider = ft.Slider(
            min=0.0,
            max=2.0,
            value=0.3,
            divisions=20,
            width=200,
            on_change=self._update_silence_labels
        )
        
        self.start_silence_label = ft.Text("0.3s", size=12, color=ft.Colors.GREY_600)
        
        self.end_silence_slider = ft.Slider(
            min=0.0,
            max=2.0,
            value=0.5,
            divisions=20,
            width=200,
            on_change=self._update_silence_labels
        )
        
        self.end_silence_label = ft.Text("0.5s", size=12, color=ft.Colors.GREY_600)
        
        # Text input area
        self.text_input = ft.TextField(
            label="Enter text (one sentence per line)",
            multiline=True,
            min_lines=5,
            max_lines=15,
            expand=True,
            hint_text="Type your text here, with each sentence on a new line...\nExample:\nHello, this is the first sentence.\nThis is the second sentence.\nAnd this is the third sentence.",
            on_change=self.on_text_change
        )
        
        # Sample scripts button
        self.sample_scripts_button = ft.ElevatedButton(
            "📋 Load Sample Scripts",
            on_click=self.load_sample_scripts,
            tooltip="Load sample sentences for testing voices"
        )
        
        # Reference audio text
        self.reference_text_input = ft.TextField(
            label="Reference Audio Text (auto-filled when voice selected)",
            hint_text="This text should match what's spoken in your reference audio. Will auto-fill with smart defaults - edit if needed.",
            multiline=True,
            min_lines=2,
            max_lines=4
        )
        
        # ComfyUI voice selection
        self.comfy_voice_dropdown = ft.Dropdown(
            label="ComfyUI Voices",
            hint_text="Select a voice from ComfyUI examples",
            width=300,
            on_change=self.on_comfy_voice_change
        )
        
        # Load available ComfyUI voices
        self.load_comfy_voices()
        
        # Model info panel (expandable)
        self.model_info_expanded = False
        self.model_info_panel = self.create_model_info_panel()
        
        # B-roll generation controls
        self.broll_enabled = ft.Checkbox(
            label="Auto-generate B-roll videos",
            value=False,
            tooltip="Automatically create video content for your generated audio using contextual keywords",
            on_change=self.on_broll_enabled_change
        )
        
        self.broll_expanded = False
        self.broll_generator = None  # Initialize lazily
        self.broll_status_text = ft.Text(
            "B-roll: Disabled",
            size=12,
            color=ft.Colors.GREY_600
        )
        
        # B-roll settings (initially hidden)
        self.broll_quality_dropdown = ft.Dropdown(
            label="Video Quality",
            value="hd",
            options=[
                ft.dropdown.Option("hd", "HD (1080p+)"),
                ft.dropdown.Option("sd", "SD (720p)"),
                ft.dropdown.Option("any", "Any Quality")
            ],
            width=150
        )
        
        self.broll_results_slider = ft.Slider(
            min=1,
            max=5,
            value=3,
            divisions=4,
            label="Results: {value}",
            width=150,
            tooltip="Number of video alternatives per sentence"
        )
        
        self.broll_keywords_preview = ft.Text(
            "Keywords will appear here after text analysis",
            size=12,
            color=ft.Colors.GREY_600,
            italic=True
        )
    
    def load_comfy_voices(self):
        """Load available ComfyUI voice examples and user custom voices"""
        try:
            # Load ComfyUI voices
            comfy_voices = self.f5_tts.get_available_comfy_voices()
            
            # Load user custom voices
            user_voices = []
            user_voices_dir = Path("user_voices")
            if user_voices_dir.exists():
                for voice_file in user_voices_dir.glob("*.wav"):
                    reference_file = user_voices_dir / f"{voice_file.stem}.reference.txt"
                    if reference_file.exists():
                        user_voices.append({
                            'name': f"🎤 {voice_file.stem}",  # Mark custom voices with icon
                            'path': str(voice_file),
                            'reference_path': str(reference_file),
                            'format': voice_file.suffix[1:],  # Remove the dot
                            'type': 'custom'
                        })
            
            # Combine all voices
            all_voices = (comfy_voices or []) + user_voices
            
            if all_voices:
                dropdown_options = [ft.dropdown.Option("", "-- Select ComfyUI Voice --")]
                
                # Add ComfyUI voices
                if comfy_voices:
                    for voice in comfy_voices:
                        dropdown_options.append(
                            ft.dropdown.Option(voice["name"], f"{voice['name']} ({voice['format'].upper()})")
                        )
                
                # Add custom voices
                if user_voices:
                    if comfy_voices:  # Add separator if we have both types
                        dropdown_options.append(ft.dropdown.Option("separator", "--- Custom Voices ---"))
                    for voice in user_voices:
                        dropdown_options.append(
                            ft.dropdown.Option(voice["name"], f"{voice['name']} ({voice['format'].upper()})")
                        )
                
                self.comfy_voice_dropdown.options = dropdown_options
                self.available_voices = all_voices
                
                comfy_count = len(comfy_voices) if comfy_voices else 0
                custom_count = len(user_voices)
                print(f"✅ Loaded {len(all_voices)} voices ({comfy_count} ComfyUI + {custom_count} custom)")
            else:
                self.comfy_voice_dropdown.options = [
                    ft.dropdown.Option("", "No voices found")
                ]
                self.available_voices = []
                print("⚠️ No voices found")
                
        except Exception as e:
            print(f"❌ Failed to load voices: {e}")
            self.comfy_voice_dropdown.options = [
                ft.dropdown.Option("", "Error loading voices")
            ]
            self.available_voices = []
    
    def load_available_models(self):
        """Load available F5-TTS models dynamically from the engine"""
        try:
            # Import the get_f5tts_models function from ChatterBox engine
            import sys
            import os
            
            # Add ChatterBox path to sys.path if not already there
            chatterbox_path = Path.cwd() / "chatterbox_srt_voice"
            if chatterbox_path.exists() and str(chatterbox_path) not in sys.path:
                sys.path.insert(0, str(chatterbox_path))
            
            from engines.f5tts import get_f5tts_models
            
            # Get all available models
            available_models = get_f5tts_models()
            print(f"🎯 Found {len(available_models)} F5-TTS models: {available_models}")
            
            # Create dropdown options with proper display names
            model_options = []
            
            # Define friendly display names for models
            model_display_names = {
                "E2TTS_Base": "E2TTS Base Model",
                "F5TTS_Base": "F5-TTS Base Model", 
                "F5TTS_v1_Base": "F5-TTS v1 Base Model",
                "F5TTS_Large": "F5-TTS Large Model",
                "F5-DE": "F5-TTS German",
                "F5-ES": "F5-TTS Spanish", 
                "F5-FR": "F5-TTS French",
                "F5-JP": "F5-TTS Japanese",
                "F5-IT": "F5-TTS Italian",
                "F5-TH": "F5-TTS Thai",
                "F5-PT-BR": "F5-TTS Portuguese (Brazil)"
            }
            
            for model in available_models:
                display_name = model_display_names.get(model, model)
                if model.startswith("local:"):
                    # Handle local models from ComfyUI
                    local_name = model.replace("local:", "")
                    display_name = f"Local: {local_name}"
                
                model_options.append(ft.dropdown.Option(model, display_name))
            
            # Update dropdown options
            self.model_dropdown.options = model_options
            
            # Set default value if available (prefer F5TTS_v1_Base as most versatile)
            if available_models and "F5TTS_v1_Base" in available_models:
                self.model_dropdown.value = "F5TTS_v1_Base"
            elif available_models and "E2TTS_Base" in available_models:
                self.model_dropdown.value = "E2TTS_Base"
            elif available_models:
                self.model_dropdown.value = available_models[0]
                
            print(f"✅ Loaded {len(model_options)} models into dropdown")
            
        except ImportError as e:
            print(f"⚠️ Could not import F5-TTS models function: {e}")
            # Fallback to hardcoded models
            self.model_dropdown.options = [
                ft.dropdown.Option("F5TTS_v1_Base", "F5-TTS v1 Base Model"),
                ft.dropdown.Option("E2TTS_Base", "E2TTS Base Model"),
                ft.dropdown.Option("F5TTS_Base", "F5-TTS Base Model"),
            ]
            self.model_dropdown.value = "F5TTS_v1_Base"
        except Exception as e:
            print(f"❌ Error loading F5-TTS models: {e}")
            # Fallback to hardcoded models
            self.model_dropdown.options = [
                ft.dropdown.Option("F5TTS_v1_Base", "F5-TTS v1 Base Model"),
                ft.dropdown.Option("E2TTS_Base", "E2TTS Base Model"),
                ft.dropdown.Option("F5TTS_Base", "F5-TTS Base Model"),
            ]
            self.model_dropdown.value = "F5TTS_v1_Base"
    
    def create_model_info_panel(self):
        """Create an expandable panel with F5-TTS model information"""
        
        # Model information data
        model_info = {
            "F5TTS_Base": {
                "name": "F5TTS Base",
                "description": "The original baseline F5-TTS model",
                "traits": "Uses Diffusion Transformer + ConvNeXt V2 backbone. Strong zero-shot ability.",
                "best_for": "General English narration, versatile voice cloning"
            },
            "F5TTS_v1_Base": {
                "name": "F5TTS v1 Base",
                "description": "Improved version of the base model",
                "traits": "Faster training & inference, better stability, refined voice quality.",
                "best_for": "Most versatile option for multilingual and expressive storytelling"
            },
            "E2TTS_Base": {
                "name": "E2TTS Base",
                "description": "Separate architecture (Flat-UNet Transformer)",
                "traits": "Closer reproduction of original research paper. Often used for benchmarking.",
                "best_for": "Research-style reproduction and experimentation"
            },
            "F5-DE": {
                "name": "F5-TTS German",
                "description": "German language-specific checkpoint",
                "traits": "Pretrained specifically for German speech patterns and phonetics.",
                "best_for": "German text-to-speech generation"
            },
            "F5-ES": {
                "name": "F5-TTS Spanish", 
                "description": "Spanish language-specific checkpoint",
                "traits": "Pretrained specifically for Spanish speech patterns and phonetics.",
                "best_for": "Spanish text-to-speech generation"
            },
            "F5-FR": {
                "name": "F5-TTS French",
                "description": "French language-specific checkpoint", 
                "traits": "Pretrained specifically for French speech patterns and phonetics.",
                "best_for": "French text-to-speech generation"
            },
            "F5-JP": {
                "name": "F5-TTS Japanese",
                "description": "Japanese language-specific checkpoint",
                "traits": "Pretrained specifically for Japanese speech patterns and phonetics.",
                "best_for": "Japanese text-to-speech generation"
            },
            "F5-IT": {
                "name": "F5-TTS Italian", 
                "description": "Italian language-specific checkpoint",
                "traits": "Pretrained specifically for Italian speech patterns and phonetics.",
                "best_for": "Italian text-to-speech generation"
            },
            "F5-TH": {
                "name": "F5-TTS Thai",
                "description": "Thai language-specific checkpoint",
                "traits": "Pretrained specifically for Thai speech patterns and phonetics.", 
                "best_for": "Thai text-to-speech generation"
            },
            "F5-PT-BR": {
                "name": "F5-TTS Portuguese (Brazil)",
                "description": "Brazilian Portuguese language-specific checkpoint",
                "traits": "Pretrained specifically for Brazilian Portuguese speech patterns.",
                "best_for": "Brazilian Portuguese text-to-speech generation"
            }
        }
        
        # Theme-aware colors - safe defaults if page is not available yet
        # Use colors that work well in both light and dark themes
        is_dark = self.page and self.page.theme_mode == ft.ThemeMode.DARK
        
        # Card colors
        if is_dark:
            card_bg = ft.Colors.GREY_800
            text_color = ft.Colors.WHITE
            description_color = ft.Colors.GREY_300
            traits_color = ft.Colors.GREY_400
            features_bg = ft.Colors.GREY_700
            panel_bg = ft.Colors.GREY_800
        else:
            card_bg = ft.Colors.WHITE
            text_color = ft.Colors.BLACK
            description_color = ft.Colors.GREY_700
            traits_color = ft.Colors.GREY_600
            features_bg = ft.Colors.BLUE_50
            panel_bg = ft.Colors.WHITE
        
        # Create expandable content
        model_cards = []
        for model_key, info in model_info.items():
            card = ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon("psychology", size=20, color="primary"),
                        ft.Text(info["name"], size=14, weight=ft.FontWeight.W_600, color=ft.Colors.PRIMARY),
                    ], spacing=8),
                    ft.Text(info["description"], size=12, color=description_color),
                    ft.Text(f"Traits: {info['traits']}", size=11, color=traits_color),
                    ft.Text(f"Best for: {info['best_for']}", size=11, color=ft.Colors.GREEN, weight=ft.FontWeight.W_500),
                ], spacing=4),
                padding=12,
                bgcolor=card_bg,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.GREY_300 if not is_dark else ft.Colors.GREY_600),
                margin=ft.margin.only(bottom=8)
            )
            model_cards.append(card)
        
        # Core features section
        features_section = ft.Container(
            content=ft.Column([
                ft.Text("⚙️ Core F5-TTS Features", size=16, weight=ft.FontWeight.W_600, color=ft.Colors.SECONDARY),
                ft.Text("• Flow Matching: Faster, more efficient speech generation vs traditional diffusion", size=12, color=text_color),
                ft.Text("• Sway Sampling: Improves naturalness without retraining", size=12, color=text_color),
                ft.Text("• Multilingual Training: 100K+ hours dataset, handles code-switching", size=12, color=text_color), 
                ft.Text("• Zero-Shot Voices: Clone voices from short reference clips", size=12, color=text_color),
                ft.Text("• Speed Control: Adjust speaking rate at inference time", size=12, color=text_color),
            ], spacing=6),
            padding=12,
            bgcolor=features_bg,
            border_radius=8,
            margin=ft.margin.only(top=10)
        )
        
        # Toggle button
        self.model_info_toggle_button = ft.ElevatedButton(
            text="📖 Show Model Guide",
            icon="info_outline",
            on_click=self.toggle_model_info,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE,
                color=ft.Colors.WHITE
            )
        )
        
        # Create the expandable container (initially hidden)
        self.model_info_content = ft.Container(
            content=ft.Column([
                ft.Text("🗣️ F5-TTS Model Variants", size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.PRIMARY),
                ft.Container(height=10),
                *model_cards,
                features_section,
            ], scroll=ft.ScrollMode.AUTO),
            padding=20,
            bgcolor=panel_bg,
            border_radius=12,
            border=ft.border.all(2, ft.Colors.PRIMARY),
            visible=False,  # Initially hidden
            animate_opacity=300,
        )
        
        return ft.Column([
            self.model_info_toggle_button,
            self.model_info_content,
        ], spacing=10)
    
    def toggle_model_info(self, e):
        """Toggle the visibility of model info panel"""
        self.model_info_expanded = not self.model_info_expanded
        self.model_info_content.visible = self.model_info_expanded
        
        if self.model_info_expanded:
            self.model_info_toggle_button.text = "📖 Hide Model Guide"
            self.model_info_toggle_button.icon = "keyboard_arrow_up"
        else:
            self.model_info_toggle_button.text = "📖 Show Model Guide"
            self.model_info_toggle_button.icon = "info_outline"
            
        self.update_ui()
    
    def on_comfy_voice_change(self, e):
        """Handle ComfyUI voice selection"""
        selected_voice = e.control.value
        
        # Skip separator items
        if selected_voice == "separator":
            e.control.value = ""
            self.update_ui()
            return
        
        if selected_voice and hasattr(self, 'available_voices'):
            # Find the selected voice (handle both ComfyUI and custom voices)
            voice_info = next((v for v in self.available_voices if v["name"] == selected_voice), None)
            
            if voice_info:
                # Set the reference audio automatically
                voice_path = Path(voice_info["path"])
                if voice_path.exists():
                    self.selected_reference_audio = voice_path
                    
                    # Different display for custom vs ComfyUI voices
                    if voice_info.get('type') == 'custom':
                        self.audio_file_text.value = f"✅ Custom Voice: {selected_voice}"
                    else:
                        self.audio_file_text.value = f"✅ ComfyUI Voice: {voice_info['name']}"
                    
                    self.audio_file_text.color = ft.Colors.GREEN
                    
                    # Load reference text
                    if voice_info.get('type') == 'custom':
                        # For custom voices, load from reference file
                        reference_file = Path(voice_info['reference_path'])
                        if reference_file.exists():
                            try:
                                with open(reference_file, 'r', encoding='utf-8') as f:
                                    self.reference_text_input.value = f.read().strip()
                                print(f"✅ Loaded custom voice reference text")
                            except Exception as e:
                                print(f"⚠️ Failed to load reference text: {e}")
                                self.reference_text_input.value = self.generate_smart_reference_text(voice_path)
                    else:
                        # For ComfyUI voices, use existing logic
                        if 'reference_text' in voice_info and voice_info['reference_text'].strip():
                            self.reference_text_input.value = voice_info['reference_text']
                            print(f"✅ Loaded ComfyUI reference text: {voice_info['reference_text'][:100]}...")
                        else:
                            # Fallback to suggested text
                            suggested_text = self.get_suggested_reference_text(selected_voice)
                            if suggested_text:
                                self.reference_text_input.value = suggested_text
                    
                    print(f"🎤 Selected voice: {selected_voice}")
                    self.update_ui()
    
    def _update_silence_labels(self, e):
        """Update slider labels with proper decimal formatting"""
        value = f"{e.control.value:.1f}s"
        
        # Update the corresponding text label
        if e.control == self.start_silence_slider:
            self.start_silence_label.value = value
        elif e.control == self.end_silence_slider:
            self.end_silence_label.value = value
        
        self.page.update()
    
    def get_suggested_reference_text(self, voice_name: str) -> str:
        """Get suggested reference text based on voice name"""
        # Common reference texts for different voice types
        reference_texts = {
            "David_Attenborough": "The natural world is full of wonders that continue to amaze us.",
            "morgan_freeman": "In the beginning, there was nothing but darkness and silence.",
            "obama": "My fellow Americans, we face challenges that require us to work together.",
            "trump": "This is going to be tremendous, absolutely tremendous.",
            "joe_rogan": "That's entirely possible, and it's fascinating to think about.",
            "default": "This is a sample of my voice for reference audio cloning."
        }
        
        # Try to match voice name to known references
        voice_lower = voice_name.lower()
        for key, text in reference_texts.items():
            if key.lower() in voice_lower:
                return text
        
        return reference_texts["default"]
    
    def initialize_broll_generator(self):
        """Initialize B-roll generator lazily"""
        if self.broll_generator is None:
            try:
                # Use the provided API key from the conversation summary
                api_key = "uKxpgNExFg7eaG0HxnWMQejOtIR8XR5vc3yNlEMq6BiOQbz9dwHn8Yd3"
                self.broll_generator = BRollGenerator(api_key)
                
                # Test connection
                if self.broll_generator.test_connection():
                    self.broll_status_text.value = "B-roll: Ready"
                    self.broll_status_text.color = ft.Colors.GREEN
                    return True
                else:
                    self.broll_status_text.value = "B-roll: API connection failed"
                    self.broll_status_text.color = ft.Colors.RED
                    return False
                    
            except Exception as e:
                self.broll_status_text.value = f"B-roll: Error - {str(e)}"
                self.broll_status_text.color = ft.Colors.RED
                return False
        return True
    
    def on_broll_enabled_change(self, e):
        """Handle B-roll enable/disable"""
        if self.broll_enabled.value:
            if self.initialize_broll_generator():
                self.broll_status_text.value = "B-roll: Ready"
                self.broll_status_text.color = ft.Colors.GREEN
            else:
                self.broll_enabled.value = False
                if self.page:
                    self.page.update()
        else:
            self.broll_status_text.value = "B-roll: Disabled"
            self.broll_status_text.color = ft.Colors.GREY_600
        
        if self.page:
            self.page.update()
    
    def toggle_broll_settings(self, e):
        """Toggle B-roll settings visibility"""
        self.broll_expanded = not self.broll_expanded
        if hasattr(self, 'broll_settings_content'):
            self.broll_settings_content.visible = self.broll_expanded
            # Update button text
            if hasattr(self, 'broll_settings_toggle'):
                icon = "expand_less" if self.broll_expanded else "expand_more"
                self.broll_settings_toggle.icon = icon
        
        if self.page:
            self.page.update()
    
    def update_broll_keywords_preview(self, text: str):
        """Update the keywords preview based on current text"""
        if not self.broll_enabled.value:
            return
        
        try:
            # Extract sentences and get keywords preview
            sentences = self.get_sentences_from_input()
            if sentences and self.initialize_broll_generator():
                # Get keywords for all sentences
                keywords_dict = self.broll_generator.keyword_extractor.extract_keywords(sentences)
                
                # Collect all keywords from first few sentences
                all_keywords = []
                for i in range(min(3, len(sentences))):  # First 3 sentences
                    if i in keywords_dict:
                        all_keywords.extend(keywords_dict[i])
                
                unique_keywords = list(dict.fromkeys(all_keywords))  # Remove duplicates while preserving order
                if unique_keywords:
                    # Convert to strings and join
                    keyword_strings = [str(kw) for kw in unique_keywords]
                    preview_text = f"Keywords: {', '.join(keyword_strings[:8])}"
                    if len(unique_keywords) > 8:
                        preview_text += f" (+{len(unique_keywords) - 8} more)"
                else:
                    preview_text = "No keywords extracted"
                
                self.broll_keywords_preview.value = preview_text
                self.broll_keywords_preview.color = ft.Colors.BLUE
            else:
                self.broll_keywords_preview.value = "Enter text to see keyword preview"
                self.broll_keywords_preview.color = ft.Colors.GREY_600
                
        except Exception as e:
            self.broll_keywords_preview.value = f"Preview error: {str(e)}"
            self.broll_keywords_preview.color = ft.Colors.RED
        
        if self.page:
            self.page.update()
    
    def on_text_change(self, e):
        """Handle text input changes to update keyword preview"""
        # Update regular UI state
        self.update_ui()
        
        # Update B-roll keywords preview
        self.update_broll_keywords_preview(e.control.value)

    def build(self):
        """Build the F5-TTS view"""
        
        # Reference audio selection
        self.audio_file_text = ft.Text(
            "No reference audio selected (will use default voice)",
            size=14,
            color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        self.audio_upload_area = ft.Container(
            content=ft.Column([
                ft.Icon(
                    ft.Icons.MIC_OUTLINED,
                    size=48,
                    color=ft.Colors.SECONDARY
                ),
                ft.Text(
                    "Reference Audio (Optional)",
                    size=16,
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.SECONDARY
                ),
                ft.Text(
                    "Select audio to clone voice (WAV, MP3, M4A, FLAC)",
                    size=12,
                    color=ft.Colors.ON_SURFACE_VARIANT
                ),
                
                # ComfyUI voice selection
                ft.Container(
                    content=ft.Column([
                        ft.Text("ComfyUI Voice Examples", size=14, weight=ft.FontWeight.W_500),
                        self.comfy_voice_dropdown,
                        ft.Text("OR", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                    margin=ft.margin.symmetric(vertical=10)
                ),
                
                ft.ElevatedButton(
                    "Browse Custom Audio",
                    icon=ft.Icons.MIC,
                    on_click=self.open_audio_picker,
                    style=ft.ButtonStyle(
                        bgcolor=ft.Colors.SECONDARY,
                        color=ft.Colors.WHITE,
                    )
                ),
                self.audio_file_text,
                self.reference_text_input
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=12
            ),
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=12,
            padding=30,
            margin=20,
            border=ft.border.all(2, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            ink=True,
            on_click=self.open_audio_picker,
        )
        
        # Text input section
        self.text_file_text = ft.Text(
            "No text file selected",
            size=14,
            color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        text_input_section = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.TEXT_SNIPPET_OUTLINED, size=24, color=ft.Colors.PRIMARY),
                    ft.Text("Text Input", size=18, weight=ft.FontWeight.W_600, color=ft.Colors.PRIMARY),
                ], spacing=8),
                
                ft.Row([
                    ft.ElevatedButton(
                        "Load from File",
                        icon=ft.Icons.FOLDER_OPEN,
                        on_click=self.open_text_picker,
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.PRIMARY,
                            color=ft.Colors.WHITE
                        )
                    ),
                    self.text_file_text,
                ], spacing=15),
                
                ft.Row([
                    self.sample_scripts_button,
                    ft.Text("|", color=ft.Colors.GREY_400),
                    ft.Text("Quick start with sample sentences", size=12, color=ft.Colors.GREY_600)
                ], spacing=10),
                
                self.text_input
            ], spacing=15),
            padding=20,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=12,
            margin=ft.margin.symmetric(horizontal=20)
        )
        
        # Settings panel
        settings_panel = ft.Container(
            content=ft.Column([
                ft.Text("F5-TTS Settings", size=18, weight=ft.FontWeight.W_600),
                
                ft.Row([
                    self.model_dropdown,
                    self.seed_field,
                ], spacing=20),
                
                ft.Column([
                    ft.Text("Temperature", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                    self.temperature_slider,
                ], spacing=5),
                
                ft.Row([
                    ft.Column([
                        ft.Text("Speed", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                        self.speed_slider,
                    ], spacing=5),
                    ft.Column([
                        ft.Text("Target RMS", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                        self.target_rms_slider,
                    ], spacing=5),
                ], spacing=30),
                
                ft.Row([
                    ft.Column([
                        ft.Text("NFE Steps", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                        self.nfe_step_slider,
                    ], spacing=5),
                    ft.Column([
                        ft.Text("CFG Strength", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                        self.cfg_strength_slider,
                    ], spacing=5),
                ], spacing=30),
                
                ft.Row([
                    self.enable_chunking,
                    ft.Column([
                        ft.Text("Max Chars per Chunk", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                        self.max_chars_slider,
                    ], spacing=5),
                ], spacing=30),
                
                # Silence Padding Controls
                ft.Row([
                    self.enable_silence_padding,
                    ft.Column([
                        ft.Text("Start Silence", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                        self.start_silence_slider,
                        self.start_silence_label,
                    ], spacing=5),
                    ft.Column([
                        ft.Text("End Silence", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                        self.end_silence_slider,
                        self.end_silence_label,
                    ], spacing=5),
                ], spacing=30),
                
                # Audio Output Controls
                ft.Row([
                    self.generate_individual_files,
                ], spacing=30),
                
            ], spacing=20),
            padding=20,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=12,
            margin=ft.margin.symmetric(horizontal=20, vertical=10)
        )
        
        # B-roll generation panel
        self.broll_settings_toggle = ft.IconButton(
            icon=ft.Icons.EXPAND_MORE,
            tooltip="Show B-roll settings",
            on_click=self.toggle_broll_settings
        )
        
        self.broll_settings_content = ft.Container(
            content=ft.Column([
                ft.Row([
                    self.broll_quality_dropdown,
                    ft.Column([
                        ft.Text("Video Results", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                        self.broll_results_slider,
                    ], spacing=5),
                ], spacing=30),
                
                ft.Container(
                    content=self.broll_keywords_preview,
                    padding=10,
                    bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_50,
                    border_radius=8,
                    margin=ft.margin.only(top=10)
                ),
            ], spacing=15),
            visible=False,
            animate_opacity=300
        )
        
        broll_panel = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.Icons.MOVIE_CREATION_OUTLINED, size=20, color=ft.Colors.BLUE),
                    ft.Text("B-Roll Video Generation", size=16, weight=ft.FontWeight.W_600, color=ft.Colors.BLUE),
                    ft.Container(expand=True),  # Spacer
                    self.broll_status_text,
                    self.broll_settings_toggle,
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                self.broll_enabled,
                self.broll_settings_content,
            ], spacing=10),
            padding=20,
            bgcolor=ft.Colors.BLUE_GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.BLUE_GREY_50,
            border_radius=12,
            border=ft.border.all(2, ft.Colors.BLUE_GREY_200),
            margin=ft.margin.symmetric(horizontal=20, vertical=10)
        )
        
        # Generate button and status
        self.generate_button = ft.ElevatedButton(
            text="Generate Speech",
            icon=ft.Icons.RECORD_VOICE_OVER,
            on_click=self.start_generation,
            disabled=False,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.GREEN,
                color=ft.Colors.WHITE,
                shape=ft.RoundedRectangleBorder(radius=8)
            ),
            height=48,
            width=200
        )
        
        self.status_text = ft.Text(
            "Enter text or load a file to begin",
            size=14,
            color=ft.Colors.ON_SURFACE_VARIANT,
            text_align=ft.TextAlign.CENTER
        )
        
        # Main layout
        return ft.Container(
            content=ft.Column([
                # Header
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "F5-TTS Voice Generator",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.PRIMARY
                        ),
                        ft.Text(
                            "Generate individual audio files from text sentences using F5-TTS",
                            size=14,
                            color=ft.Colors.ON_SURFACE_VARIANT
                        )
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                    ),
                    margin=ft.margin.only(bottom=20)
                ),
                
                # Model Info Panel
                self.model_info_panel,
                
                # Reference audio section
                self.audio_upload_area,
                
                # Text input section
                text_input_section,
                
                # Settings panel  
                settings_panel,
                
                # B-roll panel
                broll_panel,
                
                # Action area
                ft.Container(
                    content=ft.Column([
                        self.generate_button,
                        self.status_text
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=15
                    ),
                    margin=ft.margin.symmetric(vertical=20)
                ),
                
                # Hidden file pickers
                self.text_picker,
                self.audio_picker
                
            ], 
            spacing=0,
            scroll=ft.ScrollMode.AUTO
            ),
            expand=True,
            padding=20
        )
    
    def open_text_picker(self, e=None):
        """Open text file picker"""
        if self.page:
            self.page.overlay.append(self.text_picker)
            self.page.update()
            
        self.text_picker.pick_files(
            dialog_title="Select Text File",
            allowed_extensions=["txt"],
            allow_multiple=False
        )
    
    def open_audio_picker(self, e=None):
        """Open reference audio file picker"""
        if self.page:
            self.page.overlay.append(self.audio_picker)
            self.page.update()
            
        self.audio_picker.pick_files(
            dialog_title="Select Reference Audio File",
            allowed_extensions=["wav", "mp3", "m4a", "flac", "ogg"],
            allow_multiple=False
        )
    
    def on_text_picker_result(self, e: ft.FilePickerResultEvent):
        """Handle text file selection"""
        if e.files:
            file_path = Path(e.files[0].path)
            if self.validate_text_file(file_path):
                self.selected_text_file = file_path
                self.text_file_text.value = f"✓ {file_path.name}"
                self.text_file_text.color = ft.Colors.GREEN
                
                # Load text content
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        self.text_input.value = content
                        self.update_ui()
                except Exception as e:
                    self.show_error(f"Error loading text file: {str(e)}")
    
    def on_audio_picker_result(self, e: ft.FilePickerResultEvent):
        """Handle reference audio file selection"""
        if e.files:
            file_path = Path(e.files[0].path)
            if self.validate_audio_file(file_path):
                self.selected_reference_audio = file_path
                self.audio_file_text.value = f"✓ {file_path.name}"
                self.audio_file_text.color = ft.Colors.GREEN
                
                # Auto-fill reference text with smart default
                if not self.reference_text_input.value.strip():
                    default_text = self.generate_smart_reference_text(file_path)
                    self.reference_text_input.value = default_text
                    print(f"🤖 Auto-filled reference text: {default_text}")
                
                # Ask if user wants to save this voice for future use
                self.show_save_voice_dialog(file_path)
                
                self.update_ui()
    
    def generate_smart_reference_text(self, audio_path: Path) -> str:
        """Generate smart default reference text based on filename and context"""
        filename = audio_path.stem.lower()
        
        # Check for specific known voices first
        if "prince" in filename:
            return "So just to clarify... what we really need to do is create this process, which will streamline all our backlog requirements."
        
        # Check if this might be from Eleven Labs (common patterns)
        if any(pattern in filename for pattern in ['eleven', '11labs', 'elevenlabs', 'studio', 'quality']):
            return "The conversation flowed naturally as we discussed the upcoming project timeline."
        
        # For other custom voices, use a shorter, more neutral text
        # This is shorter than David Attenborough's text to avoid speed issues
        return "The meeting started promptly at ten this morning."
    
    def show_save_voice_dialog(self, audio_path: Path):
        """Show dialog asking if user wants to save the voice for future use"""
        if not hasattr(self, 'page') or not self.page:
            return
            
        voice_name = audio_path.stem
        
        def save_voice(e):
            self.save_custom_voice(audio_path, voice_name)
            dialog.open = False
            self.page.update()
            
        def cancel_save(e):
            dialog.open = False
            self.page.update()
        
        dialog = ft.AlertDialog(
            title=ft.Text("Save Custom Voice?"),
            content=ft.Column([
                ft.Text(f"Would you like to save '{voice_name}' to your voice library for future use?"),
                ft.Text("This will copy the audio file and reference text to your saved voices.", size=12, color=ft.Colors.GREY_600)
            ], height=80, spacing=10),
            actions=[
                ft.TextButton("Save Voice", on_click=save_voice),
                ft.TextButton("Use Once", on_click=cancel_save)
            ]
        )
        
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()
    
    def load_sample_scripts(self, e):
        """Load sample scripts into the text input area"""
        sample_scripts = [
            "Welcome to our presentation about artificial intelligence.",
            "The weather forecast shows sunny skies for the weekend.",
            "Please remember to save your work before closing the application.",
            "Innovation drives progress in technology and science.",
            "The museum opens at nine in the morning every day.",
            "Customer service representatives are available to assist you.",
            "The conference will feature speakers from around the world.",
            "Environmental conservation requires global cooperation and commitment.",
            "Digital transformation is reshaping modern business practices.",
            "Thank you for your attention and participation today."
        ]
        
        # Join scripts with newlines (one sentence per line)
        self.text_input.value = "\n".join(sample_scripts)
        
        # Update the UI
        if hasattr(self, 'page') and self.page:
            self.page.update()
        
        print("📋 Loaded sample scripts for voice testing")
    
    def save_custom_voice(self, audio_path: Path, voice_name: str):
        """Save custom voice to user voice library"""
        try:
            # Create user voices directory
            user_voices_dir = Path("user_voices")
            user_voices_dir.mkdir(exist_ok=True)
            
            # Copy audio file
            saved_audio_path = user_voices_dir / f"{voice_name}{audio_path.suffix}"
            import shutil
            shutil.copy2(audio_path, saved_audio_path)
            
            # Save reference text
            reference_text_path = user_voices_dir / f"{voice_name}.reference.txt"
            with open(reference_text_path, 'w', encoding='utf-8') as f:
                f.write(self.reference_text_input.value or self.generate_smart_reference_text(audio_path))
            
            print(f"💾 Saved custom voice: {voice_name}")
            print(f"   Audio: {saved_audio_path}")
            print(f"   Reference: {reference_text_path}")
            
            # Refresh available voices
            self.load_comfy_voices()  # This should also load custom voices now
            
            # Show success message
            if hasattr(self, 'page') and self.page:
                self.page.show_snack_bar(ft.SnackBar(
                    ft.Text(f"✅ Saved '{voice_name}' to voice library!"),
                    bgcolor=ft.Colors.GREEN
                ))
            
        except Exception as e:
            print(f"❌ Failed to save custom voice: {e}")
            if hasattr(self, 'page') and self.page:
                self.page.show_snack_bar(ft.SnackBar(
                    ft.Text(f"❌ Failed to save voice: {str(e)}"),
                    bgcolor=ft.Colors.RED
                ))
    
    def validate_text_file(self, file_path: Path) -> bool:
        """Validate text file"""
        if not file_path.exists():
            self.show_error("Text file does not exist")
            return False
            
        if file_path.suffix.lower() != '.txt':
            self.show_error("Only .txt files are supported")
            return False
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    self.show_error("Text file is empty")
                    return False
        except Exception as e:
            self.show_error(f"Error reading text file: {str(e)}")
            return False
            
        return True
    
    def validate_audio_file(self, file_path: Path) -> bool:
        """Validate reference audio file"""
        if not file_path.exists():
            self.show_error("Audio file does not exist")
            return False
            
        if file_path.suffix.lower() not in ['.wav', '.mp3', '.m4a', '.flac', '.ogg']:
            self.show_error("Unsupported audio format")
            return False
            
        if file_path.stat().st_size == 0:
            self.show_error("Audio file is empty")
            return False
            
        return True
    
    def get_sentences_from_input(self) -> List[str]:
        """Extract sentences from text input"""
        text = self.text_input.value.strip() if self.text_input.value else ""
        if not text:
            return []
        
        # Split by lines only - each line becomes one audio file
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        return lines
    
    def get_tts_parameters(self) -> dict:
        """Get current TTS parameters from UI"""
        return {
            'model': self.model_dropdown.value,
            'seed': int(self.seed_field.value) if self.seed_field.value.isdigit() else 1193103530,
            'temperature': self.temperature_slider.value,
            'speed': self.speed_slider.value,
            'target_rms': self.target_rms_slider.value,
            'nfe_step': int(self.nfe_step_slider.value),
            'cfg_strength': self.cfg_strength_slider.value,
            'enable_chunking': self.enable_chunking.value,
            'max_chars_per_chunk': int(self.max_chars_slider.value),
            'add_silence_padding': self.enable_silence_padding.value,
            'start_silence': self.start_silence_slider.value,
            'end_silence': self.end_silence_slider.value,
            'generate_individual_files': self.generate_individual_files.value
        }
    
    def update_ui(self):
        """Update UI state"""
        sentences = self.get_sentences_from_input()
        
        if sentences:
            self.status_text.value = f"Ready to generate {len(sentences)} audio files"
            self.status_text.color = ft.Colors.GREEN
        else:
            self.status_text.value = "Enter text or load a file to begin"
            self.status_text.color = ft.Colors.ON_SURFACE_VARIANT
        
        if self.page:
            self.page.update()
    
    def start_generation(self, e=None):
        """Start TTS generation process using universal progress view"""
        if self.is_processing:
            return
        
        sentences = self.get_sentences_from_input()
        if not sentences:
            self.show_error("Please enter text or load a text file")
            return
        
        self.is_processing = True
        
        # Switch to progress tab and start processing
        self._switch_to_progress_tab(sentences)
    
    def _switch_to_progress_tab(self, sentences: List[str]):
        """Switch to the progress tab to show F5-TTS processing"""
        # Get reference to main window through page
        if hasattr(self.page, 'main_window'):
            main_window = self.page.main_window
            main_window.show_progress_view()
            
            # Start F5-TTS processing using universal progress view
            progress_view = main_window.progress_view
            if progress_view:
                # Get TTS parameters
                params = self.get_tts_parameters()
                
                # Configure for F5-TTS processing
                config = {
                    'sentences': sentences,
                    'text_file': self.selected_text_file,
                    'reference_audio': self.selected_reference_audio,
                    'reference_text': self.reference_text_input.value.strip() if self.reference_text_input.value else "",
                    'model_name': self.model_dropdown.value,
                    'params': params,
                    'broll_enabled': self.broll_enabled.value,
                    'broll_config': {
                        'quality': self.broll_quality_dropdown.value,
                        'results_per_keyword': int(self.broll_results_slider.value),
                        'generator': self.broll_generator if self.broll_enabled.value else None
                    } if self.broll_enabled.value else None
                }
                
                # Start F5-TTS processing operation
                progress_view.start_operation(
                    OperationType.F5_TTS_GENERATION,
                    self.f5_tts,
                    config
                )
                
                print("✅ Started F5-TTS generation with universal progress view")
            else:
                print("⚠️ Progress view not found")
        else:
            print("⚠️ Cannot switch to progress tab - main_window reference not found")
    
    def process_tts_generation(self, sentences: List[str]):
        """Process TTS generation in background thread"""
        try:
            # Load model if not already loaded
            if not self.f5_tts.model_loaded:
                self.update_progress("Loading F5-TTS model...", 0.05)
                self.f5_tts.load_model(
                    model_name=self.model_dropdown.value,
                    progress_callback=self.update_progress
                )
            
            # Set reference audio if provided
            if self.selected_reference_audio:
                self.update_progress("Setting reference audio...", 0.15)
                reference_text = self.reference_text_input.value.strip() if self.reference_text_input.value else ""
                self.f5_tts.set_reference_audio(self.selected_reference_audio, reference_text)
            
            # Create output directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = Path.cwd() / f"f5_tts_output_{timestamp}"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Get TTS parameters
            params = self.get_tts_parameters()
            
            # Process sentences
            self.update_progress("Generating speech...", 0.2)
            results = self.f5_tts.process_sentences(
                sentences=sentences,
                output_dir=output_dir,
                params=params,
                progress_callback=lambda msg, progress: self.update_progress(msg, 0.2 + progress * 0.75)
            )
            
            # Show completion
            self.show_completion(output_dir, results)
            
        except Exception as e:
            self.show_error(f"Error generating speech: {str(e)}")
        finally:
            self.is_processing = False
            self.close_progress_dialog()
    
    def update_progress(self, message: str, progress: float):
        """Update progress dialog - thread-safe"""
        if self.progress_dialog and self.page and hasattr(self, 'progress_text') and hasattr(self, 'progress_bar'):
            try:
                self.progress_text.value = message
                self.progress_bar.value = progress
                self.page.update()
            except Exception as e:
                print(f"Progress update error: {e}")
    
    def show_progress_dialog(self):
        """Show progress dialog"""
        self.progress_text = ft.Text("Initializing...", size=14)
        self.progress_bar = ft.ProgressBar(value=0, width=300)
        self.progress_info = ft.Text(
            "This may take several minutes for the first run", 
            size=12, color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        self.progress_dialog = ft.AlertDialog(
            title=ft.Text("🎤 Generating Speech", size=18),
            content=ft.Column([
                self.progress_text,
                self.progress_bar,
                self.progress_info
            ], height=120, spacing=15),
            modal=True
        )
        
        if self.page:
            self.page.dialog = self.progress_dialog
            self.progress_dialog.open = True
            self.page.update()
    
    def close_progress_dialog(self):
        """Close progress dialog"""
        if self.progress_dialog and self.page:
            self.progress_dialog.open = False
            self.page.update()
    
    def show_completion(self, output_dir: Path, results: dict):
        """Show completion dialog"""
        success_count = results['success_count']
        failed_count = results['failed_count']
        merged_file = results.get('merged_file')
        
        content_items = [
            ft.Text(f"Successfully generated: {success_count} audio files"),
        ]
        
        if failed_count > 0:
            content_items.append(ft.Text(f"Failed: {failed_count} files"))
        
        if merged_file:
            content_items.append(ft.Text(f"Merged file created: {merged_file.name}", color=ft.Colors.BLUE))
        
        content_items.extend([
            ft.Text(f"Output directory: {output_dir}"),
            ft.ElevatedButton(
                "Open Output Folder",
                icon=ft.Icons.FOLDER_OPEN,
                on_click=lambda e: self.open_folder(output_dir)
            )
        ])
        
        completion_dialog = ft.AlertDialog(
            title=ft.Text("F5-TTS Generation Complete!", color=ft.Colors.GREEN),
            content=ft.Column(content_items, height=180, spacing=15),
            actions=[
                ft.TextButton("OK", on_click=lambda e: self.close_completion_dialog())
            ]
        )
        
        if self.page:
            self.page.dialog = completion_dialog
            completion_dialog.open = True
            self.page.update()
    
    def close_completion_dialog(self):
        """Close completion dialog"""
        if self.page and self.page.dialog:
            self.page.dialog.open = False
            self.page.update()
    
    def show_error(self, message: str):
        """Show error dialog"""
        error_dialog = ft.AlertDialog(
            title=ft.Text("Error", color=ft.Colors.RED),
            content=ft.Text(message),
            actions=[
                ft.TextButton("OK", on_click=lambda e: self.close_error_dialog())
            ]
        )
        
        if self.page:
            self.page.dialog = error_dialog
            error_dialog.open = True
            self.page.update()
    
    def close_error_dialog(self):
        """Close error dialog"""
        if self.page and self.page.dialog:
            self.page.dialog.open = False
            self.page.update()
    
    def open_folder(self, folder_path: Path):
        """Open folder in file explorer"""
        import subprocess
        import sys
        
        try:
            if sys.platform == "win32":
                subprocess.run(["explorer", str(folder_path)], check=True)
            elif sys.platform == "darwin":
                subprocess.run(["open", str(folder_path)], check=True)
            else:
                subprocess.run(["xdg-open", str(folder_path)], check=True)
        except Exception as e:
            print(f"Could not open folder: {e}")