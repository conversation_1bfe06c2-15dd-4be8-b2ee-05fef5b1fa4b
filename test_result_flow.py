#!/usr/bin/env python3
"""
Test video processing flow to verify fixes
"""
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_result_processing():
    """Test the result processing flow"""
    
    # Create a mock processing result like the video processor would return
    mock_video_result = {
        'success': True,
        'job_dir': Path('./test_output'),
        'extracted_frames': [
            type('FrameData', (), {
                'frame_number': 1,
                'timestamp': 1.5,
                'similarity_score': 0.95
            })(),
            type('FrameData', (), {
                'frame_number': 2, 
                'timestamp': 3.2,
                'similarity_score': 0.87
            })()
        ],
        'saved_paths': [
            Path('./test_output/frame_001.png'),
            Path('./test_output/frame_002.png')
        ],
        'detected_scenes': [],
        'scene_video_paths': [],
        'scene_audio_paths': [],
        'transcriptions': [],
        'metadata': {'video_info': {'fps': 30, 'total_frames': 120}},
        'processing_time': 45.6
    }
    
    # Test how the universal progress view wraps this
    completion_result = {
        'type': 'video_processing',
        'video_path': Path('./test_video.mp4'),
        'result': mock_video_result
    }
    
    print("📊 Testing processing result flow:")
    print(f"1. Video processor result keys: {list(mock_video_result.keys())}")
    print(f"2. Universal progress wrapper keys: {list(completion_result.keys())}")
    print(f"3. Nested result keys: {list(completion_result['result'].keys())}")
    
    # Test the main window processing
    from src.ui.main_window import MainWindow
    main_window = MainWindow()
    
    print("\n🔄 Testing main window result processing...")
    
    # Simulate the completion callback
    main_window.on_processing_complete(completion_result)
    
    print(f"4. Main window processing_result keys: {list(main_window.processing_result.keys())}")
    print(f"5. Success: {main_window.processing_result.get('success', False)}")
    print(f"6. Extracted frames count: {len(main_window.processing_result.get('extracted_frames', []))}")
    
    print("\n✅ Test completed successfully!")

if __name__ == "__main__":
    try:
        test_result_processing()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()