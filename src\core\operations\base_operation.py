"""
Base operation interface for all processing operations
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
from enum import Enum


class OperationStage(Enum):
    """Standard operation stages"""
    INITIALIZE = "initialize"
    PROCESS = "process"
    FINALIZE = "finalize"
    COMPLETE = "complete"
    ERROR = "error"


class BaseOperation(ABC):
    """Base class for all operations - implement this to create new operation types"""
    
    def __init__(self):
        self.operation_config: Dict[str, Any] = {}
        self.operation_result: Dict[str, Any] = {}
    
    @property
    @abstractmethod
    def operation_id(self) -> str:
        """Unique identifier for this operation type (e.g., 'video_processing')"""
        pass
    
    @property
    @abstractmethod
    def display_name(self) -> str:
        """Human-readable name for UI (e.g., 'Video Processing')"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Description for tooltips/help"""
        pass
    
    @property
    @abstractmethod
    def icon(self) -> str:
        """Flet icon name for UI (e.g., 'video_file')"""
        pass
    
    @abstractmethod
    def get_stages(self) -> List[str]:
        """Return list of stage names for this operation (e.g., ['Analyze', 'Extract', 'Complete'])"""
        pass
    
    @abstractmethod
    def get_stats_schema(self) -> Dict[str, str]:
        """Return stat labels and their descriptions for progress display"""
        pass
    
    @abstractmethod
    def create_progress_view(self, operation_config: Dict[str, Any]) -> 'BaseProgressView':
        """Create operation-specific progress view"""
        pass
    
    @abstractmethod
    def create_results_view(self, operation_result: Dict[str, Any]) -> 'BaseResultsView':
        """Create operation-specific results view"""
        pass
    
    @abstractmethod
    def execute(self, config: Dict[str, Any], progress_callback: Callable) -> Dict[str, Any]:
        """Execute the operation with given configuration"""
        pass
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate operation configuration (override if needed)"""
        return True
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for this operation (override if needed)"""
        return {}


# Import guard to prevent circular imports
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from ...ui.views.base_progress_view import BaseProgressView
    from ...ui.views.base_results_view import BaseResultsView