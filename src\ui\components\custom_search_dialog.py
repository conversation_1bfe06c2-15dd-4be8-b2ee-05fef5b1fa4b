import flet as ft
from typing import Dict, Callable

class CustomSearchDialog(ft.AlertDialog):
    def __init__(self, sentence_data: Dict, on_search: Callable):
        super().__init__()
        self.modal = True
        self.title = ft.Text("Custom Video Search")
        self.sentence_data = sentence_data
        self.on_search = on_search

        self.search_field = ft.TextField(label="Search query", value=", ".join(sentence_data.get('keywords', [])))
        self.content = ft.Container(
            content=self.search_field,
            width=400,
        )
        self.actions = [
            ft.TextButton("Search", on_click=self._handle_search),
            ft.TextButton("Cancel", on_click=self._handle_close),
        ]

    def _handle_search(self, e):
        query = self.search_field.value
        if query:
            self.on_search(self.sentence_data, query)
        self.open = False
        self.page.update()

    def _handle_close(self, e):
        self.open = False
        self.page.update()
