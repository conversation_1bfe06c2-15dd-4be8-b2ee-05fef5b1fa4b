.audio-analyzer-dropzone {
    width: 100%;
    min-height: 100px;
    border: 2px dashed #666;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    transition: all 0.2s ease;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.audio-analyzer-dropzone.dragover {
    border-color: #4CAF50;
    background-color: rgba(76, 175, 80, 0.1);
}

.audio-analyzer-dropzone .drop-message {
    color: #666;
    font-size: 14px;
    text-align: center;
    padding: 10px;
}

.audio-analyzer-dropzone.dragover .drop-message {
    color: #4CAF50;
}

.audio-analyzer-controls {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.audio-analyzer-visualization {
    width: 100%;
    height: 80px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 10px;
    position: relative;
    overflow: hidden;
}

.audio-analyzer-canvas {
    position: relative;
}

.audio-analyzer-waveform {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    stroke: #4CAF50;
    fill: none;
    stroke-width: 1.5px;
}

.audio-analyzer-time-marker {
    position: absolute;
    top: 0;
    height: 100%;
    width: 2px;
    background-color: rgba(255, 0, 0, 0.7);
    z-index: 10;
}

.audio-analyzer-time-display {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-top: 5px;
}

.audio-analyzer-floating-analyze {
    z-index: 50;
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    color: white;
    background: #28a745;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4);
    transition: all 0.2s ease;
    user-select: none;
    pointer-events: auto;
}

.audio-analyzer-floating-analyze:hover {
    background: #218838;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.5);
    transform: translate(-50%, -120%) translateY(-2px);
}

.audio-analyzer-floating-analyze:active {
    transform: translate(-50%, -120%) translateY(0px);
    box-shadow: 0 2px 6px rgba(40, 167, 69, 0.4);
}