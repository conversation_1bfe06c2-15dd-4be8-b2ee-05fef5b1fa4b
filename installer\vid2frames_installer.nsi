# Vid2Frames Professional Installer
# Advanced NSIS script for commercial distribution

!define PRODUCT_NAME "Vid2Frames Pro"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "Vid2Frames Solutions"
!define PRODUCT_WEB_SITE "https://www.vid2frames.com"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\Vid2Frames.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"
!define PRODUCT_STARTMENU_REGVAL "NSIS:StartMenuDir"

# MUI Settings
!include "MUI2.nsh"
!include "LogicLib.nsh"
!include "WinVer.nsh"
!include "x64.nsh"

# MUI Settings
!define MUI_ABORTWARNING
!define MUI_ICON "assets\icon.ico"
!define MUI_UNICON "assets\uninstall.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "assets\header.bmp"
!define MUI_WELCOMEFINISHPAGE_BITMAP "assets\wizard.bmp"
!define MUI_UNWELCOMEFINISHPAGE_BITMAP "assets\wizard.bmp"

# Welcome page
!insertmacro MUI_PAGE_WELCOME

# License page
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"

# Components page
!insertmacro MUI_PAGE_COMPONENTS

# Directory page
!insertmacro MUI_PAGE_DIRECTORY

# Start menu page
var ICONS_GROUP
!define MUI_STARTMENUPAGE_NODISABLE
!define MUI_STARTMENUPAGE_DEFAULTFOLDER "${PRODUCT_NAME}"
!define MUI_STARTMENUPAGE_REGISTRY_ROOT "${PRODUCT_UNINST_ROOT_KEY}"
!define MUI_STARTMENUPAGE_REGISTRY_KEY "${PRODUCT_UNINST_KEY}"
!define MUI_STARTMENUPAGE_REGISTRY_VALUENAME "${PRODUCT_STARTMENU_REGVAL}"
!insertmacro MUI_PAGE_STARTMENU Application $ICONS_GROUP

# Instfiles page
!insertmacro MUI_PAGE_INSTFILES

# Finish page
!define MUI_FINISHPAGE_RUN "$INSTDIR\Vid2Frames.exe"
!define MUI_FINISHPAGE_SHOWREADME "$INSTDIR\README.txt"
!insertmacro MUI_PAGE_FINISH

# Uninstaller pages
!insertmacro MUI_UNPAGE_INSTFILES

# Language files
!insertmacro MUI_LANGUAGE "English"

# Reserve files
!insertmacro MUI_RESERVEFILE_INSTALLOPTIONS

# MUI end ------

Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "Vid2Frames-Pro-Setup-${PRODUCT_VERSION}.exe"
InstallDir "$PROGRAMFILES64\Vid2Frames"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

# Version Information
VIProductVersion "*******"
VIAddVersionKey "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey "Comments" "Professional video frame extraction tool"
VIAddVersionKey "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey "LegalTrademarks" "Vid2Frames® is a trademark of ${PRODUCT_PUBLISHER}"
VIAddVersionKey "LegalCopyright" "© 2025 ${PRODUCT_PUBLISHER}"
VIAddVersionKey "FileDescription" "${PRODUCT_NAME} Installer"
VIAddVersionKey "FileVersion" "${PRODUCT_VERSION}"
VIAddVersionKey "ProductVersion" "${PRODUCT_VERSION}"

Function .onInit
  # Check if running on Windows 10 or later
  ${If} ${AtLeastWin10}
    # OK, continue
  ${Else}
    MessageBox MB_OK|MB_ICONSTOP "Vid2Frames Pro requires Windows 10 or later.$\r$\nInstallation will now exit."
    Abort
  ${EndIf}

  # Check if 64-bit Windows
  ${If} ${RunningX64}
    # OK, continue
  ${Else}
    MessageBox MB_OK|MB_ICONSTOP "Vid2Frames Pro requires 64-bit Windows.$\r$\nInstallation will now exit."
    Abort
  ${EndIf}

  # Check for existing installation
  ReadRegStr $R0 ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString"
  StrCmp $R0 "" done

  MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION \
  "${PRODUCT_NAME} is already installed. $\n$\nClick 'OK' to remove the \
  previous version or 'Cancel' to cancel this upgrade." \
  IDOK uninst
  Abort

uninst:
  ClearErrors
  ExecWait '$R0 _?=$INSTDIR'

  IfErrors no_remove_uninstaller done
    no_remove_uninstaller:

done:

FunctionEnd

Section "Core Application" SEC01
  SectionIn RO
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer
  
  # Main executable and dependencies
  File "dist\Vid2Frames.exe"
  File "LICENSE.txt"
  File "README.txt"
  File "CHANGELOG.txt"
  
  # Create uninstaller
  WriteUninstaller "$INSTDIR\uninst.exe"
  
  # Registry entries
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\Vid2Frames.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\Vid2Frames.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
  WriteRegDWORD ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "NoModify" 1
  WriteRegDWORD ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "NoRepair" 1
  
  # Estimate install size (in KB)
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "EstimatedSize" "$0"
  
SectionEnd

Section "Desktop Shortcut" SEC02
  CreateShortCut "$DESKTOP\Vid2Frames Pro.lnk" "$INSTDIR\Vid2Frames.exe"
SectionEnd

Section "Quick Launch Shortcut" SEC03
  CreateShortCut "$QUICKLAUNCH\Vid2Frames Pro.lnk" "$INSTDIR\Vid2Frames.exe"
SectionEnd

Section "File Associations" SEC04
  # Register file associations for video files
  WriteRegStr HKLM "SOFTWARE\Classes\.mp4\OpenWithList\Vid2Frames.exe" "" ""
  WriteRegStr HKLM "SOFTWARE\Classes\.avi\OpenWithList\Vid2Frames.exe" "" ""
  WriteRegStr HKLM "SOFTWARE\Classes\.mov\OpenWithList\Vid2Frames.exe" "" ""
  WriteRegStr HKLM "SOFTWARE\Classes\.mkv\OpenWithList\Vid2Frames.exe" "" ""
  WriteRegStr HKLM "SOFTWARE\Classes\.webm\OpenWithList\Vid2Frames.exe" "" ""
  
  # Register application
  WriteRegStr HKLM "SOFTWARE\Classes\Applications\Vid2Frames.exe\FriendlyAppName" "" "Vid2Frames Pro"
  WriteRegStr HKLM "SOFTWARE\Classes\Applications\Vid2Frames.exe\shell\open\command" "" '"$INSTDIR\Vid2Frames.exe" "%1"'
SectionEnd

Section "Microsoft Visual C++ Redistributable" SEC05
  DetailPrint "Installing Microsoft Visual C++ Redistributable..."
  SetOutPath "$TEMP"
  File "redist\VC_redist.x64.exe"
  ExecWait '"$TEMP\VC_redist.x64.exe" /quiet /norestart'
  Delete "$TEMP\VC_redist.x64.exe"
SectionEnd

Section -AdditionalIcons
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application
  CreateDirectory "$SMPROGRAMS\$ICONS_GROUP"
  CreateShortCut "$SMPROGRAMS\$ICONS_GROUP\Vid2Frames Pro.lnk" "$INSTDIR\Vid2Frames.exe"
  CreateShortCut "$SMPROGRAMS\$ICONS_GROUP\Uninstall.lnk" "$INSTDIR\uninst.exe"
  CreateShortCut "$SMPROGRAMS\$ICONS_GROUP\Documentation.lnk" "$INSTDIR\README.txt"
  CreateShortCut "$SMPROGRAMS\$ICONS_GROUP\Visit Website.lnk" "${PRODUCT_WEB_SITE}"
  !insertmacro MUI_STARTMENU_WRITE_END
SectionEnd

Section -Post
  WriteUninstaller "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\Vid2Frames.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\Vid2Frames.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd

# Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "Core application files (required)"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "Creates a desktop shortcut for quick access"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC03} "Creates a quick launch shortcut"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC04} "Associates video files with Vid2Frames Pro"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC05} "Installs Microsoft Visual C++ Redistributable (required)"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

Function un.onUninstSuccess
  HideWindow
  MessageBox MB_ICONINFORMATION|MB_OK "$(^Name) was successfully removed from your computer."
FunctionEnd

Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "Are you sure you want to completely remove $(^Name) and all of its components?" IDYES +2
  Abort
FunctionEnd

Section Uninstall
  !insertmacro MUI_STARTMENU_GETFOLDER "Application" $ICONS_GROUP
  Delete "$INSTDIR\uninst.exe"
  Delete "$INSTDIR\Vid2Frames.exe"
  Delete "$INSTDIR\LICENSE.txt"
  Delete "$INSTDIR\README.txt"
  Delete "$INSTDIR\CHANGELOG.txt"

  Delete "$SMPROGRAMS\$ICONS_GROUP\Uninstall.lnk"
  Delete "$SMPROGRAMS\$ICONS_GROUP\Vid2Frames Pro.lnk"
  Delete "$SMPROGRAMS\$ICONS_GROUP\Documentation.lnk"
  Delete "$SMPROGRAMS\$ICONS_GROUP\Visit Website.lnk"

  RMDir "$SMPROGRAMS\$ICONS_GROUP"
  RMDir "$INSTDIR"

  Delete "$DESKTOP\Vid2Frames Pro.lnk"
  Delete "$QUICKLAUNCH\Vid2Frames Pro.lnk"

  # Remove file associations
  DeleteRegKey HKLM "SOFTWARE\Classes\.mp4\OpenWithList\Vid2Frames.exe"
  DeleteRegKey HKLM "SOFTWARE\Classes\.avi\OpenWithList\Vid2Frames.exe"
  DeleteRegKey HKLM "SOFTWARE\Classes\.mov\OpenWithList\Vid2Frames.exe"
  DeleteRegKey HKLM "SOFTWARE\Classes\.mkv\OpenWithList\Vid2Frames.exe"
  DeleteRegKey HKLM "SOFTWARE\Classes\.webm\OpenWithList\Vid2Frames.exe"
  DeleteRegKey HKLM "SOFTWARE\Classes\Applications\Vid2Frames.exe"

  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  SetAutoClose true
SectionEnd