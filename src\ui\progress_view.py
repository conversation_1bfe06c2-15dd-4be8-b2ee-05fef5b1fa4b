import flet as ft
from typing import Optional, Dict, Any
from pathlib import Path
import threading

from ..core.video_processor import VideoProcessor


class ProgressView:
    """Progress view showing video processing status"""
    
    def __init__(self, on_processing_complete: callable):
        self.on_processing_complete = on_processing_complete
        self.processor = None
        self.current_video_path = None
        self.page = None  # Will be set by main window
        self.split_scenes_enabled = False
        self.transcription_enabled = False  # Add transcription flag
        
    def build(self):
        # Overall progress bar
        self.overall_progress = ft.ProgressBar(
            width=400,
            color=ft.Colors.PRIMARY,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            value=0.0
        )
        
        # Progress text
        self.progress_text = ft.Text(
            "Starting processing...",
            size=14,
            color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        # Stage indicator - now with 6 stages including transcription
        self.stage_indicator = ft.Row([
            ft.Container(
                content=ft.Text("1", color=ft.Colors.WHITE, size=12, text_align=ft.TextAlign.CENTER),
                width=24, height=24,
                bgcolor=ft.Colors.BLUE_400,
                border_radius=ft.border_radius.all(12),
                alignment=ft.alignment.center
            ),
            ft.Container(width=30, height=2, bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            ft.Container(
                content=ft.Text("2", color=ft.Colors.WHITE, size=12, text_align=ft.TextAlign.CENTER),
                width=24, height=24,
                bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300,
                border_radius=ft.border_radius.all(12),
                alignment=ft.alignment.center
            ),
            ft.Container(width=30, height=2, bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            ft.Container(
                content=ft.Text("3", color=ft.Colors.WHITE, size=12, text_align=ft.TextAlign.CENTER),
                width=24, height=24,
                bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300,
                border_radius=ft.border_radius.all(12),
                alignment=ft.alignment.center
            ),
            ft.Container(width=30, height=2, bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            ft.Container(
                content=ft.Text("4", color=ft.Colors.WHITE, size=12, text_align=ft.TextAlign.CENTER),
                width=24, height=24,
                bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300,
                border_radius=ft.border_radius.all(12),
                alignment=ft.alignment.center
            ),
            ft.Container(width=30, height=2, bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            ft.Container(
                content=ft.Text("5", color=ft.Colors.WHITE, size=12, text_align=ft.TextAlign.CENTER),
                width=24, height=24,
                bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300,
                border_radius=ft.border_radius.all(12),
                alignment=ft.alignment.center
            ),
            ft.Container(width=30, height=2, bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            ft.Container(
                content=ft.Text("6", color=ft.Colors.WHITE, size=12, text_align=ft.TextAlign.CENTER),
                width=24, height=24,
                bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300,
                border_radius=ft.border_radius.all(12),
                alignment=ft.alignment.center
            )
        ], alignment=ft.MainAxisAlignment.CENTER)
        
        # Stage labels - updated for 6 stages including transcription
        self.stage_labels = ft.Row([
            ft.Text("Analyze", size=10, color=ft.Colors.PRIMARY, weight=ft.FontWeight.W_500),
            ft.Text("Extract", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
            ft.Text("Scenes", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
            ft.Text("Split", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
            ft.Text("Transcribe", size=10, color=ft.Colors.ON_SURFACE_VARIANT),
            ft.Text("Save", size=10, color=ft.Colors.ON_SURFACE_VARIANT)
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        
        # Statistics container
        self.stats_container = ft.Container(
            content=ft.Column([
                ft.Text("Processing Statistics", weight=ft.FontWeight.W_600, size=16),
                ft.Row([
                    self._create_stat_item("Frames Analyzed", "0"),
                    self._create_stat_item("Frames Extracted", "0"),
                ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
                ft.Row([
                    self._create_stat_item("Videos Created", "0"),
                    self._create_stat_item("Transcriptions", "0"),
                ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
                ft.Row([
                    self._create_stat_item("Quality Score", "0.0"),
                    self._create_stat_item("Duration", "0:00"),
                ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
            ], spacing=16),
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=ft.border_radius.all(12),
            padding=20,
            margin=20
        )
        
        # Current frame preview (placeholder)
        self.frame_preview = ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.IMAGE_OUTLINED, size=48, color=ft.Colors.ON_SURFACE_VARIANT),
                ft.Text("Frame Preview", color=ft.Colors.ON_SURFACE_VARIANT)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            width=200,
            height=150,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=ft.border_radius.all(8),
            alignment=ft.alignment.center
        )
        
        # Control buttons
        self.cancel_button = ft.ElevatedButton(
            "Cancel Processing",
            icon=ft.Icons.CANCEL,
            on_click=self.cancel_processing,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.RED_400,
                color=ft.Colors.WHITE,
                padding=20
            )
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Text(
                        "Processing Video",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PRIMARY
                    ),
                    padding=20,
                    alignment=ft.alignment.center
                ),
                
                # Progress section
                ft.Container(
                    content=ft.Column([
                        self.overall_progress,
                        ft.Container(height=10),
                        self.progress_text,
                        ft.Container(height=20),
                        self.stage_indicator,
                        ft.Container(height=10),
                        ft.Container(
                            content=self.stage_labels,
                            width=300
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    padding=20
                ),
                
                # Stats and preview row
                ft.Row([
                    self.stats_container,
                    ft.Column([
                        ft.Text("Current Frame", weight=ft.FontWeight.W_500),
                        self.frame_preview
                    ], spacing=10)
                ], alignment=ft.MainAxisAlignment.CENTER, spacing=40),
                
                # Controls
                ft.Container(
                    content=self.cancel_button,
                    padding=20,
                    alignment=ft.alignment.center
                )
                
            ], scroll=ft.ScrollMode.AUTO, expand=True),
            expand=True
        )
    
    def _create_stat_item(self, label: str, value: str):
        """Create a statistic display item"""
        return ft.Column([
            ft.Text(value, size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.PRIMARY),
            ft.Text(label, size=12, color=ft.Colors.ON_SURFACE_VARIANT)
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=4)
    
    def start_processing(self, video_path: Path):
        """Start video processing"""
        self.current_video_path = video_path
        
        # Reset UI
        self.overall_progress.value = 0.0
        self.progress_text.value = "Starting processing..."
        self._reset_stages()
        self._reset_stats()
        
        # Start processing in background thread
        self.processor = VideoProcessor(progress_callback=self._thread_safe_progress_update)
        
        threading.Thread(
            target=self._run_processing,
            args=(video_path,),
            daemon=True
        ).start()
    
    def _thread_safe_progress_update(self, progress_info: dict):
        """Handle progress updates safely from background thread"""
        if self.page:
            self.page.run_thread(lambda: self.on_progress_update(progress_info))
        else:
            self.on_progress_update(progress_info)
    
    def _run_processing(self, video_path: Path):
        """Run processing in background thread"""
        try:
            # Check if scene splitting is enabled in settings
            split_scenes = False
            if hasattr(self, 'split_scenes_enabled'):
                split_scenes = self.split_scenes_enabled
                print(f"Scene splitting enabled: {split_scenes}")
            else:
                # Default to True for testing - should be controlled by settings
                split_scenes = True
                print("Scene splitting enabled by default (settings not found)")
            
            result = self.processor.process_video(
                video_path,
                split_scenes=split_scenes,
                enable_transcription=self.transcription_enabled
            )
            
            # Update UI on main thread
            self.page.run_thread(lambda: self._on_processing_finished(result))
            
        except Exception as e:
            # Handle errors on main thread
            self.page.run_thread(lambda: self._on_processing_error(str(e)))
    
    def _on_processing_finished(self, result: dict):
        """Handle processing completion"""
        if result['success']:
            self.progress_text.value = f"Processing complete! Extracted {len(result['extracted_frames'])} frames"
            self.overall_progress.value = 1.0
            self._set_all_stages_complete()
            
            # Update final stats
            metadata = result['metadata']
            self._update_stat(0, str(metadata['results']['total_extracted']))
            self._update_stat(1, str(metadata['results']['frames_saved']))
            self._update_stat(2, str(metadata['results'].get('scene_videos_created', 0)))
            self._update_stat(3, str(metadata['results'].get('transcriptions_created', 0)))
            self._update_stat(4, f"{metadata['results']['average_quality']:.2f}")
            
            # Calculate duration from video info
            video_info = metadata.get('video_info', {})
            duration = video_info.get('duration', 0)
            duration_mins = int(duration // 60)
            duration_secs = int(duration % 60)
            self._update_stat(5, f"{duration_mins}:{duration_secs:02d}")
            
            # Call completion callback after a brief delay to show completion
            threading.Timer(2.0, lambda: self.on_processing_complete(result)).start()
            
        else:
            self._on_processing_error(result.get('error', 'Unknown error occurred'))
    
    def _on_processing_error(self, error_message: str):
        """Handle processing error"""
        self.progress_text.value = f"Error: {error_message}"
        self.overall_progress.color = ft.Colors.RED_400
    
    def on_progress_update(self, progress_info: dict):
        """Handle progress updates from processor"""
        stage = progress_info.get('stage', '')
        progress = progress_info.get('progress', 0.0)
        
        # Update frame preview if frame data is available
        current_frame_data = progress_info.get('current_frame_data')
        if current_frame_data is not None:
            self._update_frame_preview(current_frame_data)
        
        # Update overall progress
        self.overall_progress.value = progress
        
        # Update stage indicators
        if stage == 'analyzing':
            self._set_stage_active(0)
            self.progress_text.value = "Analyzing video properties..."
            if 'fps' in progress_info and 'duration' in progress_info:
                duration_mins = int(progress_info['duration'] // 60)
                duration_secs = int(progress_info['duration'] % 60)
                self._update_stat(5, f"{duration_mins}:{duration_secs:02d}")
        elif stage == 'extracting':
            self._set_stage_complete(0)
            self._set_stage_active(1)
            extracted = progress_info.get('extracted_count', 0)
            current_frame = progress_info.get('current_frame', 0)
            self.progress_text.value = f"Extracting frames... {extracted} unique frames found"
            self._update_stat(0, str(current_frame))
            self._update_stat(1, str(extracted))
            if 'current_quality' in progress_info:
                self._update_stat(4, f"{progress_info['current_quality']:.2f}")
            
            # Update frame preview if frame data is available
            if 'current_frame_data' in progress_info:
                self._update_frame_preview(progress_info['current_frame_data'])
        elif stage == 'detecting_scenes':
            self._set_stage_complete(1)
            self._set_stage_active(2)
            scenes_found = progress_info.get('scenes_detected', progress_info.get('scenes_found', 0))
            self.progress_text.value = f"Detecting scene changes... {scenes_found} scenes found"
            self._update_stat(2, str(scenes_found))
        elif stage == 'scenes_detected':
            self._set_stage_complete(2)
            total_scenes = progress_info.get('total_scenes', 0)
            self.progress_text.value = f"Scene detection complete! {total_scenes} scenes detected"
            self._update_stat(2, str(total_scenes))
        elif stage == 'splitting_scenes':
            self._set_stage_active(3)
            scenes_created = progress_info.get('scenes_created', 0)
            current_scene = progress_info.get('current_scene', 0)
            total_scenes = progress_info.get('total_scenes', 0)
            self.progress_text.value = f"Splitting video into scenes... {current_scene}/{total_scenes}"
            self._update_stat(2, str(scenes_created))
        elif stage == 'scenes_split':
            self._set_stage_complete(3)
            total_scenes_created = progress_info.get('total_scenes_created', 0)
            self.progress_text.value = f"Scene splitting complete! {total_scenes_created} video files created"
            self._update_stat(2, str(total_scenes_created))
        elif stage == 'transcribing':
            self._set_stage_active(4)
            transcribed = progress_info.get('transcriptions_completed', 0)
            total_to_transcribe = progress_info.get('total_to_transcribe', 0)
            self.progress_text.value = f"Transcribing audio... {transcribed}/{total_to_transcribe} scenes"
        elif stage == 'transcription_complete':
            self._set_stage_complete(4)
            total_transcribed = progress_info.get('total_transcribed', 0)
            self.progress_text.value = f"Transcription complete! {total_transcribed} scenes transcribed"
        elif stage == 'saving':
            self._set_stage_active(5)
            saved = progress_info.get('saved_count', 0)
            self.progress_text.value = f"Saving frames to disk... {saved} saved"
            self._update_stat(1, str(saved))
        elif stage == 'complete':
            self._set_all_stages_complete()
            extracted = progress_info.get('extracted_count', 0)
            self.progress_text.value = f"Processing complete! {extracted} frames extracted"
        
        # Force UI update
        if self.page:
            self.page.update()
    
    def _set_stage_active(self, stage_index: int):
        """Set a stage as active"""
        if stage_index < len(self.stage_indicator.controls):
            circle = self.stage_indicator.controls[stage_index * 2]  # Skip connectors
            circle.bgcolor = ft.Colors.BLUE_400
            
            # Update label
            labels = [self.stage_labels.controls[0], self.stage_labels.controls[1], self.stage_labels.controls[2]]
            if stage_index < len(labels):
                labels[stage_index].color = ft.Colors.BLUE_600
                labels[stage_index].weight = ft.FontWeight.W_500
    
    def _set_stage_complete(self, stage_index: int):
        """Set a stage as complete"""
        if stage_index < len(self.stage_indicator.controls):
            circle = self.stage_indicator.controls[stage_index * 2]  # Skip connectors
            circle.bgcolor = ft.Colors.GREEN_400
            circle.content.value = "✓"
            
            # Update connector
            if (stage_index * 2 + 1) < len(self.stage_indicator.controls):
                connector = self.stage_indicator.controls[stage_index * 2 + 1]
                connector.bgcolor = ft.Colors.GREEN_400
    
    def _set_all_stages_complete(self):
        """Mark all stages as complete"""
        for i in range(6):  # Updated for 6 stages
            self._set_stage_complete(i)
    
    def _reset_stages(self):
        """Reset all stage indicators"""
        for i, control in enumerate(self.stage_indicator.controls):
            if i % 2 == 0:  # Circle
                control.bgcolor = ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300
                control.content.value = str((i // 2) + 1)
            else:  # Connector
                control.bgcolor = ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300
        
        # Reset labels
        for i, label in enumerate(self.stage_labels.controls):
            label.color = ft.Colors.ON_SURFACE_VARIANT
            label.weight = ft.FontWeight.NORMAL
    
    def _update_stat(self, index: int, value: str):
        """Update a statistic value"""
        # Find the stat containers in the stats section
        stats_rows = self.stats_container.content.controls[1:5]  # Skip title, now have 4 rows
        stat_items = []
        for row in stats_rows:
            stat_items.extend(row.controls)
        
        if index < len(stat_items):
            stat_items[index].controls[0].value = value
    
    def _reset_stats(self):
        """Reset all statistics"""
        self._update_stat(0, "0")    # Frames Analyzed
        self._update_stat(1, "0")    # Frames Extracted
        self._update_stat(2, "0")    # Videos Created
        self._update_stat(3, "0")    # Transcriptions
        self._update_stat(4, "0.0")  # Quality Score
        self._update_stat(5, "0:00") # Duration
    
    def cancel_processing(self, e):
        """Cancel current processing"""
        if self.processor:
            self.processor.stop_processing()
            self.progress_text.value = "Processing cancelled"
            self.overall_progress.color = ft.Colors.ORANGE_400
    
    def _update_frame_preview(self, frame_data):
        """Update the frame preview with current frame"""
        try:
            import cv2
            import tempfile
            import os
            import time
            from PIL import Image
            
            # Convert BGR to RGB
            if len(frame_data.shape) == 3:
                frame_rgb = cv2.cvtColor(frame_data, cv2.COLOR_BGR2RGB)
            else:
                frame_rgb = frame_data
            
            # Create unique temporary file for each frame to avoid caching
            temp_dir = tempfile.gettempdir()
            timestamp = int(time.time() * 1000)  # Millisecond timestamp
            temp_path = os.path.join(temp_dir, f"vid2frames_preview_{timestamp}.png")
            
            # Clean up old preview file if it exists
            if hasattr(self, '_current_preview_path') and os.path.exists(self._current_preview_path):
                try:
                    os.remove(self._current_preview_path)
                except:
                    pass  # Ignore cleanup errors
            
            # Save frame as temporary image using PIL
            pil_image = Image.fromarray(frame_rgb)
            # Resize for preview to keep it small and fast
            pil_image.thumbnail((200, 150), Image.Resampling.LANCZOS)
            pil_image.save(temp_path, "PNG")
            
            # Store current preview path for cleanup
            self._current_preview_path = temp_path
            
            # Update the frame preview container with the new image
            self.frame_preview.content = ft.Image(
                src=temp_path,
                width=200,
                height=150,
                fit=ft.ImageFit.CONTAIN,
                border_radius=ft.border_radius.all(8)
            )
            
            # Force immediate UI update
            if self.page:
                self.page.update()
            
        except Exception as e:
            print(f"Failed to update frame preview: {e}")
            # Keep the placeholder on error
            pass
            self.update()
