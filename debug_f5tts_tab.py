#!/usr/bin/env python3
"""
Debug F5-TTS Tab Button State
"""

import sys
from pathlib import Path

# Add src to path  
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock main window for testing
class MockMainWindow:
    def __init__(self):
        self.page = None

# Test the F5-TTS tab initialization
try:
    sys.path.append(str(Path(__file__).parent))
    from src.ui.f5_tts_view import F5TTSView
    
    print("🔄 Testing F5-TTS Tab Initialization...")
    
    # Create mock main window
    main_window = MockMainWindow()
    
    # Create F5-TTS view
    view = F5TTSView()
    print("✅ F5TTSView created successfully")
    
    # Check initial state
    print(f"📝 Input text: '{view.text_input.value if hasattr(view, 'text_input') else 'Not initialized'}'")
    print(f"🎤 Reference audio: {getattr(view, 'selected_reference_audio', None)}")
    print(f"🎙️ ComfyUI voice: {getattr(view, 'comfy_voice_dropdown', None).value if hasattr(view, 'comfy_voice_dropdown') and view.comfy_voice_dropdown else None}")
    
    # Test validation (simplified)
    is_valid = hasattr(view, 'text_input') and hasattr(view, 'f5_tts')
    print(f"✅ Initial validation result: {is_valid}")
    
    # Test with text input
    test_text = "Hello world\nThis is line two\nAnd this is line three"
    if hasattr(view, 'text_input'):
        view.text_input.value = test_text
        print(f"📝 Set test text: {len(test_text.split())} words")
    
    # Test with ComfyUI voice
    if hasattr(view, 'available_voices') and view.available_voices:
        first_voice = view.available_voices[0]
        print(f"🎤 Available voice: {first_voice['name']}")
        print(f"   Voice path: {first_voice['path']}")
    else:
        print("⚠️ No ComfyUI voices found")
    
    # Test line splitting
    lines = [line.strip() for line in test_text.split('\n') if line.strip()]
    print(f"📄 Text will be split into {len(lines)} audio files:")
    for i, line in enumerate(lines, 1):
        print(f"   File {i}: {line}")
        
except Exception as e:
    print(f"❌ Error testing tab: {e}")
    import traceback
    traceback.print_exc()