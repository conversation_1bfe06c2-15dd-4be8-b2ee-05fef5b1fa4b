# Audio Splitter Feature - Implementation Guide

## Overview

The Audio Splitter is a new feature in Vid2Frames that allows users to split audio files into segments based on text alignment using AI transcription. This feature uses Whisper AI to transcribe the audio and then aligns it with a provided text file to create accurate audio segments.

## Features

### Core Functionality
- **Audio File Support**: MP3, WAV, M4A, FLAC, OGG, AAC, WMA
- **Text File Support**: TXT files with one sentence per line
- **AI-Powered Alignment**: Uses OpenAI Whisper for accurate transcription
- **Similarity Matching**: Configurable threshold for text-audio alignment (0.5 - 1.0)
- **Multiple Output Formats**: WAV (uncompressed), MP3 (compressed), FLAC (lossless)

### User Interface
- **Dual File Upload**: Separate areas for audio and text file selection
- **Visual Feedback**: Clear status indicators and file validation
- **Progress Tracking**: Real-time progress updates during processing
- **Settings Panel**: Configurable similarity thresholds and output formats
- **Error Handling**: User-friendly error messages and recovery options

## Technical Implementation

### Architecture
```
AudioSplitView (UI Layer)
    ↓
AudioSplitter (Core Logic)
    ↓
Whisper Model (AI Transcription)
    ↓
FFmpeg (Audio Segmentation)
```

### Key Components

#### 1. AudioSplitView (`src/ui/audio_split_view.py`)
- **File Management**: Handle audio and text file selection
- **User Interface**: Material Design 3 components with theming
- **Progress Management**: Background processing with UI updates
- **Error Handling**: Graceful error display and recovery

#### 2. AudioSplitter (`src/core/audio_splitter.py`)
- **Whisper Integration**: Load and use Whisper models for transcription
- **Text Alignment**: Fuzzy matching algorithm for sentence alignment
- **Audio Extraction**: FFmpeg-based audio segment extraction
- **GPU Acceleration**: Automatic GPU/CPU detection and optimization

### Processing Pipeline

1. **Audio Transcription**
   - Load Whisper model (base/small/medium/large)
   - Transcribe entire audio with word-level timestamps
   - Extract segment boundaries and confidence scores

2. **Text Alignment** 
   - Parse input text file (one sentence per line)
   - Use fuzzy string matching to align sentences with transcription
   - Calculate similarity scores using SequenceMatcher
   - Filter matches by configurable threshold

3. **Audio Segmentation**
   - Extract audio segments using FFmpeg
   - Apply naming conventions (segment_001_text_preview.wav)
   - Save alignment metadata (JSON format)
   - Generate output in multiple formats

## Usage Instructions

### Basic Workflow
1. **Select Audio File**: Click "Browse Audio Files" or drag-and-drop
2. **Select Text File**: Click "Browse Text Files" to upload TXT file
3. **Configure Settings**: 
   - Choose output format (WAV/MP3/FLAC)
   - Adjust similarity threshold (0.8 recommended)
4. **Start Processing**: Click "Split Audio" button
5. **Monitor Progress**: Watch real-time progress updates
6. **Access Results**: Open output folder when complete

### Text File Format
```
This is the first sentence.
This is the second sentence.
This is the third sentence.
```
- One sentence per line
- UTF-8 encoding
- No special formatting required
- Empty lines are ignored

### Output Structure
```
audio_file_split/
├── segment_001_this_is_the_first_sentence.wav
├── segment_002_this_is_the_second_sentence.wav
├── segment_003_this_is_the_third_sentence.wav
└── alignment_info.json
```

## Configuration Options

### Similarity Threshold
- **Range**: 0.5 - 1.0
- **Default**: 0.8
- **Lower values**: More permissive matching, may include false positives
- **Higher values**: Stricter matching, may miss valid segments

### Output Formats
- **WAV**: Uncompressed, highest quality, larger file size
- **MP3**: Compressed, smaller size, slight quality loss
- **FLAC**: Lossless compression, good balance of size/quality

## Performance Considerations

### GPU Acceleration
- Automatic detection of CUDA-compatible GPUs
- ~5-10x faster transcription with GPU
- Graceful fallback to CPU if GPU unavailable
- Memory optimization for large audio files

### Processing Time Estimates
- **CPU**: ~1-2 minutes per minute of audio
- **GPU**: ~10-20 seconds per minute of audio
- **Alignment**: ~1-5 seconds regardless of audio length
- **Extraction**: ~1-10 seconds depending on segment count

## Error Handling

### Common Issues and Solutions

1. **"No compatible models found"**
   - Install required dependencies: `pip install openai-whisper torch torchaudio`
   - Check internet connection for model downloads

2. **"No segments could be aligned"**
   - Lower similarity threshold (try 0.6 or 0.7)
   - Check text file formatting (one sentence per line)
   - Verify audio quality and language match

3. **"FFmpeg error"**
   - Install FFmpeg system-wide
   - Check audio file format support
   - Verify file permissions and disk space

4. **GPU Issues**
   - Update PyTorch for GPU compatibility
   - Check CUDA drivers and version
   - App will automatically fall back to CPU

## Integration with Main App

### Navigation
- New "Audio Split" tab in navigation rail
- Icon: Content Cut (scissors)
- Position: Between Upload and Progress tabs

### Code Integration
```python
# Added to main_window.py
from .audio_split_view import AudioSplitView

# Navigation destination
ft.NavigationRailDestination(
    icon=ft.Icons.CONTENT_CUT_OUTLINED,
    selected_icon=ft.Icons.CONTENT_CUT,
    label="Audio Split",
)
```

## Dependencies

### Required Packages
```
openai-whisper>=20231117  # AI transcription
torch>=2.0.0             # ML framework  
torchaudio>=2.0.0        # Audio processing
librosa>=0.10.0          # Audio loading
soundfile>=0.12.1        # Audio backend
ffmpeg-python>=0.2.0     # Audio manipulation
```

### System Requirements
- **FFmpeg**: Required for audio processing
- **Python 3.8+**: Compatibility requirement
- **4GB+ RAM**: For Whisper model loading
- **GPU (Optional)**: CUDA-compatible for acceleration

## Testing

### Test Script
Run `python test_audio_splitting.py` to verify:
- Module imports successful
- GPU/CPU detection working
- Basic functionality available

### Manual Testing
1. Create test audio file (speech recording)
2. Create matching text file with sentences
3. Use Audio Split tab to process
4. Verify output quality and alignment
5. Test error scenarios (invalid files, etc.)

## Future Enhancements

### Potential Improvements
1. **Advanced Models**: Support for larger Whisper models
2. **Language Support**: Multi-language transcription options
3. **Batch Processing**: Multiple files at once
4. **Timeline Export**: Export timing data for video editing
5. **Audio Preprocessing**: Noise reduction and normalization
6. **Custom Models**: Support for fine-tuned models

### Performance Optimizations
1. **Streaming Processing**: Handle very large audio files
2. **Caching**: Cache transcriptions for repeated processing
3. **Parallel Processing**: Multi-threaded segment extraction
4. **Memory Management**: Optimize for low-memory systems

## Conclusion

The Audio Splitter feature provides powerful AI-driven audio segmentation capabilities while maintaining the app's focus on user-friendly design and robust error handling. The integration follows existing patterns and provides a solid foundation for future audio processing features.