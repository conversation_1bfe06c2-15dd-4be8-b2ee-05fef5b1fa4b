"""
ComfyUI F5-TTS Integration Setup
This script sets up F5-TTS to work with ComfyUI voice examples
"""

import sys
import subprocess
from pathlib import Path
import shutil
import os

def check_comfy_ui_installation():
    """Check if ComfyUI F5-TTS is available"""
    comfy_path = Path("C:/Users/<USER>/Documents/ComfyUI/custom_nodes/chatterbox_srt_voice")
    
    print("🔍 Checking ComfyUI F5-TTS installation...")
    print(f"Looking for: {comfy_path}")
    
    if comfy_path.exists():
        print("✅ ComfyUI chatterbox_srt_voice found!")
        
        # List contents
        print("📁 Contents:")
        for item in comfy_path.iterdir():
            if item.is_file():
                print(f"  📄 {item.name}")
            elif item.is_dir():
                print(f"  📁 {item.name}/")
        
        # Check for voice examples
        voices_path = comfy_path / "voices_examples"
        if voices_path.exists():
            voice_files = list(voices_path.glob("*.wav")) + list(voices_path.glob("*.mp3"))
            print(f"🎤 Found {len(voice_files)} voice examples:")
            for voice in voice_files[:10]:  # Show first 10
                print(f"  🎵 {voice.name}")
            if len(voice_files) > 10:
                print(f"  ... and {len(voice_files) - 10} more")
        
        return True
    else:
        print("❌ ComfyUI chatterbox_srt_voice not found")
        print("   Please check if ComfyUI is installed and chatterbox_srt_voice node exists")
        return False

def install_f5_tts_dependencies():
    """Install F5-TTS dependencies"""
    print("\n🔄 Installing F5-TTS dependencies...")
    
    dependencies = [
        "torch",
        "torchaudio", 
        "numpy",
        "soundfile",
        "librosa",
        "scipy",
        "transformers",
        "accelerate"
    ]
    
    for dep in dependencies:
        try:
            print(f"🔄 Installing {dep}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True)
            print(f"✅ {dep} installed")
        except subprocess.CalledProcessError:
            print(f"⚠️ Failed to install {dep}")

def try_install_f5_tts():
    """Try to install F5-TTS from various sources"""
    print("\n🎤 Attempting to install F5-TTS...")
    
    sources = [
        {
            "name": "F5-TTS GitHub (SWivid)",
            "command": ["git+https://github.com/SWivid/F5-TTS.git"]
        },
        {
            "name": "F5-TTS PyPI",
            "command": ["f5-tts"]
        },
        {
            "name": "Alternative F5-TTS",
            "command": ["git+https://github.com/model-scope/F5-TTS.git"]
        }
    ]
    
    for source in sources:
        print(f"\n🔄 Trying: {source['name']}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install"
            ] + source["command"], timeout=300)
            
            print(f"✅ Successfully installed F5-TTS from {source['name']}")
            
            # Test the installation
            try:
                import f5_tts
                print("✅ F5-TTS import successful")
                return True
            except ImportError:
                print("⚠️ Installed but import failed")
                continue
                
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
            print(f"❌ Failed to install from {source['name']}")
            continue
    
    print("❌ All F5-TTS installation attempts failed")
    return False

def setup_comfy_integration():
    """Set up integration with ComfyUI"""
    print("\n🔧 Setting up ComfyUI integration...")
    
    comfy_path = Path("C:/Users/<USER>/Documents/ComfyUI/custom_nodes/chatterbox_srt_voice")
    
    if comfy_path.exists():
        # Add ComfyUI path to Python path file for persistent access
        try:
            import site
            site_packages = site.getsitepackages()[0]
            pth_file = Path(site_packages) / "comfy_f5_tts.pth"
            
            with open(pth_file, 'w') as f:
                f.write(str(comfy_path) + '\n')
            
            print(f"✅ Added ComfyUI F5-TTS to Python path: {pth_file}")
            
        except Exception as e:
            print(f"⚠️ Could not set up Python path integration: {e}")
    
    print("✅ ComfyUI integration setup complete")

def test_integration():
    """Test the F5-TTS integration"""
    print("\n🧪 Testing F5-TTS integration...")
    
    try:
        # Test importing our F5-TTS processor
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        from core.f5_tts import F5TTSProcessor
        
        processor = F5TTSProcessor()
        print("✅ F5TTSProcessor created successfully")
        
        # Test voice loading
        voices = processor.get_available_comfy_voices()
        print(f"✅ Found {len(voices)} ComfyUI voices")
        
        # Test model loading
        processor.load_model("E2TTS_Base")
        print("✅ Model loading completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 ComfyUI F5-TTS Integration Setup")
    print("=" * 50)
    
    # Step 1: Check ComfyUI installation
    if not check_comfy_ui_installation():
        print("\n❌ Setup cannot continue without ComfyUI chatterbox_srt_voice")
        return False
    
    # Step 2: Install dependencies
    install_f5_tts_dependencies()
    
    # Step 3: Try to install F5-TTS
    f5_installed = try_install_f5_tts()
    
    # Step 4: Set up integration
    setup_comfy_integration()
    
    # Step 5: Test integration
    test_success = test_integration()
    
    print("\n" + "=" * 50)
    if test_success:
        print("🎉 ComfyUI F5-TTS integration setup complete!")
        print("\n📝 Next steps:")
        print("1. Run: python src/main.py")
        print("2. Go to F5-TTS tab")
        print("3. Select a ComfyUI voice from the dropdown")
        print("4. Enter text and generate speech")
    else:
        print("⚠️ Setup completed with some issues")
        print("The system will use mock generation until F5-TTS is properly installed")
    
    return test_success

if __name__ == "__main__":
    main()