"""
Pexels API integration for B-roll video search
Production-ready video search and management
"""

import requests
import time
from typing import List, Dict, Optional

class PexelsVideoAPI:
    """Production Pexels API integration for B-roll video search"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.pexels.com/videos"
        self.headers = {"Authorization": api_key}
        self.request_delay = 0.5  # Rate limiting delay
        self.max_retries = 3
    
    def search_videos(self, keywords: List[str], duration_target: float = 5.0,
                     per_page: int = 4) -> List[Dict]:
        """
        Search for B-roll videos matching keywords
        
        Args:
            keywords: List of search keywords
            duration_target: Target duration in seconds
            per_page: Number of results per keyword
            
        Returns:
            List of video data dictionaries
        """
        if not keywords:
            return []
        
        all_videos = []
        
        # Try different search combinations
        search_queries = self._generate_search_queries(keywords)
        
        for query in search_queries[:2]:  # Limit to top 2 queries to save API calls
            videos = self._search_single_query(query, per_page, min_duration=2.0)
            all_videos.extend(videos)
            
            if all_videos:  # Stop if we found videos
                break
            
            time.sleep(self.request_delay)  # Rate limiting
        
        # Sort by duration match and quality
        return self._rank_videos(all_videos, duration_target)[:per_page]
    
    def get_alternatives(self, keywords: List[str], exclude_ids: List[int] = None,
                        per_page: int = 8) -> List[Dict]:
        """
        Get alternative videos for manual selection
        
        Args:
            keywords: Search keywords
            exclude_ids: Video IDs to exclude
            per_page: Number of alternatives to return
            
        Returns:
            List of alternative video options
        """
        exclude_ids = exclude_ids or []
        all_videos = []
        
        # Search with individual keywords for more variety
        for keyword in keywords[:3]:  # Max 3 keywords to avoid too many API calls
            videos = self._search_single_query(keyword, per_page, min_duration=1.0)
            
            # Filter out excluded videos
            videos = [v for v in videos if v.get('id') not in exclude_ids]
            all_videos.extend(videos)
            
            time.sleep(self.request_delay)
        
        # Remove duplicates by ID
        seen_ids = set()
        unique_videos = []
        for video in all_videos:
            video_id = video.get('id')
            if video_id and video_id not in seen_ids:
                seen_ids.add(video_id)
                unique_videos.append(video)
        
        # Rank by quality and variety
        return self._rank_alternatives(unique_videos)[:per_page]
    
    def _generate_search_queries(self, keywords: List[str]) -> List[str]:
        """Generate effective search queries from keywords"""
        queries = []
        
        if len(keywords) >= 2:
            # Combine top 2 keywords
            queries.append(f"{keywords[0]} {keywords[1]}")
        
        # Individual keywords
        queries.extend(keywords)
        
        return queries
    
    def _search_single_query(self, query: str, per_page: int, 
                           min_duration: float) -> List[Dict]:
        """Search for a single query with retry logic"""
        for attempt in range(self.max_retries):
            try:
                params = {
                    "query": query,
                    "per_page": per_page,
                    "size": "medium",
                    "orientation": "landscape"
                }
                
                response = requests.get(f"{self.base_url}/search", 
                                      headers=self.headers, params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    videos = []
                    
                    for video in data.get("videos", []):
                        if video.get("duration", 0) >= min_duration:
                            processed = self._process_video_data(video)
                            if processed:
                                videos.append(processed)
                    
                    return videos
                
                elif response.status_code == 429:
                    # Rate limit hit, wait longer
                    wait_time = 2 ** attempt  # Exponential backoff
                    print(f"⚠️ Rate limit hit. Waiting {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                
                else:
                    print(f"❌ API Error {response.status_code} for '{query}'")
                    break
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ Network error for '{query}' (attempt {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
                    continue
                break
        
        return []
    
    def _process_video_data(self, video: Dict) -> Optional[Dict]:
        """Process raw video data from API"""
        video_files = video.get("video_files", [])
        if not video_files:
            return None
        
        # Get best quality video file
        best_video = self._get_best_video_file(video_files)
        if not best_video:
            return None
        
        return {
            "id": video.get("id"),
            "url": video.get("url"),
            "duration": video.get("duration"),
            "width": video.get("width"),
            "height": video.get("height"),
            "thumbnail": video.get("image"),
            "author": video.get("user", {}).get("name", "Unknown"),
            "download_url": best_video["link"],
            "file_type": best_video["file_type"],
            "quality": best_video["quality"]
        }
    
    def _get_best_video_file(self, video_files: List[Dict]) -> Optional[Dict]:
        """Select the best quality video file"""
        if not video_files:
            return None
        
        # Prefer HD, then SD
        for quality in ["hd", "sd"]:
            for video_file in video_files:
                if video_file.get("quality") == quality:
                    return video_file
        
        return video_files[0]  # Fallback to first available
    
    def _rank_videos(self, videos: List[Dict], target_duration: float) -> List[Dict]:
        """Rank videos by relevance and quality"""
        if not videos:
            return []
        
        # Score videos
        scored_videos = []
        for video in videos:
            score = 0
            
            # Duration match score (prefer videos close to target)
            duration_diff = abs(video["duration"] - target_duration)
            duration_score = max(0, 1 - (duration_diff / target_duration))
            score += duration_score * 2
            
            # Quality score
            if video["quality"] == "hd":
                score += 1
            
            # Resolution score (prefer higher resolution)
            resolution_score = (video["width"] * video["height"]) / (1920 * 1080)
            score += min(resolution_score, 1) * 0.5
            
            scored_videos.append((video, score))
        
        # Sort by score and return videos
        scored_videos.sort(key=lambda x: x[1], reverse=True)
        return [video for video, score in scored_videos]
    
    def _rank_alternatives(self, videos: List[Dict]) -> List[Dict]:
        """Rank alternative videos for variety"""
        if not videos:
            return []
        
        scored_videos = []
        for video in videos:
            score = 0
            
            # Quality score
            if video["quality"] == "hd":
                score += 2
            elif video["quality"] == "sd":
                score += 1
            
            # Duration score (prefer reasonable durations)
            duration = video["duration"]
            if 3 <= duration <= 30:
                score += 1
            elif duration > 30:
                score -= 0.5  # Penalize very long videos
            
            # Resolution variety score
            resolution = video["width"] * video["height"]
            if resolution >= 1920 * 1080:
                score += 1
            
            scored_videos.append((video, score))
        
        # Sort by score
        scored_videos.sort(key=lambda x: x[1], reverse=True)
        return [video for video, score in scored_videos]
    
    def test_connection(self) -> bool:
        """Test if API connection is working"""
        try:
            response = requests.get(f"{self.base_url}/search", 
                                  headers=self.headers, 
                                  params={"query": "test", "per_page": 1},
                                  timeout=5)
            return response.status_code in [200, 429]  # 429 means rate limited but API works
        except:
            return False