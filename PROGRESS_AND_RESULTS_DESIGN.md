# Progress and Results Tab Design Specification

## Overview
This document outlines what information and UI elements should be displayed in the Progress tab (during processing) and Results tab (after completion) for each type of operation in Vid2Frames.

---

## 1. VIDEO PROCESSING

### Progress Tab (During Processing)
**Stages:**
1. **Analyze** - Initialize video, validate file, extract metadata
2. **Extract** - Extract frames from video with quality filtering
3. **Scenes** - Detect scene boundaries using computer vision
4. **Transcribe** - Generate audio transcriptions (if enabled)
5. **Complete** - Finalize results and cleanup

**Real-time Stats:**
- Current Frame: `1294`
- Extracted: `6` (frames that passed quality filter)
- Total Frames: `43,176`
- FPS: `30.0`
- Quality Threshold: `0.85`
- Duration: `24:32`
- Scenes Detected: `7`
- Processing Speed: `45 fps`

**Preview Area:**
- Live frame preview (latest extracted frame)
- Frame timestamp overlay
- Quality score indicator

**Progress Indicators:**
- Overall progress bar (0-100%)
- Stage-specific progress for long operations
- ETA (estimated time remaining)

### Results Tab (After Completion)
**Summary Cards:**
- Video Information (duration, resolution, codec, file size)
- Processing Results (frames extracted, scenes detected, processing time)
- Quality Metrics (average quality score, frames filtered out)

**Action Buttons:**
- View Extracted Frames (gallery view)
- View Scene Splits (if enabled)
- View Transcriptions (if generated)
- Export Results
- Process Another Video

**Results Gallery:**
- Thumbnail grid of extracted frames
- Scene boundaries visualization
- Frame quality scores
- Transcription timeline (if available)

---

## 2. AUDIO SPLITTING

### Progress Tab (During Processing)
**Stages:**
1. **Load** - Load audio file and validate format
2. **Transcribe** - Generate text transcription using Whisper
3. **Align** - Align text sentences with audio timestamps
4. **Split** - Extract individual audio segments
5. **Complete** - Save files and generate metadata

**Real-time Stats:**
- Duration: `12:45`
- Text Lines: `34`
- Segments Created: `12/34`
- Model: `Whisper Large`
- Buffer: `500ms`
- Similarity Threshold: `0.85`
- Processing Speed: `2.3x realtime`

**Preview Area:**
- Waveform visualization
- Current segment being processed (highlighted)
- Text alignment preview

**Progress Indicators:**
- Overall progress (segments completed)
- Current operation status
- Audio processing speed

### Results Tab (After Completion)
**Summary Cards:**
- Audio Information (duration, format, bitrate, channels)
- Splitting Results (segments created, total duration, processing time)
- Quality Metrics (alignment accuracy, segments filtered)

**Action Buttons:**
- Play All Segments
- Download Segments (ZIP)
- View Transcription
- Edit Alignment
- Split Another Audio

**Results List:**
- Segment player with waveform
- Text transcription for each segment
- Timestamp and duration info
- Quality score per segment
- Individual download buttons

---

## 3. F5-TTS GENERATION

### Progress Tab (During Processing)
**Stages:**
1. **Initialize** - Load F5-TTS model and voice samples
2. **Process** - Generate speech from text input
3. **Enhance** - Apply audio post-processing
4. **Export** - Save audio file with metadata
5. **Complete** - Finalize and cleanup

**Real-time Stats:**
- Text Length: `245 characters`
- Voice Model: `Female_Voice_01`
- Processing Speed: `0.8x realtime`
- Quality: `High (48kHz)`
- Format: `WAV`
- Estimated Size: `12.4 MB`

**Preview Area:**
- Text being processed (highlighted by sentence)
- Voice model visualization
- Real-time audio waveform (if possible)

**Progress Indicators:**
- Text processing progress (characters/words)
- Model inference progress
- Audio generation speed

### Results Tab (After Completion)
**Summary Cards:**
- Generation Info (text length, voice model, processing time)
- Audio Properties (duration, format, quality, file size)
- Model Information (F5-TTS version, voice characteristics)

**Action Buttons:**
- Play Generated Audio
- Download Audio File
- Generate Variations
- Try Different Voice
- Generate More Text

**Audio Player:**
- Full waveform visualization
- Playback controls with timeline
- Text synchronization (highlight text as it plays)
- Audio quality metrics
- Export format options

---

## 4. SCENE DETECTION (Standalone)

### Progress Tab (During Processing)
**Stages:**
1. **Load** - Load video and prepare for analysis
2. **Analyze** - Detect scene boundaries frame by frame
3. **Filter** - Apply thresholds and merge similar scenes
4. **Preview** - Generate scene thumbnails
5. **Complete** - Save scene data and thumbnails

**Real-time Stats:**
- Current Frame: `2,150`
- Scenes Found: `12`
- Threshold: `0.75`
- Min Scene Length: `2.0s`
- Processing Speed: `120 fps`
- Video Duration: `45:30`

**Preview Area:**
- Current frame being analyzed
- Scene boundary indicators
- Similarity score visualization

### Results Tab (After Completion)
**Summary Cards:**
- Video Analysis (total scenes, average scene length, processing time)
- Detection Settings (threshold used, filters applied)
- Scene Statistics (shortest/longest scenes, scene distribution)

**Action Buttons:**
- View Scene Timeline
- Export Scene List
- Adjust Thresholds
- Split Video by Scenes
- Analyze Another Video

**Scene Timeline:**
- Interactive timeline with scene boundaries
- Scene thumbnails with timestamps
- Scene duration and transition scores
- Export individual scenes option

---

## 5. BATCH PROCESSING

### Progress Tab (During Processing)
**Stages:**
1. **Queue** - Validate and prepare batch items
2. **Process** - Execute operations on each item
3. **Verify** - Check results and handle errors
4. **Cleanup** - Organize output files
5. **Complete** - Generate batch summary

**Real-time Stats:**
- Items Completed: `3/12`
- Current Item: `video_004.mp4`
- Success Rate: `100%`
- Total Progress: `25%`
- Estimated Remaining: `45:30`
- Errors: `0`

**Preview Area:**
- Current item being processed
- Batch queue overview
- Error/success indicators per item

### Results Tab (After Completion)
**Summary Cards:**
- Batch Summary (total items, success/failure counts, total processing time)
- Output Organization (folder structure, file counts, total size)
- Error Report (failed items, reasons, suggestions)

**Action Buttons:**
- View All Results
- Download Results (ZIP)
- Retry Failed Items
- Process Another Batch
- Export Report

**Batch Results:**
- Success/failure status per item
- Individual result previews
- Error details and logs
- Bulk action buttons
- Export options per item

---

## UI/UX Considerations

### Progress Tab Design Principles
- **Real-time updates** without blocking UI
- **Clear visual hierarchy** (progress bar > stages > stats > preview)
- **Informative but not overwhelming** stats
- **Consistent layout** across all operation types
- **Responsive design** for different window sizes

### Results Tab Design Principles
- **Quick access** to most common actions
- **Visual results preview** when possible
- **Clear success/error indicators**
- **Export and sharing options** prominent
- **Easy navigation** back to processing new items

### Common Components
- **Progress bars** with percentage and ETA
- **Stage indicators** with visual feedback
- **Stats panels** with consistent formatting
- **Preview areas** tailored to content type
- **Action buttons** with clear labeling
- **Error handling** with helpful messages

### Responsive Behavior
- **Mobile/small screens**: Stack components vertically, hide less critical stats
- **Large screens**: Side-by-side layout with expanded previews
- **Dark/Light themes**: Consistent styling across both modes
- **Accessibility**: Screen reader support, keyboard navigation

---

## Implementation Notes

### State Management
- Each operation type should have its own progress state class
- Universal progress view should delegate to operation-specific handlers
- Results should be cached and available for review after completion

### Performance Considerations
- Progress updates should be throttled (max 30fps UI updates)
- Preview images should be compressed for display
- Large result sets should use virtual scrolling
- Background processing should not block UI interactions

### Error Handling
- Clear error messages with suggested solutions
- Ability to retry failed operations
- Detailed error logs for debugging
- Graceful degradation when features are unavailable

### Extensibility
- Plugin system for custom operation types
- Configurable stats and preview options
- Customizable export formats
- Theme and layout customization options