"""
Main Wireframe Runner
Displays all wireframes in a tabbed interface for easy comparison and testing
"""
import flet as ft
from video_frames_wireframe import VideoFramesWireframe
from audio_split_wireframe import AudioSplitWireframe
from f5_tts_wireframe import F5TTSWireframe
from settings_wireframe import SettingsWireframe


class WireframeRunner:
    def __init__(self, page: ft.Page):
        self.page = page
        self.current_theme = ft.ThemeMode.SYSTEM
        
        # Configure app themes with proper color schemes
        self.page.theme = ft.Theme(
            color_scheme_seed=ft.Colors.BLUE,
            color_scheme=ft.ColorScheme(
                primary=ft.Colors.BLUE_700,
                secondary=ft.Colors.INDIGO_400,
                surface=ft.Colors.GREY_50,
                background=ft.Colors.WHITE,
                on_surface=ft.Colors.GREY_900,
                on_background=ft.Colors.GREY_900
            )
        )
        
        self.page.dark_theme = ft.Theme(
            color_scheme_seed=ft.Colors.BLUE,
            color_scheme=ft.ColorScheme(
                primary=ft.Colors.BLUE_400,
                secondary=ft.Colors.INDIGO_300,
                surface=ft.Colors.GREY_900,
                background=ft.Colors.GREY_900,
                on_surface=ft.Colors.GREY_100,
                on_background=ft.Colors.GREY_100
            )
        )
        
        self.page.theme_mode = self.current_theme
        
        # Initialize wireframes
        self.video_wireframe = VideoFramesWireframe(page)
        self.audio_wireframe = AudioSplitWireframe(page)
        self.f5tts_wireframe = F5TTSWireframe(page)
        self.settings_wireframe = SettingsWireframe(page)
        
        self.build_ui()
    
    def build_ui(self):
        """Build the main tabbed interface"""
        # Header with theme toggle
        header = ft.Container(
            content=ft.Row([
                ft.Row([
                    ft.Icon(
                        ft.Icons.VIDEO_LIBRARY, 
                        size=32, 
                        color=ft.Colors.PRIMARY
                    ),
                    ft.Text(
                        "Vid2Frames", 
                        size=28, 
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.ON_SURFACE
                    )
                ], spacing=12),
                ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.LIGHT_MODE if self.current_theme == ft.ThemeMode.DARK else ft.Icons.DARK_MODE,
                        tooltip="Toggle theme",
                        on_click=self.toggle_theme,
                        animate_rotation=ft.Animation(300, ft.AnimationCurve.EASE_IN_OUT)
                    ),
                    ft.Text(f"Theme: {'Dark' if self.current_theme == ft.ThemeMode.DARK else 'Light'}", size=12)
                ], spacing=10)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(horizontal=40, vertical=20),
            bgcolor=ft.Colors.GREY_100,
            border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_400)),
            animate=ft.Animation(300, ft.AnimationCurve.EASE_IN_OUT)
        )
        
        # Info banner
        info_banner = ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.INFO_OUTLINE, color=ft.Colors.PRIMARY),
                ft.Column([
                    ft.Text(
                        "Interactive wireframes demonstrating modern video processing workflows",
                        size=16,
                        weight=ft.FontWeight.W_500,
                        color=ft.Colors.ON_SURFACE,
                    ),
                    ft.Text(
                        "Each tab showcases a complete user journey with integrated progress tracking and results display",
                        size=14,
                        color=ft.Colors.ON_SURFACE_VARIANT,
                    )
                ], spacing=4, expand=True)
            ], spacing=12),
            bgcolor=ft.Colors.BLUE_100,
            border_radius=12,
            padding=20,
            margin=ft.margin.symmetric(horizontal=40, vertical=20),
            border=ft.border.all(1, ft.Colors.BLUE_200),
            animate=ft.Animation(300, ft.AnimationCurve.EASE_IN_OUT)
        )
        
        # Main tabs with modern styling
        main_tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            expand=True,
            tabs=[
                ft.Tab(
                    text="Video Upload",
                    icon=ft.Icons.CLOUD_UPLOAD_OUTLINED,
                    content=ft.AnimatedSwitcher(
                        content=self.video_wireframe.build(),
                        duration=300,
                        transition=ft.AnimatedSwitcherTransition.FADE
                    )
                ),
                ft.Tab(
                    text="Audio Split",
                    icon=ft.Icons.CONTENT_CUT,
                    content=ft.AnimatedSwitcher(
                        content=self.audio_wireframe.build(),
                        duration=300,
                        transition=ft.AnimatedSwitcherTransition.FADE
                    )
                ),
                ft.Tab(
                    text="F5-TTS",
                    icon=ft.Icons.RECORD_VOICE_OVER,
                    content=ft.AnimatedSwitcher(
                        content=self.f5tts_wireframe.build(),
                        duration=300,
                        transition=ft.AnimatedSwitcherTransition.FADE
                    )
                ),
                ft.Tab(
                    text="Settings",
                    icon=ft.Icons.SETTINGS,
                    content=ft.AnimatedSwitcher(
                        content=self.settings_wireframe.build(),
                        duration=300,
                        transition=ft.AnimatedSwitcherTransition.FADE
                    )
                )
            ]
        )
        
        # Footer with modern styling
        footer = ft.Container(
            content=ft.Column([
                ft.Text("💡 Click through the tabs to explore each workflow", size=14, color=ft.Colors.ON_SURFACE_VARIANT),
                ft.Text("🎨 Toggle between light and dark themes using the button above", size=14, color=ft.Colors.ON_SURFACE_VARIANT),
                ft.Text("🎯 Each workflow demonstrates complete user journeys with modern UX patterns", size=14, color=ft.Colors.ON_SURFACE_VARIANT)
            ], spacing=6),
            padding=20,
            bgcolor=ft.Colors.GREY_100,
            border=ft.border.only(top=ft.BorderSide(1, ft.Colors.GREY_400)),
            animate=ft.Animation(300, ft.AnimationCurve.EASE_IN_OUT)
        )
        
        # Main layout
        main_layout = ft.Column([
            header,
            info_banner,
            ft.Container(main_tabs, expand=True),
            footer
        ], expand=True)
        
        self.page.add(main_layout)
    
    def toggle_theme(self, e):
        """Toggle between light and dark themes with animations"""
        # Animate icon rotation
        e.control.rotate = 3.14159  # π radians for 180° rotation
        e.control.animate_rotation = ft.Animation(300, ft.AnimationCurve.EASE_IN_OUT)
        
        if self.current_theme == ft.ThemeMode.LIGHT:
            self.current_theme = ft.ThemeMode.DARK
            e.control.icon = ft.Icons.LIGHT_MODE
        elif self.current_theme == ft.ThemeMode.DARK:
            self.current_theme = ft.ThemeMode.LIGHT
            e.control.icon = ft.Icons.DARK_MODE
        else:  # SYSTEM
            self.current_theme = ft.ThemeMode.LIGHT
            e.control.icon = ft.Icons.DARK_MODE
            
        self.page.theme_mode = self.current_theme
        
        # Update theme text with animation
        theme_text = e.control.parent.controls[1]
        theme_text.value = f"Theme: {'Dark' if self.current_theme == ft.ThemeMode.DARK else 'Light'}"
        
        # Show animated snackbar
        self.page.show_snack_bar(
            ft.SnackBar(
                content=ft.Text(f"🎨 Switched to {'Dark' if self.current_theme == ft.ThemeMode.DARK else 'Light'} theme"),
                action="OK",
                open=True,
                bgcolor=ft.Colors.BLUE_100,
                show_close_icon=True
            )
        )
        
        # Animate the page update
        self.page.update()


def main(page: ft.Page):
    # Configure page
    page.title = "Vid2Frames - Interactive Wireframes"
    page.theme_mode = ft.ThemeMode.SYSTEM
    page.window.width = 1400
    page.window.height = 900
    page.window.min_width = 1200
    page.window.min_height = 800
    
    # Set padding to 0 for full window usage
    page.padding = 0
    page.spacing = 0
    
    # Initialize wireframe runner
    WireframeRunner(page)


if __name__ == "__main__":
    ft.app(target=main)