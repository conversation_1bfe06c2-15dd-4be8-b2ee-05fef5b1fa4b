"""
Debug script to test audio extraction with your specific alignment data
"""
import sys
import json
from pathlib import Path

# Add src to path so we can import modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.core.audio_splitter import AudioSplitter
    
    def debug_audio_extraction():
        """Debug the audio extraction process with your alignment data"""
        print("🔍 Audio Extraction Debug")
        print("=" * 40)
        
        # Your alignment data from the JSON
        alignment_json_path = Path("C:/Users/<USER>/Downloads/7habits/autonoe_7habits_split_20250930_171245/alignment_info.json")
        
        if not alignment_json_path.exists():
            print(f"❌ Alignment file not found: {alignment_json_path}")
            print("Please update the path to match your alignment_info.json file")
            return
        
        # Load the alignment data
        with open(alignment_json_path, 'r', encoding='utf-8') as f:
            alignment_data = json.load(f)
        
        print(f"📄 Loaded {len(alignment_data)} segments from alignment file")
        print()
        
        # Show the first few segments with their timing
        print("First 3 segments from your JSON:")
        for i, segment in enumerate(alignment_data[:3]):
            print(f"Segment {segment.get('segment_number', i+1)}:")
            print(f"  Time: {segment['start_time']:.2f}s - {segment['end_time']:.2f}s ({segment.get('duration', 0):.2f}s)")
            print(f"  Text: '{segment['text']}'")
            print(f"  Confidence: {segment.get('confidence', 'N/A')}")
            print()
        
        # Find the original audio file (not the split segments)
        audio_path = None
        
        # Check for common original file names
        possible_paths = [
            Path("C:/Users/<USER>/Downloads/7habits/autonoe_7habits.wav"),
            Path("C:/Users/<USER>/Downloads/7habits/gemini_puck.wav"),
            alignment_json_path.parent.parent / "autonoe_7habits.wav",
            alignment_json_path.parent.parent / "audio.wav",
        ]
        
        for test_path in possible_paths:
            if test_path.exists() and not test_path.name.startswith("segment_"):
                audio_path = test_path
                break
        
        if not audio_path:
            print("❌ Original audio file not found. Looking for non-segment files:")
            # Look in parent directory too
            for search_dir in [alignment_json_path.parent, alignment_json_path.parent.parent]:
                if search_dir.exists():
                    for file in search_dir.iterdir():
                        if (file.suffix.lower() in ['.wav', '.mp3', '.m4a', '.flac', '.ogg'] and 
                            not file.name.startswith("segment_")):
                            print(f"  Found: {file}")
                            if not audio_path:  # Use the first one found
                                audio_path = file
        
        if not audio_path:
            print("Please place your audio file in the same directory as alignment_info.json")
            return
        
        print(f"🎵 Audio file: {audio_path}")
        print()
        
        # Test extraction of the first segment
        splitter = AudioSplitter()
        
        # Test the exact timing from your JSON
        segment_1 = alignment_data[0]
        start_time = segment_1['start_time']  # 0.0
        end_time = segment_1['end_time']      # 5.78
        expected_text = segment_1['text']
        
        print(f"🧪 Testing extraction of Segment 1:")
        print(f"   Start: {start_time}s")
        print(f"   End: {end_time}s") 
        print(f"   Duration: {end_time - start_time}s")
        print(f"   Expected text: '{expected_text}'")
        print()
        
        # Create test output
        test_output_dir = Path("debug_audio_test")
        test_output_dir.mkdir(exist_ok=True)
        
        test_output_path = test_output_dir / "test_segment_1.wav"
        
        # Extract with no buffer first
        print("🔧 Extracting with 0ms buffer (exact timing)...")
        success = splitter.extract_audio_segment(
            audio_path=audio_path,
            start_time=start_time,
            end_time=end_time,
            output_path=test_output_path,
            output_format="wav",
            buffer_ms=0  # No buffer
        )
        
        if success:
            print(f"✅ Extracted to: {test_output_path}")
            print(f"   File size: {test_output_path.stat().st_size} bytes")
        else:
            print("❌ Extraction failed")
            return
        
        # Extract with 100ms buffer
        test_output_path_buffered = test_output_dir / "test_segment_1_buffered.wav"
        print()
        print("🔧 Extracting with 100ms buffer...")
        success_buffered = splitter.extract_audio_segment(
            audio_path=audio_path,
            start_time=start_time,
            end_time=end_time,
            output_path=test_output_path_buffered,
            output_format="wav",
            buffer_ms=100  # 100ms buffer
        )
        
        if success_buffered:
            print(f"✅ Extracted to: {test_output_path_buffered}")
            print(f"   File size: {test_output_path_buffered.stat().st_size} bytes")
        
        print()
        print("🎧 Please listen to these test files:")
        print(f"   {test_output_path} (exact timing)")
        print(f"   {test_output_path_buffered} (with buffer)")
        print()
        print("Compare with what you expect:")
        print(f"   Should contain: '{expected_text}'")
        print()
        
        # Also test segment 2 to see the overlap issue
        if len(alignment_data) > 1:
            segment_2 = alignment_data[1]
            print(f"🧪 For comparison, Segment 2 timing:")
            print(f"   Start: {segment_2['start_time']}s")
            print(f"   End: {segment_2['end_time']}s")
            print(f"   Text: '{segment_2['text']}'")
            print(f"   Gap between segments: {segment_2['start_time'] - segment_1['end_time']:.3f}s")
    
    if __name__ == "__main__":
        debug_audio_extraction()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the Vid2Frames directory")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()