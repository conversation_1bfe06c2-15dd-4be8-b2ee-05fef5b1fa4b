#!/usr/bin/env python3
"""
Test the current fixes for Vid2Frames issues
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.ui.main_window import MainWindow
import flet as ft

def main(page: ft.Page):
    """Test the main window with current fixes"""
    
    # Configure the page
    page.title = "Vid2Frames - Test Current Fixes"
    page.theme_mode = ft.ThemeMode.SYSTEM
    page.padding = 0
    page.window.width = 1200
    page.window.height = 800
    page.window.min_width = 800
    page.window.min_height = 600
    
    # Create main window
    main_window = MainWindow()
    main_window.set_page(page)
    
    # Add content to page
    page.add(main_window.build())

if __name__ == "__main__":
    print("🔧 Testing current fixes for Vid2Frames issues...")
    print("✅ Fixed: Main window result processing (nested result extraction)")
    print("✅ Fixed: Frame preview during processing (OpenCV to base64 conversion)")
    print("✅ Fixed: Decimal frame count display (integer conversion)")
    print("🔄 Starting test application...")
    
    ft.app(target=main, view=ft.AppView.FLET_APP)