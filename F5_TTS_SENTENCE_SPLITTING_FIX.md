# F5-TTS Sentence Splitting Fix

## Issue
F5-T<PERSON> was incorrectly splitting text input on periods (and other sentence delimiters) instead of processing one audio file per line as intended. This caused lines like:

"Let's get straight into The 7 Habits of Highly Effective People by <PERSON>."

To be split into multiple audio files, with "Covey" becoming a separate audio file.

## Root Cause
In `src/ui/f5_tts_view.py`, the `get_sentences_from_input()` method had additional sentence splitting logic:

```python
# Further split by sentence endings if needed
line_sentences = re.split(r'[.!?]+', line)
```

This was splitting each line by periods, question marks, and exclamation marks, which is not the intended behavior.

## Fix
Removed the additional sentence splitting logic and now only split by newlines:

```python
def get_sentences_from_input(self) -> List[str]:
    """Extract sentences from text input"""
    text = self.text_input.value.strip() if self.text_input.value else ""
    if not text:
        return []
    
    # Split by lines only - each line becomes one audio file
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    return lines
```

## Expected Behavior
- Each line in the text input becomes one audio file
- Periods within a line do not cause additional splits
- Empty lines are ignored

## Test Case
The test case from the user's screenshot now works correctly:
- Input: 7 lines of text
- Output: 7 audio files (one per line)
- "Let's get straight into The 7 Habits of Highly Effective People by Stephen R. Covey." remains as one audio file

## Files Modified
- `src/ui/f5_tts_view.py` - Fixed sentence splitting logic
- `test_f5tts_sentence_fix.py` - Added test to verify the fix

## Date
September 30, 2025