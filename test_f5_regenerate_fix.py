#!/usr/bin/env python3
"""
Test script to verify F5-TTS regenerate button fix
"""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path('.') / 'src'))

def test_f5tts_result_processing():
    """Test that F5-TTS results are properly processed"""
    from src.ui.results_view import ResultsView
    import flet as ft
    
    print("🧪 Testing F5-TTS result processing...")
    
    # Create a mock main window
    class MockMainWindow:
        def __init__(self):
            self.page = None
    
    # Create a mock results view
    mock_main_window = MockMainWindow()
    results_view = ResultsView(mock_main_window)
    
    # Test F5-TTS result format (what we actually get)
    f5tts_result = {
        'type': 'f5_tts_generation',
        'success': True,  # Now included
        'output_dir': 'C:\\Git\\Vid2Frames\\f5_tts_output_20250928_163226',
        'sentences_count': 171,
        'results': [
            {'file': 'test1.wav', 'sentence': 'Test sentence 1'},
            {'file': 'test2.wav', 'sentence': 'Test sentence 2'}
        ]
    }
    
    # Test old-style result format (for compatibility)
    old_style_result = {
        'success': True,
        'job_dir': 'C:\\Git\\Vid2Frames\\output_dir',
        'detected_scenes': [],
        'transcriptions': []
    }
    
    # Test invalid result
    invalid_result = {
        'type': 'something_else',
        'no_success_key': True
    }
    
    print("✅ Testing F5-TTS result format...")
    try:
        results_view.set_results(f5tts_result)
        print("✅ F5-TTS result accepted successfully")
    except Exception as e:
        print(f"❌ F5-TTS result failed: {e}")
        
    print("✅ Testing old-style result format...")
    try:
        results_view.set_results(old_style_result)
        print("✅ Old-style result accepted successfully")
    except Exception as e:
        print(f"❌ Old-style result failed: {e}")
        
    print("✅ Testing invalid result format...")
    try:
        results_view.set_results(invalid_result)
        print("❌ Invalid result was incorrectly accepted")
    except Exception as e:
        print("✅ Invalid result correctly rejected")

def test_processing_flag_logic():
    """Test the processing flag reset logic"""
    print("\n🧪 Testing processing flag reset...")
    
    # Mock F5-TTS view
    class MockF5TTSView:
        def __init__(self):
            self.is_processing = False
    
    # Mock main window
    class MockMainWindow:
        def __init__(self):
            self.f5_tts_view = MockF5TTSView()
            self.current_view = ""
            self.nav_rail = None
            self.content_area = None
            self.results_view = None
            self.processing_result = None
            self.page = None
        
        def show_results_view(self):
            """Show the results view"""
            self.current_view = "results"
            
            # Reset processing flags in views that might be stuck
            if hasattr(self, 'f5_tts_view'):
                self.f5_tts_view.is_processing = False
                print("✅ Processing flag reset to False")
    
    # Test the flag reset
    main_window = MockMainWindow()
    
    # Simulate processing state
    main_window.f5_tts_view.is_processing = True
    print(f"🔄 Processing flag before: {main_window.f5_tts_view.is_processing}")
    
    # Show results (this should reset the flag)
    main_window.show_results_view()
    print(f"🔄 Processing flag after: {main_window.f5_tts_view.is_processing}")
    
    if not main_window.f5_tts_view.is_processing:
        print("✅ Processing flag properly reset - regenerate button should work")
    else:
        print("❌ Processing flag not reset - regenerate button will be stuck")

if __name__ == "__main__":
    test_f5tts_result_processing()
    test_processing_flag_logic()
    print("\n🎉 F5-TTS regenerate fix test completed!")