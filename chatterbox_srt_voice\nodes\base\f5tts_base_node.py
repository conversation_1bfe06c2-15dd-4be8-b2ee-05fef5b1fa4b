"""
Base F5-TTS Node - Common functionality for all F5-TTS nodes
Extends ChatterBox foundation for F5-TTS specific requirements
"""

import torch
import numpy as np
import tempfile
import os
import hashlib
from typing import Dict, Any, Optional, Tuple, List

# Use direct file imports that work when loaded via importlib
import os
import sys
import importlib.util

# Add project root directory to path for imports
# When loaded via importlib, __file__ might not resolve correctly
# So we'll check if utils is already accessible, and if not, try to find the project root
current_dir = os.path.dirname(os.path.abspath(__file__))
try:
    import utils.text.chunking
    utils_available = True
except ImportError:
    # Find project root by going up from this file location
    nodes_dir = os.path.dirname(current_dir)  # nodes/
    project_root = os.path.dirname(nodes_dir)  # project root
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    utils_available = True

# Load base_node module directly
base_node_path = os.path.join(current_dir, "base_node.py")
base_spec = importlib.util.spec_from_file_location("base_node_module", base_node_path)
base_module = importlib.util.module_from_spec(base_spec)
sys.modules["base_node_module"] = base_module
base_spec.loader.exec_module(base_module)

# Import the base class
BaseChatterBoxNode = base_module.BaseChatterBoxNode

from utils.text.chunking import ImprovedChatterBoxChunker
from utils.audio.processing import AudioProcessingUtils
from utils.text.pause_processor import PauseTagProcessor
import comfy.model_management as model_management

# F5-TTS specific constants
F5TTS_SAMPLE_RATE = 24000
F5TTS_ERROR_MESSAGES = {
    "missing_ref_text": "F5-TTS requires both reference audio AND reference text. Please provide ref_text parameter.",
    "model_not_found": "F5-TTS model '{}' not found. Available models: {}",
    "audio_format_error": "Audio format not compatible with F5-TTS. Expected 24kHz, got {}kHz.",
    "memory_error": "Insufficient memory for F5-TTS generation. Try reducing chunk size or switching to CPU.",
    "import_error": "F5-TTS not available. Please install F5-TTS dependencies."
}


class BaseF5TTSNode(BaseChatterBoxNode):
    """
    Base class for F5-TTS nodes extending ChatterBox foundation.
    Handles F5-TTS specific requirements while maintaining compatibility.
    """
    
    # Node metadata
    CATEGORY = "F5-TTS Voice"
    
    def __init__(self):
        super().__init__()
        self.f5tts_model = None
        self.f5tts_sample_rate = F5TTS_SAMPLE_RATE
        self.chunker = ImprovedChatterBoxChunker()
        
        # F5-TTS availability check
        self.f5tts_available = False
        self._check_f5tts_availability()
    
    def _check_f5tts_availability(self):
        """Check if F5-TTS is available"""
        try:
            from engines.f5tts import F5TTS_AVAILABLE
            self.f5tts_available = F5TTS_AVAILABLE
        except ImportError:
            self.f5tts_available = False
    
    def load_f5tts_model(self, model_name: str = "F5TTS_Base", device: str = "auto", force_reload: bool = False):
        """
        Load F5-TTS model using enhanced model manager
        """
        if not self.f5tts_available:
            raise ImportError(F5TTS_ERROR_MESSAGES["import_error"])
        
        device = self.resolve_device(device)
        self.device = device
        
        # Normalize model name for caching consistency
        normalized_model_name = model_name.replace("local:", "") if model_name.startswith("local:") else model_name
        
        # Check if we need to reload (use normalized name for cache consistency)
        if not force_reload and self.f5tts_model is not None and getattr(self, 'current_model_name', None) == normalized_model_name:
            return self.f5tts_model
        
        try:
            from engines.f5tts import ChatterBoxF5TTS
            
            # Try to find local models first
            model_paths = self._find_f5tts_models(model_name)
            
            model_loaded = False
            last_error = None
            
            for source, path in model_paths:
                try:
                    if source == "comfyui" and path:
                        # Load from local ComfyUI models directory
                        # Normalize model name (remove local: prefix if present)
                        normalized_name = model_name.replace("local:", "") if model_name.startswith("local:") else model_name
                        self.f5tts_model = ChatterBoxF5TTS.from_local(path, device, normalized_name)
                    else:
                        # Load from HuggingFace - but check if we have local files first
                        local_path = None
                        if model_name in ["F5TTS_Base", "F5TTS_v1_Base", "E2TTS_Base"]:
                            import folder_paths
                            potential_path = os.path.join(folder_paths.models_dir, "F5-TTS", model_name)
                            if os.path.exists(potential_path):
                                local_path = potential_path
                        
                        if local_path:
                            # Use local files even for non-local model names for consistency
                            self.f5tts_model = ChatterBoxF5TTS.from_local(local_path, device, model_name)
                        else:
                            # True HuggingFace download
                            self.f5tts_model = ChatterBoxF5TTS.from_pretrained(device, model_name)
                    
                    model_loaded = True
                    break
                    
                except Exception as e:
                    print(f"⚠️ Failed to load F5-TTS model from {source}: {str(e)}")
                    last_error = e
                    continue
            
            if not model_loaded:
                error_msg = f"Failed to load F5-TTS model '{model_name}' from any source"
                if last_error:
                    error_msg += f". Last error: {last_error}"
                raise RuntimeError(error_msg)
            
            # Store normalized model name for cache validation
            self.current_model_name = normalized_model_name
            return self.f5tts_model
            
        except ImportError:
            raise ImportError(F5TTS_ERROR_MESSAGES["import_error"])
        except Exception as e:
            raise RuntimeError(f"Failed to load F5-TTS model: {e}")
    
    def _find_f5tts_models(self, model_name: str = None) -> List[Tuple[str, Optional[str]]]:
        """Find F5-TTS models following existing pattern"""
        try:
            from utils.models.f5tts_manager import f5tts_model_manager
            return f5tts_model_manager.find_f5tts_models(model_name)
        except ImportError:
            return [("huggingface", None)]
    
    def generate_f5tts_audio(self, text: str, ref_audio_path: str, ref_text: str, 
                            temperature: float = 0.8, speed: float = 1.0,
                            target_rms: float = 0.1, cross_fade_duration: float = 0.15,
                            nfe_step: int = 32, cfg_strength: float = 2.0) -> torch.Tensor:
        """Generate audio using F5-TTS with reference audio+text"""
        if self.f5tts_model is None:
            raise RuntimeError("F5-TTS model not loaded. Call load_f5tts_model() first.")
        
        return self.f5tts_model.generate(
            text=text,
            ref_audio_path=ref_audio_path,
            ref_text=ref_text,
            temperature=temperature,
            speed=speed,
            target_rms=target_rms,
            cross_fade_duration=cross_fade_duration,
            nfe_step=nfe_step,
            cfg_strength=cfg_strength
        )

    def generate_f5tts_with_pause_tags(self, text: str, ref_audio_path: str, ref_text: str,
                                     enable_pause_tags: bool = True, character: str = "narrator", 
                                     seed: int = 0, enable_cache: bool = False, cache_fn=None, **generation_kwargs) -> torch.Tensor:
        """
        Generate F5-TTS audio with pause tag support.
        
        Args:
            text: Input text potentially with pause tags
            ref_audio_path: Reference audio file path
            ref_text: Reference text
            enable_pause_tags: Whether to process pause tags
            character: Character name for cache key
            seed: Seed for reproducibility and cache key
            enable_cache: Whether to use caching
            cache_fn: Function to handle caching (cache_fn(text_content) -> cached_audio or None)
            **generation_kwargs: Additional F5-TTS generation parameters
            
        Returns:
            Generated audio tensor with pauses
        """
        # Preprocess text for pause tags
        processed_text, pause_segments = PauseTagProcessor.preprocess_text_with_pause_tags(
            text, enable_pause_tags
        )
        
        if pause_segments is None:
            # No pause tags, use regular generation with optional caching
            if enable_cache and cache_fn:
                cached_audio = cache_fn(processed_text)
                if cached_audio is not None:
                    return cached_audio
            
            audio = self.generate_f5tts_audio(
                processed_text, ref_audio_path, ref_text, **generation_kwargs
            )
            
            if enable_cache and cache_fn:
                cache_fn(processed_text, audio)  # Cache the result
            
            return audio
        
        # Generate audio with pause tags, with optional caching per text segment
        def f5tts_generate_func(text_content: str) -> torch.Tensor:
            """F5-TTS generation function for pause tag processor with optional caching"""
            if enable_cache and cache_fn:
                cached_audio = cache_fn(text_content)
                if cached_audio is not None:
                    return cached_audio
            
            audio = self.generate_f5tts_audio(
                text_content, ref_audio_path, ref_text, **generation_kwargs
            )
            
            if enable_cache and cache_fn:
                cache_fn(text_content, audio)  # Cache the result
            
            return audio
        
        return PauseTagProcessor.generate_audio_with_pauses(
            pause_segments, f5tts_generate_func, F5TTS_SAMPLE_RATE
        )
    
    def handle_f5tts_reference(self, reference_audio: Optional[Dict[str, Any]], 
                              audio_prompt_path: str, ref_text: str) -> Tuple[Optional[str], str]:
        """
        Handle F5-TTS reference audio and text requirements
        Returns (audio_path, validated_ref_text)
        """
        # Validate reference text is provided
        if not ref_text or not ref_text.strip():
            raise ValueError(F5TTS_ERROR_MESSAGES["missing_ref_text"])
        
        # Handle reference audio (same as base class but with F5-TTS validation)
        audio_prompt = self.handle_reference_audio(reference_audio, audio_prompt_path)
        
        if audio_prompt is None:
            raise ValueError("F5-TTS requires reference audio. Please provide either reference_audio input or audio_prompt_path.")
        
        return audio_prompt, ref_text.strip()
    
    def validate_f5tts_inputs(self, **inputs) -> Dict[str, Any]:
        """Validate F5-TTS specific inputs including reference text"""
        # Call the base class validation directly (from BaseChatterBoxNode)
        validated = super().validate_inputs(**inputs)
        
        # Ensure ref_text is provided and not empty
        ref_text = validated.get("ref_text", "")
        if not ref_text or not ref_text.strip():
            raise ValueError(F5TTS_ERROR_MESSAGES["missing_ref_text"])
        
        validated["ref_text"] = ref_text.strip()
        
        # Validate model name if provided
        model_name = validated.get("model", "F5TTS_Base")
        available_models = self._get_available_models()
        if model_name not in available_models:
            raise ValueError(F5TTS_ERROR_MESSAGES["model_not_found"].format(model_name, available_models))
        
        return validated
    
    def _get_available_models(self) -> List[str]:
        """Get list of available F5-TTS models"""
        try:
            from engines.f5tts import get_f5tts_models
            return get_f5tts_models()
        except ImportError:
            return ["F5TTS_Base", "F5TTS_v1_Base", "E2TTS_Base"]
    
    @classmethod
    def get_available_models_for_dropdown(cls) -> List[str]:
        """Get list of available F5-TTS models for dropdown use in INPUT_TYPES"""
        try:
            from engines.f5tts import get_f5tts_models
            return get_f5tts_models()
        except ImportError:
            return ["F5TTS_Base", "F5TTS_v1_Base", "E2TTS_Base"]
    
    def format_f5tts_audio_output(self, audio_tensor: torch.Tensor) -> Dict[str, Any]:
        """Format F5-TTS audio tensor for ComfyUI output"""
        return self.format_audio_output(audio_tensor, self.f5tts_sample_rate)
    
    def combine_f5tts_audio_chunks(self, audio_segments: List[torch.Tensor], method: str, 
                                  silence_ms: int, text_length: int) -> torch.Tensor:
        """Combine F5-TTS audio segments using specified method"""
        if len(audio_segments) == 1:
            return audio_segments[0]
        
        # Auto-select best method based on text length
        if method == "auto":
            if text_length > 1000:  # Very long text
                method = "silence_padding"
            elif text_length > 500:  # Medium text
                method = "crossfade"
            else:  # Short text
                method = "concatenate"
        
        if method == "concatenate":
            return AudioProcessingUtils.concatenate_audio_segments(audio_segments, "simple")
        
        elif method == "silence_padding":
            silence_duration = silence_ms / 1000.0  # Convert to seconds
            return AudioProcessingUtils.concatenate_audio_segments(
                audio_segments, "silence", silence_duration=silence_duration, 
                sample_rate=self.f5tts_sample_rate
            )
        
        elif method == "crossfade":
            return AudioProcessingUtils.concatenate_audio_segments(
                audio_segments, "crossfade", crossfade_duration=0.1, 
                sample_rate=self.f5tts_sample_rate
            )
        
        else:
            # Fallback to concatenation
            return AudioProcessingUtils.concatenate_audio_segments(audio_segments, "simple")
    
    def get_f5tts_model_info(self) -> Dict[str, Any]:
        """Get information about loaded F5-TTS model"""
        if self.f5tts_model:
            return {
                "model_name": getattr(self.f5tts_model, 'model_name', 'unknown'),
                "sample_rate": self.f5tts_sample_rate,
                "device": self.device,
                "available": self.f5tts_available,
            }
        else:
            return {
                "model_name": None,
                "sample_rate": self.f5tts_sample_rate,
                "device": self.device,
                "available": self.f5tts_available,
            }
    
    @classmethod
    def get_f5tts_input_types_base(cls) -> Dict[str, Dict[str, Any]]:
        """
        Get base input types for F5-TTS nodes.
        Extends base input types with F5-TTS specific requirements.
        """
        base_types = cls.get_input_types_base()
        
        # Add F5-TTS specific required inputs
        base_types["required"].update({
            "ref_text": ("STRING", {
                "multiline": True, 
                "default": "This is the reference text that matches the reference audio.",
                "tooltip": "Text that corresponds to the reference audio. Required for F5-TTS voice cloning."
            }),
            "model": (cls.get_available_models_for_dropdown(), {"default": "F5TTS_v1_Base"}),
        })
        
        # Add F5-TTS specific optional inputs
        base_types["optional"].update({
            "temperature": ("FLOAT", {"default": 0.8, "min": 0.1, "max": 2.0, "step": 0.1}),
            "speed": ("FLOAT", {"default": 1.0, "min": 0.5, "max": 2.0, "step": 0.1}),
            "target_rms": ("FLOAT", {"default": 0.1, "min": 0.01, "max": 1.0, "step": 0.01}),
            "cross_fade_duration": ("FLOAT", {"default": 0.15, "min": 0.0, "max": 1.0, "step": 0.01}),
            "nfe_step": ("INT", {"default": 32, "min": 1, "max": 100}),
            "cfg_strength": ("FLOAT", {"default": 2.0, "min": 0.0, "max": 10.0, "step": 0.1}),
        })
        
        return base_types