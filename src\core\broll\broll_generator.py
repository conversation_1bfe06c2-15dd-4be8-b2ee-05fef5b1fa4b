"""
Main B-roll generator coordinator
Combines keyword extraction and video search
"""

from typing import List, Dict, Optional
from .keyword_extractor import BRollKeywordExtractor
from .pexels_api import PexelsVideoAPI

class BRollGenerator:
    """Main B-roll generation coordinator"""
    
    def __init__(self, pexels_api_key: str):
        self.keyword_extractor = BRollKeywordExtractor()
        self.video_api = PexelsVideoAPI(pexels_api_key)
        self._api_connected = None
        self.used_video_ids = set()  # Track used videos to prevent repetition
    
    def test_connection(self) -> bool:
        """Test if APIs are working"""
        if self._api_connected is None:
            self._api_connected = self.video_api.test_connection()
        return self._api_connected
    
    def reset_used_videos(self):
        """Reset the list of used video IDs for a new project"""
        self.used_video_ids.clear()
        print("🔄 Reset used video IDs - fresh selection for new project")
    
    def generate_broll_data(self, sentences: List[str], 
                           audio_durations: Optional[List[float]] = None) -> Dict:
        """
        Generate B-roll video selections for sentences
        
        Args:
            sentences: List of text sentences
            audio_durations: List of audio durations (optional)
            
        Returns:
            Dictionary with B-roll data for each sentence
        """
        if not sentences:
            return {}
        
        # Test API connection first
        if not self.test_connection():
            raise ConnectionError("Cannot connect to Pexels API. Please check your API key and internet connection.")
        
        # Extract keywords
        print(f"🔍 Extracting keywords for {len(sentences)} sentences...")
        sentence_keywords = self.keyword_extractor.extract_keywords(sentences)
        
        # Generate B-roll for each sentence
        results = {}
        total_sentences = len(sentences)
        
        for i, sentence in enumerate(sentences):
            print(f"📹 Processing sentence {i+1}/{total_sentences}...")
            
            keywords = sentence_keywords.get(i, [])
            target_duration = audio_durations[i] if audio_durations and i < len(audio_durations) else 5.0
            
            # Search for videos, excluding already used ones
            videos = []
            if keywords:
                try:
                    # Get videos and filter out already used ones
                    all_videos = self.video_api.search_videos(keywords, target_duration)
                    videos = [v for v in all_videos if v.get('id') not in self.used_video_ids]
                    
                    # If no unique videos found, allow reuse but prefer different ones
                    if not videos and all_videos:
                        videos = all_videos
                        print(f"⚠️ No unique videos for sentence {i+1}, allowing reuse")
                        
                except Exception as e:
                    print(f"⚠️ Error searching videos for sentence {i+1}: {e}")
            
            # Select the best video and mark it as used
            selected_video = videos[0] if videos else None
            if selected_video and selected_video.get('id'):
                self.used_video_ids.add(selected_video['id'])
                print(f"🎬 Selected unique video for sentence {i+1}: ID {selected_video['id']}")

            results[i] = {
                'sentence': sentence,
                'keywords': keywords,
                'duration_target': target_duration,
                'videos': videos,
                'selected_video': selected_video,
                'alternatives': []  # Will be populated on demand
            }
        
        return results
    
    def create_combined_videos(self, broll_data: Dict, audio_files: List[str], 
                              output_dir: str) -> List[Dict]:
        """
        Create combined video files with B-roll videos and generated audio
        
        Args:
            broll_data: B-roll data from generate_broll_data
            audio_files: List of audio file paths
            output_dir: Directory to save combined videos
            
        Returns:
            List of combined video file information
        """
        try:
            import subprocess
            from pathlib import Path
            import tempfile
            import requests
            
            combined_videos = []
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            print(f"🎬 Creating combined B-roll + audio videos...")
            
            for i, (sentence_idx, data) in enumerate(broll_data.items()):
                selected_video = data.get('selected_video')
                if not selected_video or sentence_idx >= len(audio_files):
                    continue
                
                audio_file = audio_files[sentence_idx]
                if not Path(audio_file).exists():
                    print(f"⚠️ Audio file not found: {audio_file}")
                    continue
                
                # Get B-roll video download URL
                video_url = selected_video.get('download_url', '')
                if not video_url:
                    # Fallback to old structure for compatibility
                    video_files = selected_video.get('video_files', [])
                    video_url = video_files[0].get('link', '') if video_files else ''
                
                if not video_url:
                    print(f"⚠️ No video URL for sentence {sentence_idx + 1}")
                    print(f"   Video data keys: {list(selected_video.keys())}")
                    print(f"   Video ID: {selected_video.get('id', 'unknown')}")
                    continue
                
                print(f"📥 Downloading video for sentence {sentence_idx + 1}...")
                print(f"   Video URL: {video_url[:100]}...")  # Show first 100 chars
                
                try:
                    # Create temporary video file
                    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_video:
                        temp_video_path = temp_video.name
                    
                    # Download video
                    response = requests.get(video_url, stream=True, timeout=30)
                    response.raise_for_status()
                    
                    with open(temp_video_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    
                    # Create output filename with proper sanitization
                    import re
                    sentence_words = data['sentence'][:30]
                    # Remove or replace invalid filename characters
                    sentence_words = re.sub(r'[<>:"/\\|?*"\']', '', sentence_words)  # Remove invalid chars
                    sentence_words = sentence_words.replace(' ', '_').strip('_')
                    # Ensure we have some content
                    if not sentence_words:
                        sentence_words = f"sentence_{sentence_idx + 1}"
                    output_filename = f"{sentence_idx + 1:03d}_{sentence_words}.mp4"
                    output_file = output_path / output_filename
                    
                    # Combine video and audio using ffmpeg
                    print(f"🎵 Combining video and audio for sentence {sentence_idx + 1}...")
                    ffmpeg_cmd = [
                        'ffmpeg', '-y',  # Overwrite output files
                        '-i', temp_video_path,  # Video input
                        '-i', audio_file,       # Audio input
                        '-c:v', 'copy',         # Copy video stream
                        '-c:a', 'aac',          # Encode audio as AAC
                        '-shortest',            # Match shortest stream duration
                        '-avoid_negative_ts', 'make_zero',
                        str(output_file)
                    ]
                    
                    result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        print(f"✅ Created combined video: {output_filename}")
                        combined_videos.append({
                            'sentence_index': sentence_idx,
                            'sentence': data['sentence'],
                            'video_file': str(output_file),
                            'duration': data['duration_target'],
                            'keywords': data['keywords']
                        })
                    else:
                        print(f"❌ FFmpeg error for sentence {sentence_idx + 1}: {result.stderr}")
                    
                    # Cleanup temp file
                    try:
                        Path(temp_video_path).unlink()
                    except:
                        pass
                        
                except Exception as e:
                    print(f"❌ Error creating combined video for sentence {sentence_idx + 1}: {e}")
                    continue
            
            print(f"🎬 Created {len(combined_videos)} combined B-roll videos")
            return combined_videos
            
        except ImportError:
            print("❌ Required modules not available for video combination")
            return []
        except Exception as e:
            print(f"❌ Error in create_combined_videos: {e}")
            return []
    
    def get_alternatives(self, sentence_index: int, keywords: List[str], 
                        exclude_video_ids: List[int] = None) -> List[Dict]:
        """
        Get alternative videos for a specific sentence
        
        Args:
            sentence_index: Index of the sentence
            keywords: Keywords for the sentence
            exclude_video_ids: Video IDs to exclude from results
            
        Returns:
            List of alternative video options
        """
        try:
            return self.video_api.get_alternatives(keywords, exclude_video_ids)
        except Exception as e:
            print(f"⚠️ Error getting alternatives for sentence {sentence_index}: {e}")
            return []
    
    def search_custom_videos(self, custom_query: str, duration_target: float = 5.0) -> List[Dict]:
        """
        Search for videos with a custom query
        
        Args:
            custom_query: Custom search query
            duration_target: Target duration in seconds
            
        Returns:
            List of matching videos
        """
        if not custom_query.strip():
            return []
        
        try:
            # Treat custom query as a single keyword
            return self.video_api.search_videos([custom_query], duration_target, per_page=8)
        except Exception as e:
            print(f"⚠️ Error with custom search '{custom_query}': {e}")
            return []
    
    def get_generation_summary(self, broll_data: Dict) -> Dict:
        """
        Get a summary of the B-roll generation results
        
        Args:
            broll_data: Results from generate_broll_data
            
        Returns:
            Summary statistics
        """
        if not broll_data:
            return {
                'total_sentences': 0,
                'successful_matches': 0,
                'success_rate': 0.0,
                'total_keywords': 0,
                'most_common_keywords': [],
                'average_video_duration': 0.0
            }
        
        total_sentences = len(broll_data)
        successful_matches = sum(1 for data in broll_data.values() if data.get('selected_video'))
        
        all_keywords = []
        video_durations = []
        
        for data in broll_data.values():
            all_keywords.extend(data.get('keywords', []))
            if data.get('selected_video'):
                video_durations.append(data['selected_video']['duration'])
        
        # Count keyword frequency
        from collections import Counter
        keyword_counts = Counter(all_keywords)
        
        return {
            'total_sentences': total_sentences,
            'successful_matches': successful_matches,
            'success_rate': (successful_matches / total_sentences) * 100 if total_sentences > 0 else 0,
            'total_keywords': len(all_keywords),
            'unique_keywords': len(set(all_keywords)),
            'most_common_keywords': keyword_counts.most_common(5),
            'average_video_duration': sum(video_durations) / len(video_durations) if video_durations else 0,
            'total_video_duration': sum(video_durations)
        }