"""
Demo script for F5-TTS functionality
Shows how to use the F5-TTS features programmatically
"""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.f5_tts import F5TTSProcessor

def demo_basic_tts():
    """Demonstrate basic text-to-speech generation"""
    print("🎤 F5-TTS Basic Demo")
    print("=" * 50)
    
    # Initialize processor
    tts = F5TTSProcessor()
    
    # Load model (mock mode)
    print("Loading F5-TTS model...")
    tts.load_model("E2TTS_Base")
    
    # Demo sentences
    sentences = [
        "Hello there! This is a demonstration of F5-TTS in Vid2Frames.",
        "The system can convert any text into natural-sounding speech.",
        "Each sentence is processed individually for maximum flexibility.",
        "You can use this for narration, voice-overs, or any audio content creation.",
    ]
    
    # Set up output directory
    output_dir = Path("demo_f5_tts_output")
    output_dir.mkdir(exist_ok=True)
    
    print(f"\nGenerating {len(sentences)} audio files...")
    
    # Process with custom parameters
    params = {
        'model': 'E2TTS_Base',
        'seed': 42,
        'temperature': 0.7,
        'speed': 1.1,
        'target_rms': 0.12,
        'nfe_step': 32,
        'cfg_strength': 2.5,
        'enable_chunking': True,
        'max_chars_per_chunk': 300
    }
    
    # Generate speech
    results = tts.process_sentences(
        sentences=sentences,
        output_dir=output_dir,
        params=params,
        progress_callback=lambda msg, prog: print(f"  📊 {prog:.0%} - {msg}")
    )
    
    # Show results
    print(f"\n✅ Generation Complete!")
    print(f"  Success: {results['success_count']} files")
    print(f"  Failed: {results['failed_count']} files")
    print(f"  Output: {output_dir}")
    
    if results['output_files']:
        print(f"\n📁 Generated files:")
        for file_path in results['output_files']:
            print(f"  • {file_path.name}")

def demo_voice_cloning():
    """Demonstrate voice cloning with reference audio"""
    print("\n\n🎭 F5-TTS Voice Cloning Demo")
    print("=" * 50)
    
    # This would be used with a real reference audio file
    # For demo purposes, we'll show the process
    
    tts = F5TTSProcessor()
    
    # Example of how voice cloning would work:
    print("Voice cloning workflow:")
    print("1. Load reference audio file (WAV, MP3, etc.)")
    print("2. Provide text that matches the reference audio")
    print("3. Generate new speech using the cloned voice")
    
    # Mock reference setup
    print("\n📁 Setting up reference audio...")
    print("   (In real usage: tts.set_reference_audio('voice_sample.wav', 'Hello, this is my voice.'))")
    
    # Example sentences for cloning
    clone_sentences = [
        "This speech uses the cloned voice from the reference audio.",
        "The F5-TTS system can learn vocal characteristics and speaking style.",
        "This enables personalized voice generation for any text content.",
    ]
    
    print(f"\n🎯 Would generate {len(clone_sentences)} files with cloned voice")
    for i, sentence in enumerate(clone_sentences, 1):
        print(f"  {i:02d}. {sentence}")

def demo_parameter_effects():
    """Demonstrate how different parameters affect generation"""
    print("\n\n🔧 F5-TTS Parameter Effects Demo")
    print("=" * 50)
    
    test_text = "This is a test sentence to demonstrate parameter effects."
    
    parameter_sets = [
        {
            'name': 'Default Settings',
            'params': {'temperature': 0.8, 'speed': 1.0, 'cfg_strength': 2.0}
        },
        {
            'name': 'Creative (High Temperature)',
            'params': {'temperature': 1.5, 'speed': 1.0, 'cfg_strength': 2.0}
        },
        {
            'name': 'Fast Speech',
            'params': {'temperature': 0.8, 'speed': 1.8, 'cfg_strength': 2.0}
        },
        {
            'name': 'Slow & Deliberate',
            'params': {'temperature': 0.6, 'speed': 0.7, 'cfg_strength': 3.0}
        }
    ]
    
    print("Different parameter combinations:")
    for i, param_set in enumerate(parameter_sets, 1):
        print(f"\n{i}. {param_set['name']}:")
        for key, value in param_set['params'].items():
            print(f"   {key}: {value}")
        print(f"   → Would generate: {i:03d}_{param_set['name'].lower().replace(' ', '_')}.wav")

def show_feature_summary():
    """Show a summary of F5-TTS features"""
    print("\n\n🌟 F5-TTS Feature Summary")
    print("=" * 50)
    
    features = [
        "✅ Text-to-Speech Generation",
        "✅ Individual file per sentence",
        "✅ Voice cloning with reference audio",
        "✅ Customizable parameters (speed, temperature, quality)",
        "✅ Multiple model support (E2TTS_Base, F5TTS_Base, F5TTS_Large)",
        "✅ Batch processing with progress tracking",
        "✅ Automatic output organization",
        "✅ GPU/CPU automatic fallback",
        "✅ Integration with Vid2Frames UI",
        "✅ Mock mode for testing without dependencies"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print(f"\n🎯 Use Cases:")
    use_cases = [
        "📚 Audiobook narration",
        "🎥 Video voice-overs", 
        "📻 Podcast content creation",
        "🎓 Educational content",
        "📢 Announcements and alerts",
        "🎨 Creative audio projects",
        "♿ Accessibility features",
        "🌐 Multi-language content"
    ]
    
    for use_case in use_cases:
        print(f"  {use_case}")

if __name__ == "__main__":
    print("🚀 F5-TTS Demo Suite for Vid2Frames")
    print("=" * 70)
    
    try:
        demo_basic_tts()
        demo_voice_cloning()
        demo_parameter_effects()
        show_feature_summary()
        
        print("\n🎉 Demo completed successfully!")
        print("\n💡 To use F5-TTS in the application:")
        print("   1. Run: python src/main.py")
        print("   2. Click the 'F5-TTS' tab in the navigation")
        print("   3. Enter text or load a .txt file")
        print("   4. Adjust settings as needed")
        print("   5. Click 'Generate Speech'")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()