#!/usr/bin/env python3
"""
Test F5-TTS Tab UI Integration
"""

import sys
from pathlib import Path
import flet as ft

# Add src to path  
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main(page: ft.Page):
    page.title = "F5-TTS Test"
    page.theme_mode = ft.ThemeMode.DARK
    
    # Mock main window
    class MockMainWindow:
        def __init__(self):
            self.page = page
    
    try:
        sys.path.append(str(Path(__file__).parent))
        from src.ui.f5_tts_view import F5TTSView
        
        print("🔄 Creating F5-TTS View...")
        main_window = MockMainWindow()
        view = F5TTSView()
        
        print("🔧 Building view UI...")
        view_content = view.build()
        
        print("🎤 Checking ComfyUI voices...")
        print(f"Available voices: {len(view.available_voices) if hasattr(view, 'available_voices') else 0}")
        if hasattr(view, 'available_voices') and view.available_voices:
            for voice in view.available_voices[:3]:
                print(f"  - {voice['name']}")
        
        page.add(ft.Column([
            ft.Text("F5-TTS View Test", size=24, weight=ft.FontWeight.BOLD),
            ft.Divider(),
            view_content
        ]))
        
        # Test input
        if hasattr(view, 'text_input') and view.text_input:
            view.text_input.value = "Hello world\\nThis is line two\\nAnd this is line three"
            print(f"📝 Set test text: {view.text_input.value}")
            
        # Check button state
        if hasattr(view, 'generate_button'):
            print(f"🔘 Generate button exists: {view.generate_button is not None}")
            print(f"✅ View created successfully")
        else:
            print(f"⚠️ Generate button not found")
        
        page.update()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        page.add(ft.Text(f"Error: {e}", color=ft.Colors.RED))

if __name__ == "__main__":
    ft.app(target=main)