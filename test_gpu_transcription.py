"""
Test GPU-accelerated transcription with RTX 5090
================================================

This script tests the updated transcription system with GPU acceleration enabled.
"""

import time
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from core.transcription import LocalTranscriber, TORCH_AVAILABLE

def test_gpu_transcription():
    """Test GPU transcription functionality"""
    print("🧪 Testing GPU-Accelerated Transcription")
    print("=" * 50)
    
    if not TORCH_AVAILABLE:
        print("❌ PyTorch not available")
        return False
    
    try:
        # Initialize transcriber
        print("🔧 Initializing transcriber...")
        transcriber = LocalTranscriber()
        
        print(f"📱 Selected device: {transcriber.device}")
        
        # Load Whisper model
        print("📥 Loading Whisper model...")
        start_time = time.time()
        
        success = transcriber.load_whisper_model()
        if not success:
            print("❌ Failed to load Whisper model")
            return False
            
        load_time = time.time() - start_time
        print(f"✅ Model loaded in {load_time:.1f} seconds")
        
        # Test basic functionality
        print("🎯 Testing basic transcription capability...")
        
        # Create a small test audio array (silence for testing)
        import numpy as np
        test_audio = np.random.randn(16000).astype(np.float32) * 0.001  # Very quiet noise
        
        start_time = time.time()
        result = transcriber._transcribe_with_whisper(test_audio)
        transcribe_time = time.time() - start_time
        
        if result is not None:
            print(f"✅ Transcription successful in {transcribe_time:.2f} seconds")
            print(f"📝 Result: '{result}' (silence expected)")
            
            # Performance analysis
            if transcriber.device == "cuda":
                print(f"🚀 GPU acceleration is working!")
                print(f"⚡ Expected ~10x speed improvement over CPU")
            else:
                print(f"🖥️  Running on CPU (still works, but slower)")
                
            return True
        else:
            print("❌ Transcription returned None")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_device_selection():
    """Test device selection logic"""
    print("\n🔍 Testing Device Selection Logic")
    print("=" * 50)
    
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name()}")
            device_props = torch.cuda.get_device_properties(0)
            print(f"Compute capability: sm_{device_props.major}{device_props.minor}")
            print(f"Memory: {device_props.total_memory / 1024**3:.1f} GB")
            
            # Test GPU operations
            print("🧪 Testing GPU operations...")
            x = torch.randn(1000, device='cuda')
            y = x + 1
            print(f"✅ Basic GPU operations working")
            
            del x, y
            torch.cuda.empty_cache()
        
    except Exception as e:
        print(f"❌ Device test failed: {e}")

if __name__ == "__main__":
    print("🎯 RTX 5090 GPU Transcription Test")
    print("=" * 60)
    
    # Test device selection first
    test_device_selection()
    
    # Test transcription
    success = test_gpu_transcription()
    
    if success:
        print(f"\n🎉 SUCCESS! GPU transcription is ready!")
        print(f"✅ Your RTX 5090 can now accelerate Vid2Frames transcription")
        print(f"⚡ Expected performance: ~30 seconds instead of 2-3 minutes")
    else:
        print(f"\n❌ GPU transcription test failed")
        print(f"🔄 Will fallback to CPU mode automatically")
        
    print(f"\n🚀 Ready to transcribe videos with GPU acceleration!")