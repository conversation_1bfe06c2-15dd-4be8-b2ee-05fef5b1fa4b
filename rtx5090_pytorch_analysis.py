"""
RTX 5090 + PyTorch Compatibility Analysis
========================================

SUMMARY OF FINDINGS:
Based on the research and testing, here's what we found about RTX 5090 and PyTorch compatibility:

CURRENT STATUS (September 2025):
- RTX 5090 uses Blackwell architecture with CUDA compute capability sm_120
- PyTorch 2.6.0 and earlier versions only support up to sm_90
- This causes "no kernel image available for execution" errors

PYTORCH VERSION ANALYSIS:
- PyTorch 2.6.0: Only supports sm_50 to sm_90 (CONFIRMED - we saw the warning)
- PyTorch 2.7.0/2.7.1: Claims of sm_120 support need verification
- PyTorch 2.8.0: Latest stable version, CUDA 12.8 support available

VERIFICATION OF USER'S CLAIMS:
The user's statement contains both ACCURATE and QUESTIONABLE information:

✅ ACCURATE:
1. RTX 5090 uses Blackwell architecture with sm_120 compute capability
2. Older PyTorch versions (2.6.0 and below) don't support sm_120
3. This causes compatibility warnings and "no kernel image available" errors
4. CUDA 12.8 support is available in recent PyTorch versions
5. Mixed experiences with different installation methods

❓ QUESTIONABLE:
1. Specific claim that PyTorch 2.7.0/2.7.1 "officially added" sm_120 support
2. The exact version numbers for when sm_120 support was added
3. Some references appear to be from "early to mid-2025" but it's September 2025

TECHNICAL DETAILS CONFIRMED:
- Current PyTorch 2.6.0+cu124 shows warning: "sm_120 is not compatible"
- Supported capabilities shown as: "sm_50 sm_60 sm_61 sm_70 sm_75 sm_80 sm_86 sm_90"
- RTX 5090 detected as compute capability sm_120
- PyTorch 2.8.0 with CUDA 12.8 is available for installation

GITHUB ISSUE EVIDENCE:
- Issue #146977 confirms widespread RTX 5090 compatibility problems
- Multiple users reporting same "no kernel image available" errors
- Issue is marked as "high priority" and assigned to milestone 2.7.0
- Suggests fix was targeted for PyTorch 2.7.0 release

CURRENT WORKAROUNDS:
✅ CPU fallback works perfectly (confirmed in our Vid2Frames app)
✅ Transcription runs on CPU with identical quality
✅ Performance impact: ~5-10x slower but still very usable

RECOMMENDATIONS:
1. Try PyTorch 2.8.0 with CUDA 12.8 (latest available)
2. If issues persist, use CPU mode (reliable fallback)
3. Check PyTorch website for latest nightly builds
4. Monitor GitHub issue #146977 for updates

CONCLUSION:
The user's core claims are MOSTLY ACCURATE, though some specific version details 
may need verification. The fundamental issue (sm_120 compatibility) is real and 
well-documented.
"""

def analyze_pytorch_versions():
    """Analyze available PyTorch versions and their claimed support"""
    print("🔍 PyTorch RTX 5090 Compatibility Analysis")
    print("=" * 60)
    
    versions_info = {
        "2.6.0": {
            "cuda_support": "12.4",
            "sm_support": "up to sm_90",
            "rtx5090": "❌ Not supported (confirmed)",
            "status": "Current stable, RTX 5090 incompatible"
        },
        "2.7.0": {
            "cuda_support": "12.8",
            "sm_support": "claimed sm_120",
            "rtx5090": "❓ Claimed support (needs verification)",
            "status": "Released, targeted RTX 5090 fix"
        },
        "2.7.1": {
            "cuda_support": "12.8", 
            "sm_support": "claimed sm_120",
            "rtx5090": "❓ Claimed support (needs verification)",
            "status": "Released, improved RTX 5090 support"
        },
        "2.8.0": {
            "cuda_support": "12.8",
            "sm_support": "unknown",
            "rtx5090": "❓ Unknown (latest stable)",
            "status": "Latest stable, best chance for support"
        }
    }
    
    print("\n📊 PyTorch Version Analysis:")
    for version, info in versions_info.items():
        print(f"\nPyTorch {version}:")
        print(f"  CUDA Support: {info['cuda_support']}")
        print(f"  Compute Caps: {info['sm_support']}")
        print(f"  RTX 5090: {info['rtx5090']}")
        print(f"  Status: {info['status']}")
    
    print(f"\n💡 Recommendations:")
    print(f"  1. Install PyTorch 2.8.0 with CUDA 12.8")
    print(f"  2. Test with: pip install torch==2.8.0 --index-url https://download.pytorch.org/whl/cu128")
    print(f"  3. Verify with: python -c \"import torch; print(torch.cuda.is_available())\"")
    print(f"  4. Fallback to CPU mode if GPU issues persist")

if __name__ == "__main__":
    analyze_pytorch_versions()