#!/usr/bin/env python3
"""
Simple Flet dialog test to verify dialog functionality works
"""

import flet as ft

def main(page: ft.Page):
    page.title = "Dialog Test"
    
    def show_test_dialog(e):
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Test Dialog"),
            content=ft.Text("This is a test dialog to verify Flet dialog functionality."),
            actions=[
                ft.TextButton("Close", on_click=lambda e: close_dialog())
            ]
        )
        
        def close_dialog():
            dialog.open = False
            page.update()
        
        page.dialog = dialog
        dialog.open = True
        page.update()
    
    page.add(
        ft.ElevatedButton("Show Dialog", on_click=show_test_dialog)
    )

if __name__ == "__main__":
    ft.app(target=main)