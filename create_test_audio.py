"""
Create a simple test audio file for testing the audio splitting progress feedback
"""

import numpy as np
import soundfile as sf
from pathlib import Path

def create_test_audio():
    """Create a simple test audio file with speech-like patterns"""
    # Generate 30 seconds of audio at 22050 Hz sample rate
    sample_rate = 22050
    duration = 30  # seconds
    
    # Create time array
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Create a simple speech-like pattern with multiple segments
    audio = np.zeros_like(t)
    
    # Segment 1 (0-6s): Low frequency tone
    segment1 = t <= 6
    audio[segment1] = 0.3 * np.sin(2 * np.pi * 200 * t[segment1]) * np.exp(-t[segment1]/3)
    
    # Segment 2 (6-12s): Mid frequency tone
    segment2 = (t > 6) & (t <= 12)
    audio[segment2] = 0.3 * np.sin(2 * np.pi * 400 * t[segment2]) * np.exp(-(t[segment2]-6)/3)
    
    # Segment 3 (12-18s): Higher frequency tone
    segment3 = (t > 12) & (t <= 18)
    audio[segment3] = 0.3 * np.sin(2 * np.pi * 600 * t[segment3]) * np.exp(-(t[segment3]-12)/3)
    
    # Segment 4 (18-24s): Different pattern
    segment4 = (t > 18) & (t <= 24)
    audio[segment4] = 0.3 * np.sin(2 * np.pi * 300 * t[segment4]) * np.exp(-(t[segment4]-18)/3)
    
    # Segment 5 (24-30s): Final pattern
    segment5 = t > 24
    audio[segment5] = 0.3 * np.sin(2 * np.pi * 500 * t[segment5]) * np.exp(-(t[segment5]-24)/3)
    
    # Add some noise for realism
    noise = np.random.normal(0, 0.05, len(audio))
    audio = audio + noise
    
    # Normalize
    audio = audio / np.max(np.abs(audio)) * 0.8
    
    # Save as WAV file
    output_path = Path("test_audio.wav")
    sf.write(output_path, audio, sample_rate)
    
    print(f"✅ Created test audio file: {output_path}")
    print(f"   Duration: {duration} seconds")
    print(f"   Sample rate: {sample_rate} Hz")
    print(f"   Size: {len(audio)} samples")
    
    return output_path

if __name__ == "__main__":
    create_test_audio()