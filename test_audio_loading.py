"""
Quick test to verify audio loading is working
"""
import tempfile
from pathlib import Path
import subprocess

def test_audio_extraction():
    """Test that we can extract and load audio properly"""
    print("🎤 Testing Audio Extraction & Loading")
    print("=" * 50)
    
    # Create a simple test audio with ffmpeg (sine wave)
    temp_audio = Path(tempfile.mktemp(suffix=".wav"))
    
    try:
        # Generate 2 seconds of sine wave audio
        cmd = [
            "ffmpeg", "-y", "-f", "lavfi", "-i", "sine=frequency=440:duration=2",
            "-ar", "16000", "-ac", "1", str(temp_audio)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Failed to generate test audio: {result.stderr}")
            return False
            
        print(f"✅ Generated test audio: {temp_audio}")
        
        # Try to load it with librosa
        try:
            import librosa
            audio_array, sample_rate = librosa.load(temp_audio, sr=16000, mono=True)
            print(f"✅ Loaded audio: {len(audio_array)} samples at {sample_rate}Hz")
            print(f"   Duration: {len(audio_array)/sample_rate:.2f} seconds")
            
            # Try to load with torch
            import torch
            waveform = torch.tensor(audio_array).unsqueeze(0)
            print(f"✅ Converted to torch tensor: {waveform.shape}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load audio: {e}")
            return False
            
    finally:
        # Clean up
        if temp_audio.exists():
            temp_audio.unlink()

if __name__ == "__main__":
    success = test_audio_extraction()
    
    if success:
        print(f"\n🎉 Audio loading system working perfectly!")
        print(f"   Transcription should now work without errors")
    else:
        print(f"\n❌ Audio loading still has issues")
        print(f"   Check that librosa and soundfile are properly installed")