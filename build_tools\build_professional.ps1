# Professional Build Script for Vid2Frames Pro (Windows PowerShell)
# Handles Windows builds and installer creation

param(
    [string]$Version = "1.0.0",
    [switch]$SkipTests,
    [switch]$CreateInstaller = $true,
    [switch]$SignCode = $false,
    [string]$CertPath = "",
    [switch]$Clean = $true
)

# Configuration
$ProductName = "Vid2Frames Pro"
$BuildDir = "build"
$DistDir = "dist"
$InstallerDir = "installer"

# Colors for output
$Colors = @{
    Red = [System.ConsoleColor]::Red
    Green = [System.ConsoleColor]::Green
    Yellow = [System.ConsoleColor]::Yellow
    Blue = [System.ConsoleColor]::Blue
    White = [System.ConsoleColor]::White
}

function Write-Log {
    param($Message, $Color = $Colors.Green)
    Write-Host "[BUILD] $Message" -ForegroundColor $Color
}

function Write-Warning {
    param($Message)
    Write-Host "[WARN] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
    exit 1
}

function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

# Clean previous builds
function Clean-Build {
    Write-Log "Cleaning previous builds..."
    
    if (Test-Path $BuildDir) { Remove-Item $BuildDir -Recurse -Force }
    if (Test-Path $DistDir) { Remove-Item $DistDir -Recurse -Force }
    
    New-Item -ItemType Directory -Path $BuildDir -Force | Out-Null
    New-Item -ItemType Directory -Path $DistDir -Force | Out-Null
    
    Write-Info "Build directories cleaned"
}

# Check dependencies
function Test-Dependencies {
    Write-Log "Checking dependencies..."
    
    # Check Python
    try {
        $pythonVersion = python --version 2>&1
        if ($pythonVersion -match "Python (\d+)\.(\d+)") {
            $major = [int]$matches[1]
            $minor = [int]$matches[2]
            if ($major -lt 3 -or ($major -eq 3 -and $minor -lt 11)) {
                Write-Error "Python 3.11+ required. Found: $pythonVersion"
            }
            Write-Info "Python version: $pythonVersion ✓"
        }
    }
    catch {
        Write-Error "Python not found. Please install Python 3.11+"
    }
    
    # Check if virtual environment exists
    if (-not (Test-Path ".venv")) {
        Write-Log "Creating virtual environment..."
        python -m venv .venv
    }
    
    # Activate virtual environment and install dependencies
    Write-Log "Setting up virtual environment..."
    & ".venv\Scripts\Activate.ps1"
    python -m pip install --upgrade pip
    pip install -r requirements.txt
    pip install pyinstaller auto-py-to-exe
    
    Write-Info "Virtual environment ready"
}

# Run tests
function Invoke-Tests {
    if ($SkipTests) {
        Write-Warning "Skipping tests as requested"
        return
    }
    
    Write-Log "Running tests..."
    
    if (Test-Path "tests") {
        python -m pytest tests/ -v --cov=src --cov-report=html --cov-report=term
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Tests failed"
        }
        Write-Info "All tests passed ✓"
    }
    else {
        Write-Warning "No tests directory found, skipping tests"
    }
}

# Create executable
function Build-Executable {
    Write-Log "Building executable with PyInstaller..."
    
    # Ensure assets directory exists
    if (-not (Test-Path "assets")) { New-Item -ItemType Directory -Path "assets" -Force | Out-Null }
    
    # Create basic icon if not exists
    if (-not (Test-Path "assets\icon.ico")) {
        Write-Warning "No icon found at assets\icon.ico"
        # Create a placeholder file
        New-Item -ItemType File -Path "assets\icon.ico" -Force | Out-Null
    }
    
    # Build with PyInstaller
    pyinstaller vid2frames.spec --clean --noconfirm
    
    if (Test-Path "dist\Vid2Frames.exe") {
        Write-Info "Executable built successfully ✓"
    }
    else {
        Write-Error "Failed to build executable"
    }
}

# Create documentation files
function New-Documentation {
    Write-Log "Creating documentation files..."
    
    # License file
    $licenseContent = @"
Vid2Frames Pro - Commercial License

Copyright (c) 2025 Vid2Frames Solutions. All rights reserved.

This software is licensed, not sold. By installing and using Vid2Frames Pro, 
you agree to the terms and conditions of this license agreement.

GRANT OF LICENSE:
Subject to the terms of this agreement, Vid2Frames Solutions grants you a 
non-exclusive, non-transferable license to use Vid2Frames Pro on a single 
computer system.

RESTRICTIONS:
- You may not distribute, sell, or sublicense this software
- You may not reverse engineer, decompile, or disassemble this software
- You may not remove or modify any copyright notices
- Commercial use requires a separate commercial license

DISCLAIMER:
This software is provided "as is" without warranty of any kind.

For support and licensing inquiries: <EMAIL>
"@
    $licenseContent | Out-File -FilePath "$DistDir\LICENSE.txt" -Encoding UTF8

    # README file
    $readmeContent = @"
Vid2Frames Pro - Professional Video Frame Extraction Tool

Thank you for choosing Vid2Frames Pro!

QUICK START:
1. Double-click Vid2Frames.exe to launch the application
2. Select your video file using the file picker or drag & drop
3. Configure extraction settings (optional)
4. Click "Extract Frames" to begin processing
5. Save your extracted frames to any location

SYSTEM REQUIREMENTS:
- Windows 10 or later (64-bit)
- 4GB RAM minimum (8GB recommended)
- 1GB free disk space
- DirectX 11 compatible graphics card

FEATURES:
✓ Intelligent frame extraction with AI-powered duplicate removal
✓ Scene detection and automatic video splitting  
✓ Multiple video format support (MP4, AVI, MOV, MKV, WebM)
✓ High-quality output in PNG, JPEG, or WebP formats
✓ Real-time processing progress with live previews
✓ Cross-platform compatibility

SUPPORT:
- Documentation: https://docs.vid2frames.com
- Email Support: <EMAIL>  
- Video Tutorials: https://tutorials.vid2frames.com
- FAQ: https://help.vid2frames.com

© 2025 Vid2Frames Solutions. All rights reserved.
"@
    $readmeContent | Out-File -FilePath "$DistDir\README.txt" -Encoding UTF8

    # Changelog
    $changelogContent = @"
Vid2Frames Pro - Changelog

Version $Version ($(Get-Date -Format 'yyyy-MM-dd'))
$(('=' * 50))
🚀 INITIAL RELEASE

NEW FEATURES:
✓ Revolutionary unified scene detection algorithm
✓ Intelligent frame extraction with SSIM similarity detection
✓ Automatic video splitting into individual scene files
✓ Modern Flet-based UI with Material Design
✓ Multi-threaded processing for optimal performance
✓ Support for MP4, AVI, MOV, MKV, WebM formats
✓ Configurable quality and similarity thresholds
✓ Real-time progress tracking with live previews
✓ Local processing ensuring complete privacy
✓ Cross-platform compatibility (Windows, macOS, Linux)

For technical support: <EMAIL>
"@
    $changelogContent | Out-File -FilePath "$DistDir\CHANGELOG.txt" -Encoding UTF8

    Write-Info "Documentation files created ✓"
}

# Code signing function
function Invoke-CodeSigning {
    param($FilePath)
    
    if (-not $SignCode -or -not $CertPath) {
        Write-Warning "Code signing skipped (no certificate specified)"
        return
    }
    
    if (-not (Test-Path $CertPath)) {
        Write-Warning "Certificate not found at: $CertPath"
        return
    }
    
    Write-Log "Signing executable..."
    
    # Use signtool (requires Windows SDK)
    $signTool = Get-Command "signtool.exe" -ErrorAction SilentlyContinue
    if ($signTool) {
        & signtool.exe sign /f $CertPath /t http://timestamp.digicert.com $FilePath
        if ($LASTEXITCODE -eq 0) {
            Write-Info "Code signing completed ✓"
        }
        else {
            Write-Warning "Code signing failed"
        }
    }
    else {
        Write-Warning "signtool.exe not found. Install Windows SDK for code signing."
    }
}

# Create Windows installer
function New-WindowsInstaller {
    if (-not $CreateInstaller) {
        Write-Warning "Installer creation skipped"
        return
    }
    
    Write-Log "Creating Windows installer..."
    
    # Check for NSIS
    $makensis = Get-Command "makensis.exe" -ErrorAction SilentlyContinue
    if (-not $makensis) {
        Write-Warning "NSIS not found. Please install NSIS to create Windows installer"
        Write-Warning "Download from: https://nsis.sourceforge.io/Download"
        return
    }
    
    # Ensure installer directory structure
    if (-not (Test-Path "$InstallerDir\dist")) { 
        New-Item -ItemType Directory -Path "$InstallerDir\dist" -Force | Out-Null 
    }
    
    # Copy distribution files
    Copy-Item "$DistDir\*" "$InstallerDir\dist\" -Recurse -Force
    
    # Download Visual C++ Redistributable if needed
    $redist = "$InstallerDir\redist\VC_redist.x64.exe"
    if (-not (Test-Path $redist)) {
        Write-Log "Downloading Visual C++ Redistributable..."
        New-Item -ItemType Directory -Path "$InstallerDir\redist" -Force | Out-Null
        try {
            Invoke-WebRequest -Uri "https://aka.ms/vs/17/release/vc_redist.x64.exe" -OutFile $redist
            Write-Info "Visual C++ Redistributable downloaded"
        }
        catch {
            Write-Warning "Failed to download Visual C++ Redistributable"
        }
    }
    
    # Create installer with NSIS
    Push-Location $InstallerDir
    try {
        & makensis.exe "vid2frames_installer.nsi"
        if ($LASTEXITCODE -eq 0) {
            $installerFile = "Vid2Frames-Pro-Setup-$Version.exe"
            if (Test-Path $installerFile) {
                Move-Item $installerFile "..\$DistDir\" -Force
                
                # Sign the installer
                Invoke-CodeSigning "..\$DistDir\$installerFile"
                
                Write-Info "Windows installer created: $installerFile ✓"
            }
        }
        else {
            Write-Warning "Failed to create Windows installer"
        }
    }
    finally {
        Pop-Location
    }
}

# Create portable package
function New-PortablePackage {
    Write-Log "Creating portable package..."
    
    Push-Location $DistDir
    try {
        $zipFile = "Vid2Frames-Pro-$Version-Portable.zip"
        Compress-Archive -Path "*" -DestinationPath $zipFile -CompressionLevel Optimal -Force
        Write-Info "Portable package created: $zipFile ✓"
    }
    finally {
        Pop-Location
    }
}

# Generate checksums
function New-Checksums {
    Write-Log "Generating checksums..."
    
    Push-Location $DistDir
    try {
        Get-ChildItem -File | ForEach-Object {
            $hash = Get-FileHash $_.Name -Algorithm SHA256
            "$($hash.Hash)  $($_.Name)"
        } | Out-File "SHA256SUMS.txt" -Encoding UTF8
        
        Write-Info "SHA256 checksums generated ✓"
    }
    finally {
        Pop-Location
    }
}

# Main build function
function Start-Build {
    Write-Info "🚀 Building $ProductName v$Version"
    Write-Host "=" * 50
    
    try {
        if ($Clean) { Clean-Build }
        Test-Dependencies
        Invoke-Tests
        Build-Executable
        
        # Sign the main executable
        if (Test-Path "$DistDir\Vid2Frames.exe") {
            Invoke-CodeSigning "$DistDir\Vid2Frames.exe"
        }
        
        New-Documentation
        New-WindowsInstaller
        New-PortablePackage
        New-Checksums
        
        Write-Host "=" * 50
        Write-Log "✅ Build completed successfully!" $Colors.Green
        Write-Host ""
        Write-Info "📦 Distribution files created in: $DistDir\"
        Write-Info "🔧 Installer files created in: $InstallerDir\"
        Write-Host ""
        Write-Info "Next steps for monetization:"
        Write-Host "  1. Test the installer on clean Windows systems"
        Write-Host "  2. Set up code signing certificate for security"
        Write-Host "  3. Create product website and payment processing"
        Write-Host "  4. Submit to software marketplaces"
        Write-Host "  5. Set up automated build pipeline"
        Write-Host ""
        Write-Warning "⚠️  Remember to:"
        Write-Warning "    - Test on different Windows versions"
        Write-Warning "    - Add proper code signing for distribution"
        Write-Warning "    - Create comprehensive user documentation"
        Write-Warning "    - Set up customer support infrastructure"
    }
    catch {
        Write-Error "Build failed: $_"
    }
}

# Run the build
Start-Build