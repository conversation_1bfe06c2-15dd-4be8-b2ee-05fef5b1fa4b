"""
Demo script for programmatic use of the audio splitting feature
"""
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.audio_splitter import split_audio_file

def demo_audio_splitting():
    """Demo the audio splitting functionality"""
    print("Audio Splitting Demo")
    print("======================")
    
    # Example usage
    demo_text = """
To use this feature programmatically:

1. Create an audio file (e.g., recording.mp3)
2. Create a text file with sentences (e.g., sentences.txt):
   
   Hello, this is the first sentence.
   This is the second sentence.
   Here's the third sentence.

3. Run the splitting:
   
   from src.core.audio_splitter import split_audio_file
   
   success = split_audio_file(
       audio_path=Path("recording.mp3"),
       text_file_path=Path("sentences.txt"),
       output_dir=Path("output_segments/"),
       similarity_threshold=0.8,
       output_format="wav"
   )

4. Find your split audio files in the output directory:
   - segment_001_hello_this_is_the_first.wav
   - segment_002_this_is_the_second.wav  
   - segment_003_heres_the_third.wav
   - alignment_info.json (metadata)
    """
    
    print(demo_text)
    print("\nExample files created:")
    print(f"   Text file: {Path('example_text.txt').absolute()}")
    print("\nTo test with real audio:")
    print("1. Record or obtain an MP3/WAV audio file")
    print("2. Make sure the text in example_text.txt matches the audio content")
    print("3. Use the Audio Split tab in the main application")
    print("4. Or use split_audio_file() function programmatically")
    
    print("\nFeatures:")
    print("- AI-powered transcription with OpenAI Whisper")
    print("- Fuzzy text matching for alignment")  
    print("- Multiple output formats (WAV, MP3, FLAC)")
    print("- GPU acceleration support")
    print("- Detailed progress tracking")
    print("- Error handling and recovery")

if __name__ == "__main__":
    demo_audio_splitting()