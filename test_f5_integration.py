"""
Simple integration test for F5-TTS in Vid2Frames
"""

def test_f5_tts_integration():
    """Test that F5-TTS components can be imported and used"""
    print("🧪 Testing F5-TTS Integration")
    print("=" * 40)
    
    # Test 1: Import core F5-TTS
    try:
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        
        from core.f5_tts import F5TTSProcessor
        print("✅ F5TTSProcessor imported successfully")
    except Exception as e:
        print(f"❌ Failed to import F5TTSProcessor: {e}")
        return False
    
    # Test 2: Import F5-TTS view
    try:
        from ui.f5_tts_view import F5TTSView
        print("✅ F5TTSView imported successfully")
    except Exception as e:
        print(f"❌ Failed to import F5TTSView: {e}")
        return False
    
    # Test 3: Create processor instance
    try:
        processor = F5TTSProcessor()
        print("✅ F5TTSProcessor instance created")
    except Exception as e:
        print(f"❌ Failed to create F5TTSProcessor: {e}")
        return False
    
    # Test 4: Create view instance
    try:
        view = F5TTSView()
        print("✅ F5TTSView instance created")
    except Exception as e:
        print(f"❌ Failed to create F5TTSView: {e}")
        return False
    
    # Test 5: Check available models
    try:
        models = processor.get_available_models()
        print(f"✅ Available models: {models}")
    except Exception as e:
        print(f"❌ Failed to get models: {e}")
        return False
    
    # Test 6: Test parameter validation
    try:
        test_params = {'temperature': 0.9, 'speed': 1.2}
        validated = processor.validate_parameters(test_params)
        print(f"✅ Parameter validation works")
    except Exception as e:
        print(f"❌ Parameter validation failed: {e}")
        return False
    
    print("\n🎉 All F5-TTS integration tests passed!")
    return True

if __name__ == "__main__":
    test_f5_tts_integration()