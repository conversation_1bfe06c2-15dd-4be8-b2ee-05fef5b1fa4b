<!DOCTYPE html>
<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Vid2Frames Results</title>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            colors: {
              primary: "#137fec",
              "background-light": "#f6f7f8",
              "background-dark": "#101922",
            },
            fontFamily: {
              display: ["Inter"],
            },
            borderRadius: {
              DEFAULT: "0.25rem",
              lg: "0.5rem",
              xl: "0.75rem",
              full: "9999px",
            },
          },
        },
      };
    </script>
<style>
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .animate-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
      }
      .result-card:nth-child(1) { animation-delay: 0.1s; }
      .result-card:nth-child(2) { animation-delay: 0.2s; }
      .result-card:nth-child(3) { animation-delay: 0.3s; }
      .result-card:nth-child(4) { animation-delay: 0.4s; }
      .result-card:nth-child(5) { animation-delay: 0.5s; }
      .result-card:nth-child(6) { animation-delay: 0.6s; }
      .result-card:nth-child(7) { animation-delay: 0.7s; }
      .result-card:nth-child(8) { animation-delay: 0.8s; }
      .result-card:nth-child(9) { animation-delay: 0.9s; }
      .result-card:nth-child(10) { animation-delay: 1.0s; }
      .result-card:nth-child(11) { animation-delay: 1.1s; }
      .result-card:nth-child(12) { animation-delay: 1.2s; }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display text-gray-800 dark:text-gray-200">
<div class="flex min-h-screen flex-col">
<header class="sticky top-0 z-20 flex items-center justify-between border-b border-black/10 bg-background-light/80 px-10 py-3 backdrop-blur-sm dark:border-white/10 dark:bg-background-dark/80">
<div class="flex items-center gap-3">
<svg class="h-6 w-6 text-primary" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4H17.3334V17.3334H30.6666V30.6666H44V44H4V4Z" fill="currentColor"></path>
</svg>
<h1 class="text-lg font-bold text-gray-900 dark:text-white">
            Vid2Frames
          </h1>
</div>
<nav class="hidden items-center gap-6 md:flex">
<a class="text-sm font-medium text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary" href="#">Home</a>
<a class="text-sm font-medium text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary" href="#">Pricing</a>
<a class="text-sm font-medium text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary" href="#">Contact</a>
</nav>
<div class="flex items-center gap-4">
<button class="flex items-center justify-center gap-2 rounded-lg bg-primary px-4 py-2 text-sm font-bold text-white shadow-lg shadow-primary/20 transition-all hover:shadow-xl hover:shadow-primary/30 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-background-dark">
<span class="material-symbols-outlined text-base">upload</span>
<span>Upload</span>
</button>
<div class="h-10 w-10">
<img alt="User Avatar" class="h-full w-full rounded-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuCwk0RDTTX36yuJfoGC8BREdsrk1dd683nKRCMhxM_Fj95J2GlaJnHQXv9B6vQJ2rwFgms4E66HEJ1dIWlEmC6Q5gylGDonx7eb_L6oETXow5WD1GiRbRXZbgy3b3x2hw7_A0CB5HJ53eJL4FLdh-JbDPm-8Uzr5Ve7mnbpZu9uB1h8NRcgf36RWwDxn8_Fn8j5fx-iouvDg3gAFTeOPRy2IiMFpnflVSLFWBnSEcjTWXidfTYYD5b8kytIq1fNy5kK77p0DeIZVP8"/>
</div>
</div>
</header>
<main class="flex-grow">
<div class="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
<div class="mb-10 text-center">
<h2 class="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Processing Results
            </h2>
<p class="mt-4 text-lg text-gray-600 dark:text-gray-400">
              Review the extracted frames from your video. Each frame is displayed with its timestamp.
            </p>
</div>
<div class="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBBiV2zYa8_X9wRZNI4rqmBQFWw8sk7arirV_vZ_sFwuajO3_3YKrZ-Vpj7RjsiemWjlSHDjr4DgXWXgQf0QLF7YhqjhJBDpgulFtfofKuQYeKSh2CuQrCcidZe9Mb-eur1rqbrpzbMVEy0KnzyqAHp6yDqo7A0j5MgAhtbxRD_1-6xh0LBqHTOkbBd1449CI90FkigqdCeoBoptWow9CYVdc5DFL33FiKxDLlbEmNFd1TejjlGfYpfmTydaNqpKlsn2eEfj9ML9xo"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:01.234</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAILg84GOjWhjdfSfInU7xAZYBkozIoHF2zPPX0qjtRGaYvpSIWEwZLNAjGISn63WrZqb2XR3uTAw42mDyNUOM6-T82SFbbyol_pmth3Jkj4H_FhYV5ZtxUrsT9PPcfK0ZxR_KTZhe9ZtGf9v9l2cO9owPaR1Xl03f4v_OEtsZoUkJICgWySYFRdcMaR1Zh4ye08igW-x5m1tywxHpHRK6gzaGVDbpL5SvKaBxw4M-DfAfWiDBklE9kVEWUUc1b2vF2Y1NpT4ZpU20"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:02.468</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDYt7Fo1ZUbYw5OvTHSTc6-0gcqHkTlVh5TttBOcPWM9kxqqMAmBBA9kVuGAyTQ0cNSnYByFmkfz0eO4dKUCjp1Jnx6AjPdvrqb5BSQ-lY0clCoITcXGf0TT-Va2NDmO-Dy28XB_8kforqpVLjQnslNzDCTHpo8kM0hptt2zmMG8mv636tTijY3rd17FzEGFFb4qfGTpMTMqLF-OFEWYxuNdGGkjckWIlDMt6Q8ywCRg6wWQbMIbS6pL0BcevmGHAmjCo8CMBXlNrs"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:03.702</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAOVqcSrNkarcBig0ke59da29Th35f-2QVO87SPtfsNw8KaYOyj6-LxmOqAKu4_F-0DzCqLDbBmzVnyTqAvltfeDPiUqYkizesT53eJz2M6mHqubbXZ7yb2Ne7V12jdpF65UwMBx3Xh7zn0osGzeORWZ8IN5X7sw71TA-nWqNe1Z53gBk2qGwU73tiSGSEIUNlDAhDTlwquO4iyN63WsEmS5vvGQxmgT1MmTM-I-5SRGXRqCoFcY-qgS1bdK3XEnLQ1S-IQTczqSBc"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:04.936</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBv_H6gPnBRaQrHiTEZw0DgGyjy5sY9ZeoFxHf181IMXMOnMthl7NfZpQiUBTZP8jhYuyqISUQenOMvMInGxzkJgjnvceqane7pLxlhYQupCOIthUMHzfhM4ZXu1k_c2Hop50B3iYBySZKgDm6SdTqAZ5fhg5ZLaGE1oTLXXONDm_KkYWt1VCIMUYtDooSJnhKMNn4zUfOcsZbKK3hXuFd-ih054fL3oVofoTIW5vx7-EVU6ChI4OYnTJo-dz8Mjzuj_ofw3ruikCs"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:06.170</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC8Za8lmqddpNj-uL8r_W4tND0unu5O4QW455ldW9we84_l-oZfjBO_mOgFe2lMJM_YOUAtsfYrAZgNK99TxI9Hs0CMiUcY8WI9CjGVOKC_PsfOQD8Gtrm6Omr9195g-ZT2VLv3hj8AVlCuaukdLyq9l_b3W1HOsrq0MS0VDdwTfzaF1Jr2u19m5O-as9g9D49Yy11gGVqzN-1OCT3iuG_loLOfPxb5isjirV32GUGXVXwmqeF_3HLTTkSmSOLshsetrIq7Jipi1Mk"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:07.404</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuAiGC3oWwH9V1lzvCzZviqOpdr9Bpzc_jQcuStLFNIxaeQlxcL3-xCfZRn4FlX6DnwKbbQTm50mfMRGDSq1S1JFsDlNgEstj2dY4yATZeA6uojjmRxXCOULahfHO5ZKkkjkFTqSD1qkqxIQxp_-VHPUgXjo-NW-wEJ62do-1i-FpNKiSaXE8uXqAYxODUfpb3yZu7k2lo76xBI9XNgdTt6wRg2SZ8InYB-xGFhC4r0nC3lXp1gLwu89yotOSNBIEJKBJjPSfsgjaLk"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:08.638</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDXyftSWc9AV8C065N7jO85to8ULIhmsFB9Zg9SSa34TKTY9zKXoR6a-K4NOEsJwuCPs2EVOlyKQLWR2_nWqV0KMk-pvFjhGpwqXLYculqBJN8JXb_BT3D_UBzD0AvndQb84Z7JAwUIVtT-xxBvkOGnvuPfJ4ImhfTNxD_aUqmXjsNYw3jcORoXKeS3iXCirArv0pH9HQOQl2rFszWOpuyNi_ITseHLMu5zcwoIF2wBQSZckuM2l-QPP_XFnBNc7qcgzzDEjb9-LP4"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:09.872</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuD5VDz5EWc8LsR720HSs8xwuQwP_bMWASFhUbJLCluNvW33a-DpkFVLjapM74wHvO9JKYnJcHazdOMD5GqNkdwtpuZmPc3dUiz9o9mjcbtb15Wjj6Vil-bHtnaGxZlE9vZpcCYJUHcfEDR0sNPs3Wv_0EQRRov4ATJwb-nlvYOh5Xod_jbPoKeLCf7Or-UnTzvH9R75LIfjpE3xNI6wsVRmYL8swN7MAPotDsPBVl-UqIBS9CKablcLcYRpYcdbZMbTV1CXbusTMvA"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:11.106</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuBE49k2i6DtP1YZdcIZXmTgKyhOnagTWbQ_IRd7KXkKOg-KZUyJlgR3bs77XUNzfvj_7Z9gIMhDUn1ZoB3F1qcMCrDRSopthPly3wz9vTIQuZukJpYyfWLoAI4vGtsih4aRse1P0EkCu6PbdsnDQqhzFhF-ny4mum7CUm091i8cJWX49uC5Iq4SWwMeFrWLBaO4qFMEm4gHFgW-TJQpCGGoJu22cKr3Ev-WL_WDYo3YIKDS07coVbb5PYuMWPt5KbyGz5UXet_mjw0"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:12.340</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuCILVYYZASKFEh8D9mvlyuwBNb5NmUZCcr6ABqirwSIYzi0E_wRDZNzEP_ja3zMJK9o63rwYqdHaTmi3jMoMzYfYMl-c-qNRuNEXh5cQUmQK_FXnywBb2ElsLN05cO3QsaSTxHYmqZEssoO5PlO_mB_FWwea_Otioz93FilmQ0N59cWEg1Xk9WVgmOhLWCN3izDOCQ_-SsUR0J5Z-HYNDWslf0g3CvZ60S6RMXQqYTOENhUW8kY4g_HBVCuWK4VAnUuQntin7kqcZI"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:13.574</p>
</div>
</div>
</div>
<div class="group result-card animate-fade-in opacity-0">
<div class="relative overflow-hidden rounded-xl shadow-lg transition-all duration-300 ease-in-out group-hover:scale-105 group-hover:shadow-2xl">
<div class="absolute inset-0 bg-primary/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100 dark:bg-primary/30"></div>
<img alt="Video Frame" class="aspect-video w-full object-cover" src="https://lh3.googleusercontent.com/aida-public/AB6AXuDtMwddHcs73J6Cs8nJa93mRU3LMU2LqxOHBaDRvl9ncuEjbkfQw4yHrMi3Wz3lrx3zK_zDN1YLAzXfSr8RaykdrGljNh7nh0MJS8vGl5wTxmQkMQu9WGfB4CA2JjYrQ7E-h5Jq_d8PY1KXzRGlbpBM98wq8RRnYbHKzToReXh81-hOzVMrYi1hQynoO8ZNSGQwfaBqQi_67-yt78csnG7rcnFhGLyvVOYdMVUvq6IR1jI0yvEmbftB5C-hYjaW8Edl_5mCw9I8G3Y"/>
<div class="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300 group-hover:opacity-100">
<button class="rounded-full bg-white/80 p-2 text-gray-800 shadow-md backdrop-blur-sm hover:bg-white dark:bg-background-dark/80 dark:text-white dark:hover:bg-background-dark"><span class="material-symbols-outlined">download</span></button>
</div>
<div class="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/60 to-transparent p-2">
<p class="text-xs font-medium text-white">00:00:14.808</p>
</div>
</div>
</div>
</div>
</div>
</main>
<footer class="border-t border-black/10 bg-background-light dark:border-white/10 dark:bg-background-dark">
<div class="mx-auto max-w-7xl px-6 py-8 lg:px-8">
<nav class="flex flex-wrap justify-center gap-x-6 gap-y-4">
<a class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary" href="#">Terms of Service</a>
<a class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary" href="#">Privacy Policy</a>
<a class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary" href="#">Contact Us</a>
</nav>
<p class="mt-8 text-center text-sm text-gray-500 dark:text-gray-400">
            © 2024 Vid2Frames. All rights reserved.
          </p>
</div>
</footer>
</div>

</body></html>