#!/usr/bin/env python3

"""
Test script to verify that F5-TTS uses correct ComfyUI reference text
and doesn't append "this is a sample" to generated audio
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.append('src')

from core.f5_tts import F5TTSProcessor
import tempfile
import datetime

def test_reference_text_loading():
    """Test that ComfyUI reference text is loaded correctly"""
    print("🧪 Testing reference text loading...")
    
    # Initialize F5-TTS processor
    f5_processor = F5TTSProcessor()
    
    # Load the model
    print("📦 Loading F5-TTS model...")
    f5_processor.load_model("E2TTS_Base")
    
    # Get available ComfyUI voices
    print("🎤 Getting ComfyUI voices...")
    voices = f5_processor.get_available_comfy_voices()
    
    if not voices:
        print("❌ No ComfyUI voices found!")
        return False
    
    print(f"📝 Found {len(voices)} voices:")
    for i, voice in enumerate(voices):
        print(f"  {i+1}. {voice['name']}")
        if 'reference_text' in voice:
            ref_text = voice['reference_text'][:100] + '...' if len(voice['reference_text']) > 100 else voice['reference_text']
            print(f"     Reference: '{ref_text}'")
        else:
            print(f"     Reference: NOT FOUND")
    
    # Test with Clint Eastwood voice (should have reference text)
    clint_voice = None
    for voice in voices:
        if "clint" in voice['name'].lower():
            clint_voice = voice
            break
    
    if not clint_voice:
        print("⚠️ <PERSON> voice not found, using first available voice")
        clint_voice = voices[0]
    
    print(f"\n🎭 Testing with voice: {clint_voice['name']}")
    print(f"🎭 Reference text: '{clint_voice.get('reference_text', 'NOT FOUND')}'")
    
    # Set reference audio with proper text
    voice_path = Path(clint_voice['path'])
    f5_processor.set_reference_audio(voice_path)
    
    print(f"✅ Reference audio set to: {voice_path.name}")
    print(f"✅ Internal reference text: '{f5_processor.reference_text}'")
    
    return True

def test_audio_generation():
    """Test audio generation to ensure no 'this is a sample' text"""
    print("\n🧪 Testing audio generation...")
    
    # Initialize F5-TTS processor
    f5_processor = F5TTSProcessor()
    
    # Load the model
    f5_processor.load_model("E2TTS_Base")
    
    # Get ComfyUI voices
    voices = f5_processor.get_available_comfy_voices()
    if not voices:
        print("❌ No voices available for testing")
        return False
    
    # Use first voice with reference text
    test_voice = voices[0]
    voice_path = Path(test_voice['path'])
    f5_processor.set_reference_audio(voice_path)
    
    # Test sentences
    test_sentences = [
        "Hello world, this is a test of voice cloning.",
        "The quick brown fox jumps over the lazy dog.",
        "Testing one two three."
    ]
    
    # Create temporary output directory
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path.cwd() / f"test_output_{timestamp}"
    
    print(f"🎵 Generating test audio in: {output_dir}")
    
    # Generate speech
    results = f5_processor.process_sentences(
        test_sentences, 
        output_dir,
        progress_callback=lambda msg, progress: print(f"  {msg} ({progress:.1%})")
    )
    
    print(f"\n📊 Results:")
    print(f"  ✅ Success: {results['success_count']}")
    print(f"  ❌ Failed: {results['failed_count']}")
    print(f"  📁 Output files: {len(results['output_files'])}")
    
    # List generated files
    if results['output_files']:
        print(f"\n📂 Generated files:")
        for file_path in results['output_files']:
            file_size = file_path.stat().st_size
            print(f"  - {file_path.name} ({file_size:,} bytes)")
    
    return len(results['output_files']) > 0

def main():
    print("🧪 F5-TTS Reference Text Fix Test")
    print("=" * 50)
    
    try:
        # Test reference text loading
        if not test_reference_text_loading():
            print("❌ Reference text loading test failed")
            return
        
        # Test audio generation
        if not test_audio_generation():
            print("❌ Audio generation test failed")
            return
        
        print("\n✅ All tests passed!")
        print("🎉 F5-TTS should now use proper ComfyUI reference text")
        print("🎉 No more 'this is a sample' appended to your text!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()