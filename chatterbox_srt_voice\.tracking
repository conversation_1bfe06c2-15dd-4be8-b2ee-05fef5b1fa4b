.github/workflows/publish_action.yml
.gitignore
CHANGELOG.md
LICENSE
PROJECT_INDEX.md
README.md
__init__.py
docs/BUMP_SCRIPT_INSTRUCTIONS.md
docs/CHARACTER_SWITCHING_GUIDE.md
docs/Dev reports/CLAUDE_VERSION_MANAGEMENT_GUIDE.md
docs/Dev reports/COMFYUI_MESSAGING_GUIDE.md
docs/Dev reports/F5TTS_EDIT_IMPROVEMENTS_PLAN.md
docs/Dev reports/F5TTS_IMPLEMENTATION_COMPLETE_REPORT.md
docs/Dev reports/F5TTS_IMPLEMENTATION_SUMMARY.md
docs/Dev reports/F5TTS_INTEGRATION_SPECIFICATION.md
docs/Dev reports/PARAMETER_MIGRATION_CHECKLIST.md
docs/Dev reports/SRT_IMPLEMENTATION.md
docs/Dev reports/SRT_IMPLEMENTATION_OLD.md
docs/Dev reports/VERSION_UPDATE_GUIDE.md
docs/Dev reports/f5tts_integration_guide.py
docs/Dev reports/refactoring summary/PHASE2_MIGRATION_SUMMARY.md
docs/Dev reports/refactoring summary/PHASE3_COMPLETION_SUMMARY.md
docs/Dev reports/refactoring summary/Refactoring Successfully Completed.md
docs/MULTILANGUAGE_MODULARIZATION_PLAN.md
docs/PAUSE_TAGS_IMPLEMENTATION_REPORT.md
docs/VERSION_3.1_RELEASE_GUIDE.md
docs/test_cases.txt
engines/__init__.py
engines/adapters/__init__.py
engines/adapters/chatterbox_adapter.py
engines/adapters/f5tts_adapter.py
engines/chatterbox/__init__.py
engines/chatterbox/audio_timing.py
engines/chatterbox/language_models.py
engines/chatterbox/models/s3gen/__init__.py
engines/chatterbox/models/s3gen/const.py
engines/chatterbox/models/s3gen/decoder.py
engines/chatterbox/models/s3gen/f0_predictor.py
engines/chatterbox/models/s3gen/flow.py
engines/chatterbox/models/s3gen/flow_matching.py
engines/chatterbox/models/s3gen/hifigan.py
engines/chatterbox/models/s3gen/matcha/decoder.py
engines/chatterbox/models/s3gen/matcha/flow_matching.py
engines/chatterbox/models/s3gen/matcha/text_encoder.py
engines/chatterbox/models/s3gen/matcha/transformer.py
engines/chatterbox/models/s3gen/s3gen.py
engines/chatterbox/models/s3gen/transformer/__init__.py
engines/chatterbox/models/s3gen/transformer/activation.py
engines/chatterbox/models/s3gen/transformer/attention.py
engines/chatterbox/models/s3gen/transformer/convolution.py
engines/chatterbox/models/s3gen/transformer/embedding.py
engines/chatterbox/models/s3gen/transformer/encoder_layer.py
engines/chatterbox/models/s3gen/transformer/positionwise_feed_forward.py
engines/chatterbox/models/s3gen/transformer/subsampling.py
engines/chatterbox/models/s3gen/transformer/upsample_encoder.py
engines/chatterbox/models/s3gen/utils/class_utils.py
engines/chatterbox/models/s3gen/utils/mask.py
engines/chatterbox/models/s3gen/utils/mel.py
engines/chatterbox/models/s3gen/xvector.py
engines/chatterbox/models/s3tokenizer/__init__.py
engines/chatterbox/models/s3tokenizer/s3tokenizer.py
engines/chatterbox/models/t3/__init__.py
engines/chatterbox/models/t3/inference/alignment_stream_analyzer.py
engines/chatterbox/models/t3/inference/t3_hf_backend.py
engines/chatterbox/models/t3/llama_configs.py
engines/chatterbox/models/t3/modules/cond_enc.py
engines/chatterbox/models/t3/modules/learned_pos_emb.py
engines/chatterbox/models/t3/modules/perceiver.py
engines/chatterbox/models/t3/modules/t3_config.py
engines/chatterbox/models/t3/t3.py
engines/chatterbox/models/tokenizers/__init__.py
engines/chatterbox/models/tokenizers/tokenizer.py
engines/chatterbox/models/voice_encoder/__init__.py
engines/chatterbox/models/voice_encoder/config.py
engines/chatterbox/models/voice_encoder/melspec.py
engines/chatterbox/models/voice_encoder/voice_encoder.py
engines/chatterbox/tts.py
engines/chatterbox/vc.py
engines/f5tts/__init__.py
engines/f5tts/audio_compositing.py
engines/f5tts/f5tts.py
engines/f5tts/f5tts_edit_engine.py
example_workflows/Chatterbox integration.jpg
example_workflows/Chatterbox integration.json
images/AllNodesShowcase.png
images/srt.png
images/waveanalgif.gif
nodes.py
nodes/__init__.py
nodes/audio/__init__.py
nodes/audio/analyzer_node.py
nodes/audio/analyzer_options_node.py
nodes/audio/recorder_node.py
nodes/base/__init__.py
nodes/base/base_node.py
nodes/base/f5tts_base_node.py
nodes/chatterbox/__init__.py
nodes/chatterbox/chatterbox_srt_node.py
nodes/chatterbox/chatterbox_tts_node.py
nodes/chatterbox/chatterbox_vc_node.py
nodes/f5tts/__init__.py
nodes/f5tts/f5tts_edit_node.py
nodes/f5tts/f5tts_edit_options_node.py
nodes/f5tts/f5tts_node.py
nodes/f5tts/f5tts_srt_node.py
pyproject.toml
requirements.txt
scripts/bump_version_enhanced.py
scripts/version_utils.py
utils/__init__.py
utils/audio/__init__.py
utils/audio/analysis.py
utils/audio/cache.py
utils/audio/processing.py
utils/models/__init__.py
utils/models/f5tts_manager.py
utils/models/language_mapper.py
utils/models/manager.py
utils/system/__init__.py
utils/system/import_manager.py
utils/system/subprocess.py
utils/text/__init__.py
utils/text/character_parser.py
utils/text/chunking.py
utils/text/pause_processor.py
utils/timing/__init__.py
utils/timing/assembly.py
utils/timing/engine.py
utils/timing/parser.py
utils/timing/reporting.py
utils/voice/__init__.py
utils/voice/discovery.py
utils/voice/multilingual_engine.py
voices_examples/#character_alias_map.txt
voices_examples/Clint_Eastwood CC3 (enhanced2).reference.txt
voices_examples/Clint_Eastwood CC3 (enhanced2).wav
voices_examples/Clint_Eastwood CC3.txt
voices_examples/David_Attenborough CC3.reference.txt
voices_examples/David_Attenborough CC3.txt
voices_examples/David_Attenborough CC3.wav
voices_examples/Morgan Freeman CC3.txt
voices_examples/Morgan_Freeman CC3.reference.txt
voices_examples/Morgan_Freeman CC3.wav
voices_examples/Sophie_Anderson CC3.reference.txt
voices_examples/Sophie_Anderson CC3.txt
voices_examples/Sophie_Anderson CC3.wav
voices_examples/crestfallen_original.mp3
voices_examples/female/female_01.reference.txt
voices_examples/female/female_01.wav
voices_examples/female/female_02.reference.txt
voices_examples/female/female_02.wav
voices_examples/male/male_01.reference.txt
voices_examples/male/male_01.wav
voices_examples/male/male_02.reference.txt
voices_examples/male/male_02.wav
web/audio_analyzer.css
web/audio_analyzer_controls.js
web/audio_analyzer_core.js
web/audio_analyzer_drawing.js
web/audio_analyzer_events.js
web/audio_analyzer_interface.js
web/audio_analyzer_layout.js
web/audio_analyzer_node_integration.js
web/audio_analyzer_regions.js
web/audio_analyzer_ui.js
web/audio_analyzer_visualization.js
web/audio_analyzer_widgets.js
web/chatterbox_srt_showcontrol.js
web/chatterbox_voice_capture.js