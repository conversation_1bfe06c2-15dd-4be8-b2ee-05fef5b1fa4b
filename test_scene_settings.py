#!/usr/bin/env python3
"""
Quick test to verify scene splitting setting is working
"""

def test_scene_splitting_default():
    """Test that scene splitting setting defaults to True"""
    print("🧪 Testing Scene Splitting Settings")
    print("=" * 40)
    
    # Mock the settings dictionary
    default_settings = {
        'similarity_threshold': 0.85,
        'quality_threshold': 0.7,
        'output_format': 'PNG',
        'max_frames': None,
        'resize_width': None,
        'scene_detection': True,
        'split_scenes': True,  # This should be True now
        'min_scene_duration': 1.0,
        'max_worker_threads': 4,
        'ui_theme': 'system'
    }
    
    # Check settings
    print(f"Default split_scenes setting: {default_settings['split_scenes']}")
    print(f"Default scene_detection setting: {default_settings['scene_detection']}")
    print(f"Default min_scene_duration setting: {default_settings['min_scene_duration']}")
    
    # Verify expected defaults
    if default_settings['split_scenes']:
        print("✅ Scene splitting is enabled by default")
    else:
        print("❌ Scene splitting is disabled by default")
    
    if default_settings['scene_detection']:
        print("✅ Scene detection is enabled by default")
    else:
        print("❌ Scene detection is disabled by default")
    
    print(f"✅ Minimum scene duration: {default_settings['min_scene_duration']}s")
    
    print("\n🎯 Scene-Related Settings Summary:")
    for key, value in default_settings.items():
        if 'scene' in key or 'split' in key:
            print(f"  {key}: {value}")

if __name__ == "__main__":
    print("🚀 Testing Scene Splitting Configuration")
    print("="*50)
    
    test_scene_splitting_default()
    
    print("\n🎉 Settings test complete!")
    print("The scene splitting should now be enabled by default when processing videos.")
    print("\nNext step: Try processing a video - you should see:")
    print("  Scene splitting enabled: True")
    print("  Scene boundary detected at X.Xs (frame Y)")  
    print("  Created N scenes from M boundaries")
    print("  Created N scene video files")