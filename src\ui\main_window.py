import flet as ft
from pathlib import Path

from .upload_view import UploadView
from .universal_progress_view import UniversalProgressView
from .results_view import ResultsView
from .settings_view import SettingsView
from .audio_split_view import AudioSplitView
from .f5_tts_view import F5TTSView


class MainWindow:
    """Main application window with navigation"""

    def __init__(self):
        self.page = None  # Will be set by main.py
        self.current_view = "upload"
        self.processing_result = None

        # Title bar refs no longer needed - using system title bar
        # self._title_container = None
        # self._title_text = None
        # self._btn_min = None
        # self._btn_max = None
        # self._btn_close = None

        # Initialize views
        self.upload_view = UploadView(on_video_selected=self.start_video_processing)
        self.progress_view = UniversalProgressView(on_processing_complete=self.on_processing_complete)
        self.results_view = ResultsView(main_window=self)
        self.settings_view = SettingsView(main_window=self)
        self.audio_split_view = AudioSplitView()
        self.f5_tts_view = F5TTSView()

    def set_page(self, page):
        """Set page reference for this window and all child views"""
        self.page = page
        # Pass page reference to all child views
        self.upload_view.page = page
        self.progress_view.page = page
        self.results_view.page = page
        self.settings_view.page = page
        self.audio_split_view.set_page(page)  # Use set_page method for audio split view
        self.f5_tts_view.page = page

    def get_theme_colors(self):
        """Get colors that adapt to current theme"""
        if self.page and self.page.theme_mode == ft.ThemeMode.DARK:
            return {
                'text_secondary': ft.Colors.GREY_300,
                'text_disabled': ft.Colors.GREY_500,
                'surface_variant': ft.Colors.GREY_800,
                'outline': ft.Colors.GREY_600,
                'icon_secondary': ft.Colors.GREY_400,
            }
        else:
            return {
                'text_secondary': ft.Colors.GREY_600,
                'text_disabled': ft.Colors.GREY_400,
                'surface_variant': ft.Colors.GREY_100,
                'outline': ft.Colors.GREY_300,
                'icon_secondary': ft.Colors.GREY_400,
            }

    def build(self):
        # Navigation rail
        self.nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=200,
            destinations=[
                ft.NavigationRailDestination(
                    icon="cloud_upload_outlined",
                    selected_icon="cloud_upload",
                    label="Upload",
                ),
                ft.NavigationRailDestination(
                    icon="content_cut_outlined",
                    selected_icon="content_cut",
                    label="Audio Split",
                ),
                ft.NavigationRailDestination(
                    icon="record_voice_over_outlined",
                    selected_icon="record_voice_over",
                    label="F5-TTS",
                ),
                ft.NavigationRailDestination(
                    icon="hourglass_empty_outlined",
                    selected_icon="hourglass_empty",
                    label="Progress",
                ),
                ft.NavigationRailDestination(
                    icon="grid_view_outlined",
                    selected_icon="grid_view",
                    label="Results",
                ),
                ft.NavigationRailDestination(
                    icon="settings_outlined",
                    selected_icon="settings",
                    label="Settings",
                ),
            ],
            on_change=self.on_nav_change,
        )

        # Content area
        self.content_area = ft.Container(
            content=self.upload_view.build(),
            expand=True,
            padding=0,
        )

        # Window control handlers
        def _minimize(e=None):
            if self.page and hasattr(self.page, 'window'):
                try:
                    self.page.window.minimize()
                except Exception as ex:
                    print(f"Minimize failed: {ex}")

        def _toggle_max(e=None):
            if self.page and hasattr(self.page, 'window'):
                try:
                    if hasattr(self.page.window, 'toggle_maximized'):
                        self.page.window.toggle_maximized()
                    elif hasattr(self.page.window, 'maximized'):
                        if self.page.window.maximized:
                            self.page.window.unmaximize()
                        else:
                            self.page.window.maximize()
                    else:
                        print("Maximize toggle not supported")
                except Exception as ex:
                    print(f"Maximize toggle failed: {ex}")

        def _close(e=None):
            if self.page and hasattr(self.page, 'window'):
                try:
                    self.page.window.close()
                except Exception as ex:
                    print(f"Close failed: {ex}")

        # Use system title bar instead of custom one for proper window controls
        # Themed custom title bar code commented out
        # self._title_icon = ft.Icon(ft.Icons.MOVIE_CREATION_OUTLINED, color=title_fg, size=18)
        # self._title_text = ft.Text("Vid2Frames", color=title_fg, weight=ft.FontWeight.W_600)
        # self._btn_min = ft.IconButton(icon=ft.Icons.MINIMIZE, icon_color=title_fg, tooltip="Minimize", on_click=_minimize)
        # self._btn_max = ft.IconButton(icon=ft.Icons.CROP_SQUARE, icon_color=title_fg, tooltip="Maximize/Restore", on_click=_toggle_max)
        # self._btn_close = ft.IconButton(icon=ft.Icons.CLOSE, icon_color=title_fg, tooltip="Close", on_click=_close)

        # self._title_container = ft.Container(
        #     content=ft.Row([
        #         ft.WindowDragArea(
        #             ft.Row([
        #                 self._title_icon,
        #                 self._title_text,
        #             ], spacing=8, vertical_alignment=ft.CrossAxisAlignment.CENTER),
        #             expand=True,
        #         ),
        #         ft.Row([
        #             self._btn_min,
        #             self._btn_max,
        #             self._btn_close,
        #         ], spacing=0),
        #     ], vertical_alignment=ft.CrossAxisAlignment.CENTER),
        #     bgcolor=title_bg,
        #     padding=ft.padding.symmetric(horizontal=10, vertical=6),
        #     border=ft.border.only(bottom=ft.border.BorderSide(1, title_border)),
        # )

        # Main layout without custom title bar
        main_row = ft.Row(
            [
                self.nav_rail,
                ft.VerticalDivider(width=1),
                self.content_area,
            ],
            expand=True,
        )

        return ft.Column([
            # self._title_container,  # Commented out
            main_row,
        ], expand=True)

    def on_nav_change(self, e):
        """Handle navigation rail selection"""
        selected_index = e.control.selected_index

        # Only prevent navigation to results if no processing result exists
        if selected_index == 4 and not self.processing_result:  # Results tab (now index 4)
            return

        if selected_index == 0:
            self.show_upload_view()
        elif selected_index == 1:
            self.show_audio_split_view()
        elif selected_index == 2:
            self.show_f5_tts_view()
        elif selected_index == 3:
            self.show_progress_view()
        elif selected_index == 4:
            self.show_results_view()
        elif selected_index == 5:
            self.show_settings_view()

    def show_upload_view(self):
        """Show the upload view"""
        self.current_view = "upload"
        self.nav_rail.selected_index = 0
        self.content_area.content = self.upload_view.build()
        if self.page:
            self.page.update()

    def show_audio_split_view(self):
        """Show the audio split view"""
        self.current_view = "audio_split"
        self.nav_rail.selected_index = 1
        self.content_area.content = self.audio_split_view.build()
        if self.page:
            self.page.update()

    def show_f5_tts_view(self):
        """Show the F5-TTS view"""
        self.current_view = "f5_tts"
        self.nav_rail.selected_index = 2
        self.content_area.content = self.f5_tts_view.build()
        if self.page:
            self.page.update()

    def show_progress_view(self):
        """Show the progress view"""
        self.current_view = "progress"
        self.nav_rail.selected_index = 3
        self.content_area.content = self.progress_view.build()
        if self.page:
            self.page.update()

    def show_results_view(self):
        """Show the results view"""
        self.current_view = "results"
        self.nav_rail.selected_index = 4
        self.content_area.content = self.results_view.build()
        
        # Reset processing flags in views that might be stuck
        if hasattr(self, 'f5_tts_view'):
            self.f5_tts_view.is_processing = False
            
        if self.processing_result:
            self.results_view.set_results(self.processing_result)
        if self.page:
            self.page.update()

    def show_settings_view(self):
        """Show the settings view"""
        self.current_view = "settings"
        self.nav_rail.selected_index = 5
        self.content_area.content = self.settings_view.build()
        if self.page:
            self.page.update()

    def start_video_processing(self, video_path: Path):
        """Start video processing using universal progress view"""
        self.show_progress_view()

        # Set page reference for views
        if self.page:
            self.upload_view.page = self.page
            self.progress_view.page = self.page
            self.results_view.page = self.page
            self.settings_view.page = self.page
            self.audio_split_view.set_page(self.page)  # Use set_page method
            self.f5_tts_view.page = self.page

        # Get settings for video processing
        split_scenes = False
        enable_transcription = False
        if hasattr(self.settings_view, 'settings'):
            split_scenes = self.settings_view.settings.get('split_scenes', False)
            enable_transcription = self.settings_view.settings.get('enable_transcription', False)

        # Configure for video processing
        from .universal_progress_view import OperationType
        config = {
            'video_path': video_path,
            'split_scenes': split_scenes,
            'enable_transcription': enable_transcription
        }

        # Start video processing operation using universal progress view
        self.progress_view.start_operation(
            OperationType.VIDEO_PROCESSING,
            None,  # Processor will be created in universal progress view
            config
        )

    def show_dialog(self, dialog):
        """Show a dialog through the main window's page reference"""
        if self.page:
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()
            return True
        return False

    def close_dialog(self):
        """Close the current dialog"""
        if self.page and self.page.dialog:
            self.page.dialog.open = False
            self.page.update()

    def on_processing_complete(self, result):
        """Handle processing completion"""
        # Extract actual result from completion wrapper
        if isinstance(result, dict) and 'result' in result:
            # Universal progress view wraps the result
            self.processing_result = result['result']
        else:
            # Direct result from processor
            self.processing_result = result
            
        print(f"🔄 Processing complete, result keys: {list(self.processing_result.keys()) if isinstance(self.processing_result, dict) else type(self.processing_result)}")
        self.show_results_view()

    def update_title_bar_theme(self):
        """Title bar theme update disabled - using system title bar"""
        # Since we're using the system title bar, no need to update custom title bar theme
        pass
        