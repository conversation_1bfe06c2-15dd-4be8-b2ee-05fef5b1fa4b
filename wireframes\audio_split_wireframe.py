"""
Audio Split Tab Wireframe
Demonstrates the audio splitting workflow with int            crea        # Action buttons
        self.action_buttons = ft.Row([
            create_action_button(
                "Start Audio Split",
                ft.Icons.CONTENT_CUT,
                ft.Colors.PRIMARY,
                self.start_processing,
                disabled=True
            ),
            create_action_button(
                "Upload New Files",
                ft.Icons.UPLOAD_FILE,
                ft.Colors.SECONDARY,
                self.clear_selection
            )
        ], alignment=ft.MainAxisAlignment.CENTER, spacing=20)on(
                "Start Audio Split",
                ft.Icons.CONTENT_CUT,
                ft.Colors.GREEN,
                self.start_processing,
                disabled=True
            ),
            create_action_button(
                "Clear Files",
                ft.Icons.REFRESH,
                ft.Colors.GREY,
                self.clear_selectionress and results
"""
import flet as ft
import time
import threading
from shared.mock_data import AUDIO_FILES, SAMPLE_TEXT_SENTENCES, SAMPLE_SPLIT_RESULTS, PROCESSING_STATES
from shared.wireframe_components import *


class AudioSplitWireframe:
    def __init__(self, page: ft.Page):
        self.page = page
        self.current_state = "upload"  # upload, processing, results
        self.selected_audio_file = None
        self.selected_text_file = None
        self.processing_progress = 0
        self.processing_step = ""
        self.processing_thread = None
        
        # Settings
        self.similarity_threshold = 0.8
        self.buffer_duration = 300  # ms
        self.output_format = "WAV"
        
        self.build_components()
    
    def build_components(self):
        """Build all UI components with modern design"""
        # Audio upload area
        self.audio_upload_area = create_file_upload_area(
            "Drag and drop your audio file here",
            "",
            ft.Icons.AUDIOTRACK_OUTLINED,
            "Supported: MP3, WAV, M4A, FLAC, OGG, AAC, WMA",
            self.on_audio_select
        )
        
        # Text upload area
        self.text_upload_area = create_file_upload_area(
            "Drag and drop your text file here",
            "",
            ft.Icons.TEXT_SNIPPET_OUTLINED,
            "Supported: TXT, SRT, VTT (optional for guided splitting)",
            self.on_text_select
        )
        self.text_upload_area = create_file_upload_area(
            "Select Text File",
            "One sentence per line (TXT format)",
            ft.Icons.TEXT_SNIPPET_OUTLINED,
            "Supported: TXT files only",
            self.on_text_select
        )
        
        # File info displays (hidden initially)
        self.audio_info = ft.Container(visible=False)
        self.text_info = ft.Container(visible=False)
        
        # Text preview area
        self.text_preview = ft.Container(
            content=ft.Column([
                ft.Text("Text Preview", size=16, weight=ft.FontWeight.W_600),
                ft.Container(
                    content=ft.Text(
                        "\\n".join(SAMPLE_TEXT_SENTENCES[:4]) + "\\n...",
                        size=12
                    ),
                    bgcolor=ft.Colors.GREY_100,
                    padding=15,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.GREY_300)
                )
            ], spacing=10),
            visible=False,
            margin=20
        )
        
        # Settings section
        self.settings_section = self.create_settings_section()
        
        # Processing section (hidden initially)
        self.processing_section = ft.Container(visible=False)
        
        # Results section (hidden initially)
        self.results_section = ft.Container(visible=False)
        
        # Action buttons
        self.action_buttons = ft.Row([
            create_action_button(
                "Split Audio",
                ft.Icons.CONTENT_CUT,
                ft.Colors.GREEN,
                self.start_processing,
                disabled=True
            ),
            create_action_button(
                "Clear Files",
                ft.Icons.REFRESH,
                ft.Colors.GREY,
                self.clear_selection
            )
        ], alignment=ft.MainAxisAlignment.CENTER, spacing=15)
        
        self.status_text = create_status_text("Select both audio and text files to begin")
    
    def create_settings_section(self):
        """Create the tab-specific settings section"""
        return create_settings_section("Audio Splitting Settings", [
            create_setting_row(
                "Text-Audio Similarity:",
                ft.Slider(
                    min=0.5, max=1.0, value=self.similarity_threshold,
                    divisions=10, label=f"{self.similarity_threshold:.1f}",
                    on_change=self.on_similarity_change
                ),
                "Higher values = more precise alignment"
            ),
            create_setting_row(
                "Audio Buffer (ms):",
                ft.Slider(
                    min=0, max=500, value=self.buffer_duration,
                    divisions=10, label=f"{self.buffer_duration}ms",
                    on_change=self.on_buffer_change
                ),
                "Padding added to start/end of each segment"
            ),
            create_setting_row(
                "Output Format:",
                ft.Dropdown(
                    options=[
                        ft.dropdown.Option("WAV", "WAV (Uncompressed)"),
                        ft.dropdown.Option("MP3", "MP3 (Compressed)"),
                        ft.dropdown.Option("FLAC", "FLAC (Lossless)")
                    ],
                    value=self.output_format,
                    width=200,
                    on_change=self.on_format_change
                )
            )
        ])
    
    def build(self):
        """Build the complete tab content with modern layout"""
        header = create_header(
            "Upload Your Audio Files",
            "Drag and drop your audio and text files here, or click to select files from your computer. We support all major audio formats."
        )
        
        content = ft.Column([
            header,
            ft.Row([
                ft.Container(self.audio_upload_area, expand=True),
                ft.Container(self.text_upload_area, expand=True)
            ]),
            self.audio_info,
            self.text_info,
            self.text_preview,
            self.settings_section,
            self.processing_section,
            self.results_section,
            ft.Container(
                content=ft.Column([
                    self.action_buttons,
                    self.status_text
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=15),
                margin=ft.margin.symmetric(vertical=20)
            )
        ], spacing=0, scroll=ft.ScrollMode.AUTO)
        
        return create_tab_content(content)
    
    def on_audio_select(self, e):
        """Simulate audio file selection"""
        self.selected_audio_file = AUDIO_FILES[0]  # Use first mock file
        
        # Update audio info display
        self.audio_info.content = ft.Container(
            content=ft.Column([
                ft.Text("✓ Audio File Selected", size=14, weight=ft.FontWeight.W_500, color=ft.Colors.GREEN),
                ft.Text(f"File: {self.selected_audio_file['name']}", size=12),
                ft.Text(f"Duration: {self.selected_audio_file['duration']:.1f}s", size=12),
                ft.Text(f"Format: {self.selected_audio_file['format']}", size=12),
                ft.Text(f"Size: {self.selected_audio_file['size_mb']:.1f} MB", size=12)
            ]),
            bgcolor=ft.Colors.GREY_100,
            border_radius=8,
            padding=15,
            margin=20,
            border=ft.border.all(1, ft.Colors.GREEN)
        )
        self.audio_info.visible = True
        
        # Hide upload area
        self.audio_upload_area.visible = False
        
        self.update_ui_state()
    
    def on_text_select(self, e):
        """Simulate text file selection"""
        self.selected_text_file = {"name": "sample_text.txt", "sentences": SAMPLE_TEXT_SENTENCES}
        
        # Update text info display
        self.text_info.content = ft.Container(
            content=ft.Column([
                ft.Text("✓ Text File Selected", size=14, weight=ft.FontWeight.W_500, color=ft.Colors.GREEN),
                ft.Text(f"File: {self.selected_text_file['name']}", size=12),
                ft.Text(f"Sentences: {len(self.selected_text_file['sentences'])}", size=12)
            ]),
            bgcolor=ft.Colors.GREY_100,
            border_radius=8,
            padding=15,
            margin=20,
            border=ft.border.all(1, ft.Colors.GREEN)
        )
        self.text_info.visible = True
        
        # Show text preview
        self.text_preview.visible = True
        
        # Hide upload area
        self.text_upload_area.visible = False
        
        self.update_ui_state()
    
    def update_ui_state(self):
        """Update UI state based on file selections"""
        if self.selected_audio_file and self.selected_text_file:
            self.action_buttons.controls[0].disabled = False
            self.status_text.value = "Ready to split audio"
            self.status_text.color = ft.Colors.GREEN
        else:
            self.action_buttons.controls[0].disabled = True
            missing = []
            if not self.selected_audio_file:
                missing.append("audio file")
            if not self.selected_text_file:
                missing.append("text file")
            self.status_text.value = f"Select {' and '.join(missing)} to begin"
            self.status_text.color = ft.Colors.ON_SURFACE_VARIANT
        
        self.page.update()
    
    def clear_selection(self, e):
        """Clear file selections and reset UI"""
        self.selected_audio_file = None
        self.selected_text_file = None
        self.current_state = "upload"
        
        # Reset UI elements
        self.audio_upload_area.visible = True
        self.text_upload_area.visible = True
        self.audio_info.visible = False
        self.text_info.visible = False
        self.text_preview.visible = False
        self.processing_section.visible = False
        self.results_section.visible = False
        
        # Reset status
        self.update_ui_state()
    
    def start_processing(self, e):
        """Start simulated processing"""
        if not self.selected_audio_file or not self.selected_text_file or self.current_state == "processing":
            return
            
        self.current_state = "processing"
        
        # Show processing section
        self.processing_section.content = create_progress_section(
            "Splitting Audio",
            0,
            "Initializing..."
        )
        self.processing_section.visible = True
        
        # Hide settings and action buttons during processing
        self.settings_section.visible = False
        self.action_buttons.visible = False
        
        # Start processing simulation
        self.processing_thread = threading.Thread(target=self.simulate_processing, daemon=True)
        self.processing_thread.start()
        
        self.page.update()
    
    def simulate_processing(self):
        """Simulate audio splitting with progress updates"""
        steps = PROCESSING_STATES["audio_split"]
        
        for i, step in enumerate(steps):
            # Update progress
            self.processing_progress = step["progress"]
            self.processing_step = step["step"]
            
            # Update UI
            def update_progress():
                if self.processing_section.content:
                    self.processing_section.content = create_progress_section(
                        "Splitting Audio",
                        self.processing_progress,
                        self.processing_step
                    )
                    self.page.update()
            
            self.page.run_task(update_progress)
            
            # Simulate processing time
            time.sleep(1.2)
        
        # Show results
        def show_results():
            self.show_results()
        
        self.page.run_task(show_results)
    
    def show_results(self):
        """Show processing results"""
        self.current_state = "results"
        
        # Hide processing section
        self.processing_section.visible = False
        
        # Create results content
        result_cards = []
        for i, result in enumerate(SAMPLE_SPLIT_RESULTS):
            result_cards.append(
                create_result_card(
                    f"Segment {i+1}",
                    result["text"][:50] + "...",
                    ft.Icons.AUDIOTRACK,
                    {
                        "File": result["file"],
                        "Duration": f"{result['duration']:.1f}s"
                    },
                    lambda e, idx=i: self.play_audio(idx)
                )
            )
        
        # Results summary
        summary = ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text("Audio Segments", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                    ft.Text(str(len(SAMPLE_SPLIT_RESULTS)), size=24, weight=ft.FontWeight.BOLD)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                ft.Column([
                    ft.Text("Success Rate", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                    ft.Text("100%", size=24, weight=ft.FontWeight.BOLD)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                ft.Column([
                    ft.Text("Processing Time", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                    ft.Text("01:45", size=24, weight=ft.FontWeight.BOLD)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                ft.Column([
                    ft.Text("Total Duration", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                    ft.Text(f"{sum(r['duration'] for r in SAMPLE_SPLIT_RESULTS):.1f}s", size=24, weight=ft.FontWeight.BOLD)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
            padding=20,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8,
            margin=20
        )
        
        # Results grid
        results_grid = ft.GridView(
            expand=True,
            runs_count=2,
            max_extent=300,
            child_aspect_ratio=1.5,
            spacing=10,
            run_spacing=10,
            padding=20
        )
        results_grid.controls.extend(result_cards)
        
        # Results action buttons
        results_buttons = ft.Row([
            create_action_button("Open Folder", ft.Icons.FOLDER_OPEN, ft.Colors.BLUE, self.open_folder),
            create_action_button("Export All", ft.Icons.DOWNLOAD, ft.Colors.PURPLE, self.export_all),
            create_action_button("New Split", ft.Icons.ADD, ft.Colors.GREEN, self.new_processing),
        ], alignment=ft.MainAxisAlignment.CENTER, spacing=10)
        
        self.results_section.content = ft.Column([
            ft.Text("Audio Split Results", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.PRIMARY),
            summary,
            ft.Container(results_grid, height=400),
            results_buttons
        ])
        self.results_section.visible = True
        
        # Update status
        self.status_text.value = f"Successfully split into {len(SAMPLE_SPLIT_RESULTS)} segments"
        self.status_text.color = ft.Colors.GREEN
        
        self.page.update()
    
    def play_audio(self, index):
        """Simulate audio playback"""
        filename = SAMPLE_SPLIT_RESULTS[index]["file"]
        self.page.show_snack_bar(ft.SnackBar(ft.Text(f"🎵 Playing {filename}...")))
    
    def new_processing(self, e):
        """Start new processing workflow"""
        self.clear_selection(e)
        self.settings_section.visible = True
        self.action_buttons.visible = True
    
    def open_folder(self, e):
        """Simulate opening output folder"""
        self.page.show_snack_bar(ft.SnackBar(ft.Text("📁 Opening output folder...")))
    
    def export_all(self, e):
        """Simulate exporting all files"""
        self.page.show_snack_bar(ft.SnackBar(ft.Text("📦 Exporting all audio segments...")))
    
    # Settings change handlers
    def on_similarity_change(self, e):
        self.similarity_threshold = e.control.value
        e.control.label = f"{e.control.value:.1f}"
        self.page.update()
    
    def on_buffer_change(self, e):
        self.buffer_duration = int(e.control.value)
        e.control.label = f"{int(e.control.value)}ms"
        self.page.update()
    
    def on_format_change(self, e):
        self.output_format = e.control.value


def main(page: ft.Page):
    page.title = "Audio Split - Wireframe"
    page.theme_mode = ft.ThemeMode.DARK
    page.window.width = 1200
    page.window.height = 800
    
    wireframe = AudioSplitWireframe(page)
    page.add(wireframe.build())


if __name__ == "__main__":
    ft.app(target=main)