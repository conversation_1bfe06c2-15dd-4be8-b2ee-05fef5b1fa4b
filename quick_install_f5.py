"""
Quick F5-TTS Installation
"""

import subprocess
import sys

def install_f5_tts():
    """Install F5-TTS quickly"""
    print("🚀 Installing F5-TTS...")
    
    try:
        # Install F5-TTS from GitHub
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "git+https://github.com/SWivid/F5-TTS.git"
        ], check=True)
        
        print("✅ F5-TTS installed successfully!")
        
        # Test import
        try:
            import f5_tts
            print("✅ F5-TTS import successful!")
            return True
        except ImportError:
            print("⚠️ F5-TTS installed but import failed")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        return False

if __name__ == "__main__":
    success = install_f5_tts()
    if success:
        print("\n🎉 Ready to use F5-TTS with ComfyUI voices!")
        print("Launch the app with: python src/main.py")
    else:
        print("\n⚠️ F5-TTS installation failed, but mock TTS will work")