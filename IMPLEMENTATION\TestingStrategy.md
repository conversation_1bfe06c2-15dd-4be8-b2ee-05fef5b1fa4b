# Testing Strategy - Vid2Frames

## Testing Philosophy

Our testing approach follows the testing pyramid principle with emphasis on automated testing, continuous integration, and quality-first development. We prioritize fast feedback loops and comprehensive coverage of critical video processing functionality.

## Testing Levels

### 1. Unit Testing (70% of test effort)

#### Framework & Tools
- **pytest:** Primary testing framework for Python
- **pytest-asyncio:** Testing async FastAPI endpoints
- **unittest.mock:** Mocking external dependencies
- **pytest-cov:** Code coverage reporting
- **factory_boy:** Test data generation

#### Coverage Requirements
- **Minimum Coverage:** 85% for all modules
- **Critical Path Coverage:** 95% for video processing algorithms
- **API Endpoints:** 90% line coverage
- **Database Models:** 80% coverage

#### Test Categories

**Algorithm Testing:**
```python
# Example: Frame similarity detection tests
class TestFrameSimilarity:
    def test_identical_frames_high_similarity(self):
        frame1 = create_test_frame("identical.jpg")
        frame2 = create_test_frame("identical.jpg")
        similarity = calculate_similarity(frame1, frame2)
        assert similarity > 0.95
    
    def test_different_frames_low_similarity(self):
        frame1 = create_test_frame("scene1.jpg") 
        frame2 = create_test_frame("scene2.jpg")
        similarity = calculate_similarity(frame1, frame2)
        assert similarity < 0.3
```

**API Endpoint Testing:**
```python
@pytest.mark.asyncio
async def test_upload_video_success(client, test_video):
    response = await client.post(
        "/api/v1/videos/upload",
        files={"video": test_video},
        headers={"Authorization": "Bearer valid_token"}
    )
    assert response.status_code == 201
    assert "job_id" in response.json()
```

### 2. Integration Testing (20% of test effort)

#### Framework & Tools
- **pytest-postgresql:** Database testing with clean state
- **testcontainers-python:** Docker containers for dependencies
- **httpx:** HTTP client for API testing
- **moto:** AWS S3 mocking

#### Test Scenarios

**Database Integration:**
- User registration and authentication flows
- Video job creation and status updates
- Frame metadata storage and retrieval
- Data consistency across transactions

**External Service Integration:**
- S3/MinIO file upload and retrieval
- Redis caching and session management
- Email notification service integration
- Payment processing (if applicable)

**Processing Pipeline Integration:**
```python
class TestVideoProcessingPipeline:
    def test_end_to_end_processing(self):
        # Upload video
        job_id = upload_test_video("sample.mp4")
        
        # Trigger processing
        process_video(job_id)
        
        # Verify frames extracted
        frames = get_extracted_frames(job_id)
        assert len(frames) > 0
        assert all(frame.similarity_score > 0.7 for frame in frames)
```

### 3. End-to-End Testing (10% of test effort)

#### Framework & Tools
- **Playwright:** Browser automation for web UI
- **pytest-playwright:** Integration with pytest
- **Docker Compose:** Full system testing environment

#### Test Scenarios

**User Journey Tests:**
1. **Complete Video Processing Flow:**
   - User registration and login
   - Video upload with progress tracking
   - Processing configuration
   - Frame extraction completion
   - Download of extracted frames

2. **Error Handling Scenarios:**
   - Invalid file format upload
   - Network interruption during upload
   - Processing timeout handling
   - User session expiration

```python
def test_complete_user_journey(page):
    # Login
    page.goto("/login")
    page.fill("#email", "<EMAIL>")
    page.fill("#password", "password123")
    page.click("#login-button")
    
    # Upload video
    page.goto("/upload")
    page.set_input_files("#video-input", "test_video.mp4")
    page.click("#upload-button")
    
    # Wait for processing
    page.wait_for_selector(".processing-complete")
    
    # Download frames
    page.click("#download-all")
    download = page.wait_for_download()
    assert download
```

## Performance Testing

### Load Testing
- **Tool:** Locust for API load testing
- **Metrics:** Response time, throughput, error rate
- **Scenarios:** 
  - Concurrent video uploads (50+ users)
  - Processing queue stress testing
  - Database connection pool limits

### Stress Testing
- **Video Size Limits:** Test with files up to 1GB
- **Processing Duration:** Long videos (2+ hours)
- **Memory Usage:** Monitor for memory leaks
- **Storage Limits:** Disk space exhaustion scenarios

```python
# Example Locust load test
class VideoProcessingUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        self.login()
    
    @task(3)
    def upload_video(self):
        with open("test_video.mp4", "rb") as video:
            self.client.post("/api/v1/videos/upload", 
                           files={"video": video})
    
    @task(1) 
    def check_status(self):
        if hasattr(self, 'job_id'):
            self.client.get(f"/api/v1/videos/{self.job_id}/status")
```

## Security Testing

### Authentication & Authorization Testing
- JWT token validation and expiration
- API key authentication testing
- Role-based access control verification
- Session management security

### Input Validation Testing
- File upload security (malicious files)
- SQL injection prevention
- XSS attack prevention
- Parameter tampering protection

### Infrastructure Security Testing
- Container vulnerability scanning
- Dependency security audits
- SSL/TLS configuration testing
- GDPR compliance validation

```python
def test_unauthorized_access():
    response = client.get("/api/v1/videos/123/frames")
    assert response.status_code == 401
    
def test_malicious_file_upload():
    with open("malicious.exe", "rb") as malware:
        response = client.post("/api/v1/videos/upload",
                             files={"video": malware})
        assert response.status_code == 400
        assert "unsupported format" in response.json()["detail"]
```

## Test Data Management

### Test Video Assets
- **Small Files (< 10MB):** Quick processing tests
- **Medium Files (50-100MB):** Realistic processing scenarios  
- **Large Files (> 500MB):** Performance and stress testing
- **Various Formats:** MP4, AVI, MOV, MKV, WebM
- **Edge Cases:** Corrupted files, zero-byte files, unsupported codecs

### Database Test Data
```python
# Factory for test data generation
class VideoJobFactory(factory.Factory):
    class Meta:
        model = VideoJob
    
    user_id = factory.LazyFunction(lambda: str(uuid.uuid4()))
    original_filename = factory.Sequence(lambda n: f"video_{n}.mp4")
    file_size = factory.Faker('random_int', min=1000000, max=100000000)
    duration = factory.Faker('random_int', min=30, max=3600)
    status = 'pending'
```

## Continuous Integration Testing

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: test
      redis:
        image: redis:7
        
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install -r requirements-test.txt
          
      - name: Run unit tests
        run: pytest tests/unit --cov=src --cov-report=xml
        
      - name: Run integration tests  
        run: pytest tests/integration
        
      - name: Security scan
        run: bandit -r src/
        
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: pytest-unit
        name: pytest-unit
        entry: pytest tests/unit
        language: system
        always_run: true
      - id: black
        name: black
        entry: black
        language: system
        types: [python]
      - id: isort
        name: isort
        entry: isort
        language: system
        types: [python]
```

## Test Environment Management

### Environment Configuration
- **Local Development:** SQLite database, local Redis
- **CI/CD:** PostgreSQL, Redis containers
- **Staging:** Production-like environment with test data
- **Load Testing:** Dedicated high-performance environment

### Docker Test Environment
```dockerfile
# Dockerfile.test
FROM python:3.11-slim
WORKDIR /app
COPY requirements-test.txt .
RUN pip install -r requirements-test.txt
COPY . .
CMD ["pytest", "--cov=src", "--cov-report=term-missing"]
```

## Quality Gates & Reporting

### Automated Quality Checks
- **Code Coverage:** Minimum 85% line coverage
- **Security Scan:** No high-severity vulnerabilities
- **Performance Tests:** Response time < 200ms for APIs
- **Load Tests:** Handle 50 concurrent users

### Test Reporting
- **Coverage Reports:** HTML and XML formats for CI/CD
- **Test Results:** JUnit XML for integration with tools
- **Performance Metrics:** Response time trends and alerts
- **Security Reports:** Vulnerability scanning results

### Failure Handling
```python
# Example test retry and reporting
@pytest.mark.flaky(reruns=3, reruns_delay=2)
def test_video_processing_with_retry():
    """Flaky test that retries on failure"""
    result = process_video("flaky_video.mp4")
    assert result.status == "completed"

# Custom test markers for different environments
pytestmark = [
    pytest.mark.integration,
    pytest.mark.slow,
    pytest.mark.requires_gpu  # For computer vision tests
]
```

## Monitoring & Observability Testing

### Application Performance Monitoring
- **Response Time Tracking:** Monitor API endpoint performance
- **Error Rate Monitoring:** Track and alert on error spikes
- **Resource Usage:** Memory, CPU, and storage monitoring
- **Queue Length:** Processing queue depth tracking

### Testing Observability
- **Test Execution Time:** Track slow tests for optimization
- **Flaky Test Detection:** Identify and fix unstable tests
- **Coverage Trends:** Monitor coverage changes over time
- **Test Environment Health:** Database, Redis, storage status

This comprehensive testing strategy ensures high-quality delivery of the Vid2Frames application with robust error handling, performance optimization, and security compliance.