#!/usr/bin/env python3
"""
Test script for B-roll keyword extraction
Tests the contextual keyword extraction on the business analyst script
"""

import re
# import spacy  # Will add later
from collections import Counter
from typing import List, Dict, Set

class ContextualKeywordExtractor:
    def __init__(self):
        # For now, we'll use basic text processing
        # Later we'll integrate spaCy when we add it to requirements
        self.visual_keywords = {
            # Business/Office
            "office", "computer", "laptop", "meeting", "presentation", "documents", 
            "team", "workplace", "desk", "business", "professional", "suit",
            
            # Technology
            "app", "software", "code", "developer", "tech", "system", "digital",
            "screen", "typing", "programming", "database", "website",
            
            # Communication/Collaboration  
            "discussion", "interview", "conversation", "handshake", "collaboration",
            "phone", "email", "video call", "conference", "brainstorming",
            
            # Analysis/Planning
            "chart", "diagram", "graph", "planning", "strategy", "analysis",
            "whiteboard", "notes", "documentation", "requirements", "flowchart"
        }
        
        self.abstract_words = {
            "idea", "concept", "thought", "feeling", "method", "way", "thing",
            "process", "approach", "strategy", "solution", "problem", "issue"
        }
    
    def extract_cohesive_keywords(self, sentences: List[str]) -> Dict[int, List[str]]:
        """Extract contextual keywords for each sentence"""
        print("🔍 Analyzing full script for context...")
        
        # Step 1: Analyze entire script
        full_text = " ".join(sentences)
        context = self._analyze_story_context(full_text)
        
        print(f"📊 Detected domain: {context['domain']}")
        print(f"🎯 Main themes: {context['themes'][:5]}")
        print(f"🏢 Business entities: {context['entities'][:3]}")
        
        # Step 2: Extract keywords per sentence
        sentence_keywords = {}
        print(f"\n📝 Processing {len(sentences)} sentences...")
        
        for i, sentence in enumerate(sentences):
            keywords = self._extract_sentence_keywords(
                sentence, 
                context, 
                previous_keywords=sentence_keywords.get(i-1, [])
            )
            sentence_keywords[i] = keywords
            print(f"   {i+1:2d}. \"{sentence[:50]}...\" → {keywords}")
            
        return sentence_keywords
    
    def _analyze_story_context(self, full_text: str) -> Dict:
        """Analyze the entire script for context"""
        text_lower = full_text.lower()
        
        # Simple word extraction (will be replaced with spaCy)
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text_lower)
        word_freq = Counter(words)
        
        # Domain detection
        domain = self._detect_domain(text_lower)
        
        # Extract main themes (frequent meaningful words)
        themes = []
        for word, count in word_freq.most_common(20):
            if (word not in {'the', 'and', 'you', 'they', 'that', 'with', 'for', 'are', 'but'} 
                and len(word) > 3 and count > 2):
                themes.append(word)
        
        # Extract business entities (simple pattern matching)
        entities = []
        business_patterns = ['business analyst', 'developer', 'stakeholder', 'manager', 'team']
        for pattern in business_patterns:
            if pattern in text_lower:
                entities.append(pattern)
        
        return {
            'domain': domain,
            'themes': themes,
            'entities': entities,
            'tone': 'educational'  # Could be detected more sophisticatedly
        }
    
    def _extract_sentence_keywords(self, sentence: str, context: Dict, previous_keywords: List[str]) -> List[str]:
        """Extract visual keywords for a specific sentence"""
        sentence_lower = sentence.lower()
        
        # Extract potential keywords from sentence
        words = re.findall(r'\b[a-zA-Z]{3,}\b', sentence_lower)
        
        candidates = []
        
        # 1. Look for visual concepts in the sentence
        for word in words:
            if self._is_visual_concept(word):
                priority = 1
                
                # Boost priority if it matches story themes
                if word in context['themes']:
                    priority = 3
                
                # Boost if it provides continuity
                if previous_keywords and any(self._are_related(word, prev) for prev in previous_keywords):
                    priority = max(priority, 2)
                
                candidates.append((word, priority))
        
        # 2. Add domain-specific keywords if sentence mentions key concepts
        domain_keywords = self._get_domain_keywords(sentence_lower, context['domain'])
        for keyword in domain_keywords:
            if keyword not in [c[0] for c in candidates]:
                candidates.append((keyword, 2))
        
        # 3. Sort by priority and select top 3
        candidates.sort(key=lambda x: x[1], reverse=True)
        final_keywords = [word for word, _ in candidates[:3]]
        
        # 4. Ensure we have at least 2 keywords
        if len(final_keywords) < 2:
            fallback_keywords = self._get_fallback_keywords(context['domain'])
            for fallback in fallback_keywords:
                if fallback not in final_keywords:
                    final_keywords.append(fallback)
                    if len(final_keywords) >= 2:
                        break
        
        return final_keywords[:3]  # Max 3 keywords
    
    def _is_visual_concept(self, word: str) -> bool:
        """Check if word represents something visual"""
        if word in self.abstract_words:
            return False
        
        if word in self.visual_keywords:
            return True
        
        # Check if it's a concrete noun (simplified)
        concrete_indicators = [
            'app', 'computer', 'office', 'team', 'meeting', 'document', 'chart',
            'people', 'person', 'screen', 'desk', 'phone', 'email', 'website'
        ]
        
        return any(indicator in word or word in indicator for indicator in concrete_indicators)
    
    def _detect_domain(self, text: str) -> str:
        """Detect content domain"""
        domain_keywords = {
            'business': ['business', 'analyst', 'stakeholder', 'requirement', 'manager', 'corporate', 'meeting'],
            'technology': ['app', 'software', 'developer', 'tech', 'system', 'code', 'digital'],
            'education': ['learn', 'explain', 'teach', 'understand', 'knowledge'],
            'office': ['office', 'workplace', 'professional', 'corporate', 'team']
        }
        
        domain_scores = {}
        for domain, keywords in domain_keywords.items():
            score = sum(text.count(keyword) for keyword in keywords)
            domain_scores[domain] = score
        
        return max(domain_scores, key=domain_scores.get) if domain_scores else 'general'
    
    def _get_domain_keywords(self, sentence: str, domain: str) -> List[str]:
        """Get domain-specific visual keywords based on sentence content"""
        keywords = []
        
        if domain == 'business':
            if any(word in sentence for word in ['meeting', 'interview', 'stakeholder']):
                keywords.extend(['meeting', 'office', 'business'])
            if any(word in sentence for word in ['document', 'requirement', 'write']):
                keywords.extend(['documents', 'computer', 'typing'])
            if any(word in sentence for word in ['developer', 'tech', 'app']):
                keywords.extend(['developer', 'computer', 'tech'])
            if any(word in sentence for word in ['translate', 'bridge', 'communicate']):
                keywords.extend(['communication', 'collaboration', 'team'])
        
        return keywords
    
    def _are_related(self, word1: str, word2: str) -> bool:
        """Check if two words are semantically related (simplified)"""
        related_groups = [
            {'office', 'business', 'corporate', 'workplace', 'professional'},
            {'computer', 'tech', 'software', 'app', 'digital', 'developer'},
            {'meeting', 'team', 'collaboration', 'discussion', 'interview'},
            {'documents', 'requirements', 'analysis', 'planning', 'chart'}
        ]
        
        for group in related_groups:
            if word1 in group and word2 in group:
                return True
        return False
    
    def _get_fallback_keywords(self, domain: str) -> List[str]:
        """Get fallback keywords for the domain"""
        fallbacks = {
            'business': ['office', 'professional', 'business'],
            'technology': ['computer', 'tech', 'digital'],
            'general': ['professional', 'office', 'people']
        }
        return fallbacks.get(domain, fallbacks['general'])

def test_keyword_extraction():
    """Test the keyword extraction on the business analyst script"""
    
    # Read the sample script
    try:
        with open(r"c:\Users\<USER>\OneDrive\Music\what is a ba.txt", 'r', encoding='utf-8') as f:
            script_text = f.read()
    except FileNotFoundError:
        print("❌ Could not find the sample script file")
        return
    
    # Split into sentences (simple approach)
    sentences = []
    paragraphs = script_text.split('\n\n')
    
    for paragraph in paragraphs:
        paragraph = paragraph.strip()
        if paragraph:
            # Split by sentence endings, but keep sentences reasonable length
            current_sentences = re.split(r'[.!?]+', paragraph)
            for sentence in current_sentences:
                sentence = sentence.strip()
                if sentence and len(sentence) > 20:  # Skip very short fragments
                    sentences.append(sentence)
    
    print(f"🎬 B-Roll Keyword Extraction Test")
    print(f"📄 Script: Business Analyst Explanation")
    print(f"📊 Total sentences: {len(sentences)}")
    print("=" * 70)
    
    # Extract keywords
    extractor = ContextualKeywordExtractor()
    results = extractor.extract_cohesive_keywords(sentences)
    
    print("\n" + "=" * 70)
    print("🎯 KEYWORD EXTRACTION RESULTS")
    print("=" * 70)
    
    for i, sentence in enumerate(sentences):
        keywords = results.get(i, [])
        print(f"\n📝 Sentence {i+1}:")
        print(f"   Text: \"{sentence[:80]}...\"")
        print(f"   🎥 B-roll keywords: {keywords}")
        
        # Simulate what videos these might find
        print(f"   💡 Potential videos: {', '.join([f'{k} footage' for k in keywords])}")
    
    print(f"\n🎉 Extraction complete! Generated keywords for {len(results)} sentences.")
    
    # Analysis summary
    all_keywords = []
    for keywords in results.values():
        all_keywords.extend(keywords)
    
    keyword_freq = Counter(all_keywords)
    print(f"\n📊 Most common keywords: {dict(keyword_freq.most_common(5))}")
    print(f"🎯 Unique keywords: {len(set(all_keywords))}")
    print(f"♻️  Keyword reuse rate: {(len(all_keywords) - len(set(all_keywords))) / len(all_keywords) * 100:.1f}%")

if __name__ == "__main__":
    test_keyword_extraction()