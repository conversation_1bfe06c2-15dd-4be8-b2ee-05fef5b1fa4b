#!/usr/bin/env python3
"""
Test F5-TTS with actual model
"""

import sys
from pathlib import Path
sys.path.insert(0, 'src')

from core.f5_tts import F5TTSProcessor

def test_f5_real():
    """Test real F5-TTS generation"""
    print("🧪 Testing F5-TTS with real model...")
    
    # Initialize processor
    processor = F5TTSProcessor()
    
    # Load ComfyUI voices
    voices = processor.get_available_comfy_voices()
    print(f"Available voices: {[v['name'] for v in voices]}")
    
    # Set a ComfyUI voice
    if voices:
        voice = voices[0]  # Use first available voice
        processor.set_reference_audio(voice['path'])
        print(f"🎤 Using voice: {voice['name']}")
    
    # Load model
    print("🔄 Loading F5-TTS model...")
    try:
        processor.load_model()
        print(f"Model loaded: {processor.model_loaded}")
        print(f"Using real F5-TTS: {hasattr(processor, 'model') and processor.model is not None}")
        
        # Test generation
        if processor.model_loaded:
            test_text = "Hello, this is a test of the F5-TTS voice cloning system."
            output_path = Path("test_f5_output.wav")
            
            print(f"🎵 Generating: '{test_text}'")
            success = processor.generate_speech(test_text, output_path)
            
            if success and output_path.exists():
                print(f"✅ Audio generated successfully: {output_path}")
                print(f"File size: {output_path.stat().st_size} bytes")
            else:
                print("❌ Audio generation failed")
        else:
            print("❌ Model not loaded")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_f5_real()