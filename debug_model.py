#!/usr/bin/env python3

import sys
sys.path.insert(0, 'src')

from core.f5_tts import F5TTSProcessor

# Initialize and load model
processor = F5TTSProcessor()
processor.load_model()

print(f'Model loaded: {processor.model_loaded}')
print(f'Model type: {type(processor.model)}')
print(f'Model has generate: {hasattr(processor.model, "generate")}')

if hasattr(processor.model, 'generate'):
    try:
        result = processor.model.generate('Test')
        print(f'Generate method works: {type(result)}')
    except Exception as e:
        print(f'Generate method error: {e}')
        import traceback
        traceback.print_exc()

print('Model attributes:')
for attr in dir(processor.model):
    if not attr.startswith('_'):
        print(f'  {attr}: {type(getattr(processor.model, attr))}')