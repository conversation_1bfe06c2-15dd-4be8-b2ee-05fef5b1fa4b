# Vid2Frames - Scene Splitting Feature

## Overview

The Vid2Frames application now includes advanced scene splitting functionality that can automatically detect scene changes in videos and split them into separate video files. This feature leverages the existing frame similarity detection system to identify when scenes transition.

## How It Works

### 1. Scene Detection Algorithm
- **Similarity Analysis**: Uses the same SSIM (Structural Similarity Index) algorithm used for frame extraction
- **Lower Threshold**: Applies a lower similarity threshold (70% of the frame extraction threshold) to detect more subtle scene changes
- **Frame-to-Frame Comparison**: Analyzes consecutive extracted frames to identify significant visual changes
- **Duration Filtering**: Filters out scenes shorter than the minimum duration setting

### 2. Video Splitting Process
- **FFmpeg Integration**: Uses FFmpeg for precise video segment extraction
- **Lossless Splitting**: Uses stream copying (`-c copy`) for fast, lossless splitting
- **Precise Timing**: Segments videos based on exact timestamps from scene boundaries
- **Clean Filenames**: Creates descriptive filenames like `scene_01_0.0s-15.3s.mp4`

## User Interface Features

### Settings Configuration
**Location**: Settings View → Processing Options

- **Split Video into Scenes**: Enable/disable scene splitting (checkbox)
- **Minimum Scene Duration**: Set minimum scene length (0.5-10 seconds)
- **Scene Detection**: Must be enabled for scene splitting to work

### Progress Tracking
The progress view now shows 5 stages when scene splitting is enabled:
1. **Analyze**: Video properties and metadata
2. **Extract**: Frame extraction with similarity detection
3. **Scenes**: Scene boundary detection
4. **Split**: Video file splitting with FFmpeg
5. **Save**: Frame saving to disk

### Results Display
**Enhanced Statistics**:
- Total Frames Extracted
- Scenes Detected
- Processing Time
- Total Size (includes both frames and scene videos)

**New Actions**:
- **View Scenes**: Shows detailed scene information dialog
- **Open Scenes Folder**: Direct access to scene video files

## File Organization

When scene splitting is enabled, the output structure becomes:
```
job_output_folder/
├── frames/                 # Extracted frames
│   ├── frame_000001_0.000s.png
│   ├── frame_000123_5.125s.png
│   └── ...
├── scenes/                 # Split scene videos
│   ├── scene_01_0.0s-15.3s.mp4
│   ├── scene_02_15.3s-32.1s.mp4
│   └── ...
└── metadata/
    └── job_info.json       # Processing metadata
```

## Technical Implementation

### Core Classes

#### `SceneData`
```python
class SceneData:
    def __init__(self, start_time, end_time, start_frame, end_frame):
        self.start_time = start_time      # Scene start in seconds
        self.end_time = end_time          # Scene end in seconds
        self.start_frame = start_frame    # First frame number
        self.end_frame = end_frame        # Last frame number
        self.duration = end_time - start_time
        self.frame_count = end_frame - start_frame + 1
```

### Key Methods

#### `VideoProcessor._detect_scenes()`
- Analyzes extracted frames for scene boundaries
- Uses configurable similarity threshold
- Returns list of `SceneData` objects
- Filters scenes by minimum duration

#### `VideoProcessor._split_video_into_scenes()`
- Creates scene video files using FFmpeg
- Handles FFmpeg availability detection
- Provides progress updates during splitting
- Error handling for timeout and processing issues

## Configuration Options

### Processing Settings
```python
@dataclass
class ProcessingSettings:
    split_scenes: bool = False              # Enable scene splitting
    min_scene_duration: float = 1.0        # Minimum scene length
    similarity_threshold: float = 0.85      # Base similarity threshold
```

Scene detection uses `similarity_threshold * 0.7` for more sensitive detection.

## Dependencies

### Required Software
- **FFmpeg**: Must be installed and available in system PATH
  - Windows: Download from https://ffmpeg.org/download.html
  - macOS: `brew install ffmpeg`
  - Linux: `sudo apt install ffmpeg` (Ubuntu/Debian)

### Python Libraries
All required libraries are already included in the project:
- OpenCV (`cv2`) - Video processing
- scikit-image - SSIM similarity calculation
- subprocess - FFmpeg integration

## Usage Examples

### Basic Scene Splitting
1. Open Vid2Frames application
2. Go to Settings view
3. Enable "Split Video into Scenes"
4. Set desired minimum scene duration
5. Return to Upload view and process a video
6. View results in Results view with scene information

### Advanced Configuration
```python
# Programmatic usage
processor = VideoProcessor()
result = processor.process_video(
    video_path=Path("input.mp4"),
    similarity_threshold=0.85,
    split_scenes=True
)

scenes = result['detected_scenes']
scene_videos = result['scene_video_paths']
```

## Performance Considerations

### Processing Time
- Scene detection adds minimal overhead (< 5% typical increase)
- Video splitting time depends on video length and number of scenes
- FFmpeg stream copying is fast (typically 2-10x real-time)

### Storage Requirements
- Scene videos use the same codec as original (no quality loss)
- Total storage = original video size + extracted frames
- Typical overhead: 0-10% depending on scene count

### Memory Usage
- Scene detection uses existing extracted frames (no additional memory)
- FFmpeg processes run independently (controlled memory usage)

## Error Handling

### FFmpeg Not Available
- Graceful fallback: scene detection runs but no video files created
- Clear user notification in logs
- Processing continues with frame extraction

### Scene Splitting Failures
- Individual scene failures don't stop overall processing
- Error logging for debugging
- Partial results still available

### Timeout Protection
- 60-second timeout per scene to prevent hanging
- Automatic cleanup of incomplete files
- Progress updates continue normally

## Integration Points

### With Existing Features
- **Frame Extraction**: Uses same frames for scene analysis
- **Progress Tracking**: Extended with new stages
- **ZIP Export**: Includes scene videos in export
- **Results View**: Enhanced with scene information

### Future Enhancements
- Scene thumbnail preview
- Custom scene boundary adjustment
- Multiple export formats for scenes
- Batch scene processing
- Scene metadata export (JSON, CSV)

## Troubleshooting

### Common Issues
1. **No scenes detected**: Adjust similarity threshold or minimum duration
2. **FFmpeg errors**: Verify FFmpeg installation and PATH configuration
3. **Slow processing**: Reduce minimum scene duration or video resolution
4. **Large output files**: Consider compression settings for scene videos

### Debug Information
Scene splitting progress and errors are logged to console. Enable verbose logging by running:
```bash
python src/main.py --verbose
```

## Benefits for Users

### Content Creation
- **Video Editing**: Automatically split long recordings into manageable segments
- **Content Review**: Quickly identify and extract specific scenes
- **Highlight Reels**: Isolate interesting portions automatically

### Analysis and Archival
- **Scene Cataloging**: Organize video content by natural boundaries
- **Storage Optimization**: Work with smaller, focused video segments
- **Batch Processing**: Process multiple related segments efficiently

### Educational and Research
- **Video Analysis**: Study scene composition and transitions
- **Content Indexing**: Create searchable video databases
- **Workflow Automation**: Integrate with other video processing tools

This scene splitting feature transforms Vid2Frames from a frame extraction tool into a comprehensive video analysis and segmentation solution, making it valuable for content creators, researchers, and anyone working with video content.