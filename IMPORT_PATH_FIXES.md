# Import Path Fixes for Vid2Frames Project

## Common Import Issues and Solutions

### ❌ Incorrect Import Patterns
```python
# These imports are failing:
from src.ui.tabs.f5_tts_tab import F5TTSTab          # Wrong path
from ui.main_window import MainWindow                # Missing src prefix
from ui.results_view import ResultsView              # Missing src prefix
from src.ui.tabs.video_processing_tab import Tab    # Wrong path
```

### ✅ Correct Import Patterns
```python
# Use these instead:
from src.ui.f5_tts_view import F5TTSView            # Correct path
from src.ui.main_window import MainWindow           # Correct path
from src.ui.results_view import ResultsView         # Correct path
# Video processing tab may need to be checked for actual location
```

## Files Needing Import Fixes

### 1. debug_f5tts_tab.py ✅ FIXED
- Changed `src.ui.tabs.f5_tts_tab` → `src.ui.f5_tts_view`
- Updated class references `F5TTSTab` → `F5TTSView`

### 2. test_current_fixes.py
```python
# Fix line 12:
from ui.main_window import MainWindow
# Change to:
from src.ui.main_window import MainWindow
```

### 3. test_f5_regenerate_fix.py
```python
# Fix line 14:
from ui.results_view import ResultsView
# Change to:
from src.ui.results_view import ResultsView
```

### 4. test_f5tts_ui.py
```python
# Fix line 24:
from src.ui.tabs.f5_tts_tab import F5TTSTab
# Change to:
from src.ui.f5_tts_view import F5TTSView
```

### 5. test_result_flow.py
```python
# Fix line 55:
from ui.main_window import MainWindow
# Change to:
from src.ui.main_window import MainWindow
```

### 6. test_tab_instantiation.py
```python
# Fix line 1:
from src.ui.tabs.video_processing_tab import Tab
# Change to - need to verify actual location:
# Check if this file exists or find correct import path
```

## Quick Fix Commands

### For test files needing src prefix:
```bash
# Find and replace in multiple files
find . -name "test_*.py" -exec sed -i 's/from ui\./from src.ui./g' {} \;
```

### For F5-TTS specific imports:
```bash
# Replace F5TTSTab imports
find . -name "*.py" -exec sed -i 's/src\.ui\.tabs\.f5_tts_tab/src.ui.f5_tts_view/g' {} \;
find . -name "*.py" -exec sed -i 's/F5TTSTab/F5TTSView/g' {} \;
```

## Project Structure Reference
```
src/
├── ui/
│   ├── main_window.py          ← MainWindow class
│   ├── results_view.py         ← ResultsView class
│   ├── f5_tts_view.py         ← F5TTSView class (our main UI)
│   └── universal_progress_view.py
├── core/
│   └── f5_tts.py              ← F5TTSProcessor class
└── utils/
    └── ...
```

## Notes
- Always use `from src.ui.` prefix for UI imports
- Check if `video_processing_tab` actually exists before importing
- The debug script now works as a template for testing other components