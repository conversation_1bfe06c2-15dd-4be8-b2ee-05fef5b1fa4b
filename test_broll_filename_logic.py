"""
Simple test to verify the filename sanitization works in the B-roll generator
"""
import os
import sys
sys.path.append('src')
import re
from pathlib import Path

def test_broll_filename_logic():
    """Test just the filename creation logic"""
    
    # Test data that would cause the original issue
    test_data = [
        {
            'sentence': 'Saying "I want to be rich" won\'t cut it.',
            'selected_video': {'download_url': 'https://example.com/video1.mp4'}
        },
        {
            'sentence': 'That\'s a fancy way of saying "use your brain".',
            'selected_video': {'download_url': 'https://example.com/video2.mp4'}
        },
        {
            'sentence': '"',
            'selected_video': {'download_url': 'https://example.com/video3.mp4'}
        }
    ]
    
    print("Testing filename creation logic from B-roll generator:")
    
    for sentence_idx, data in enumerate(test_data):
        # This is the FIXED logic from the B-roll generator
        sentence_words = data['sentence'][:30]
        # Remove or replace invalid filename characters  
        sentence_words = re.sub(r'[<>:"/\\|?*"\']', '', sentence_words)  # Remove invalid chars
        sentence_words = sentence_words.replace(' ', '_').strip('_')
        # Ensure we have some content
        if not sentence_words:
            sentence_words = f"sentence_{sentence_idx + 1}"
        output_filename = f"{sentence_idx + 1:03d}_{sentence_words}.mp4"
        
        print(f"\n{sentence_idx + 1}. Sentence: '{data['sentence']}'")
        print(f"   Filename: '{output_filename}'")
        print(f"   Safe: {'✅' if is_safe_filename(output_filename) else '❌'}")
        
        # Test creating the path (without actually writing)
        try:
            test_path = Path("test_output") / output_filename
            print(f"   Path valid: ✅")
        except Exception as e:
            print(f"   Path error: ❌ {e}")

def is_safe_filename(filename):
    """Check if filename is safe for Windows"""
    invalid_chars = r'[<>:"/\\|?*]'
    return not re.search(invalid_chars, filename) and not filename.endswith(('.', ' '))

if __name__ == "__main__":
    test_broll_filename_logic()