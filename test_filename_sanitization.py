"""
Test the filename sanitization fix
"""
import re

def sanitize_filename_test():
    test_sentences = [
        'Saying "I want to be rich" won\'t cut it.',
        'That\'s a fancy way of saying "use your brain".',
        '"',
        'Normal sentence without special characters.',
        'Another: test with <colons> and |pipes|.',
        ''
    ]
    
    print("Testing filename sanitization:")
    
    for i, sentence in enumerate(test_sentences):
        # Old method (broken)
        old_method = sentence[:30].replace(' ', '_')
        
        # New method (fixed)
        sentence_words = sentence[:30]
        sentence_words = re.sub(r'[<>:"/\\|?*"\']', '', sentence_words)  # Remove invalid chars
        sentence_words = sentence_words.replace(' ', '_').strip('_')
        if not sentence_words:
            sentence_words = f"sentence_{i + 1}"
        new_method = sentence_words
        
        output_filename = f"{i + 1:03d}_{new_method}.mp4"
        
        print(f"\n{i+1}. Original: '{sentence}'")
        print(f"   Old: '{old_method}'")
        print(f"   New: '{new_method}'")
        print(f"   File: '{output_filename}'")
        print(f"   Safe: {'✅' if is_safe_filename(output_filename) else '❌'}")

def is_safe_filename(filename):
    """Check if filename is safe for Windows"""
    invalid_chars = r'[<>:"/\\|?*]'
    return not re.search(invalid_chars, filename) and not filename.endswith(('.', ' '))

if __name__ == "__main__":
    sanitize_filename_test()