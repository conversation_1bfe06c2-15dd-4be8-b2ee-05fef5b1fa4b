"""
Configuration management for Vid2Frames application
"""
import json
import sqlite3
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Optional


@dataclass
class ProcessingSettings:
    """Settings for video processing"""
    similarity_threshold: float = 0.85
    quality_threshold: float = 0.7
    max_frames: Optional[int] = None
    output_format: str = "PNG"
    resize_width: Optional[int] = None
    scene_detection: bool = True
    extract_keyframes_only: bool = False
    split_scenes: bool = False  # New option for scene splitting
    min_scene_duration: float = 1.0  # Minimum scene duration in seconds
    
    # Audio splitting settings
    audio_buffer_ms: int = 100
    audio_similarity_threshold: float = 0.8
    audio_output_format: str = "WAV"


@dataclass
class ApplicationSettings:
    """General application settings"""
    max_file_size_mb: int = 1000
    max_worker_threads: int = 4
    ui_theme: str = "system"
    default_save_location: str = str(Path.home() / "Vid2Frames")
    show_preview: bool = True
    auto_cleanup: bool = True


class ConfigManager:
    """Manages application configuration and settings persistence"""
    
    def __init__(self, config_dir: Optional[Path] = None):
        if config_dir is None:
            config_dir = Path.home() / ".vid2frames"
        
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.db_path = self.config_dir / "settings.db"
        self._init_database()
        
        self.processing = ProcessingSettings()
        self.application = ApplicationSettings()
        self.load_settings()
    
    def _init_database(self):
        """Initialize the settings database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    category TEXT NOT NULL,
                    key TEXT NOT NULL,
                    value TEXT NOT NULL,
                    PRIMARY KEY (category, key)
                )
            """)
            conn.commit()
    
    def save_settings(self):
        """Save current settings to database"""
        with sqlite3.connect(self.db_path) as conn:
            # Save processing settings
            for key, value in asdict(self.processing).items():
                conn.execute(
                    "INSERT OR REPLACE INTO settings (category, key, value) VALUES (?, ?, ?)",
                    ("processing", key, json.dumps(value))
                )
            
            # Save application settings
            for key, value in asdict(self.application).items():
                conn.execute(
                    "INSERT OR REPLACE INTO settings (category, key, value) VALUES (?, ?, ?)",
                    ("application", key, json.dumps(value))
                )
            
            conn.commit()
    
    def load_settings(self):
        """Load settings from database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT category, key, value FROM settings")
                
                for category, key, value in cursor.fetchall():
                    parsed_value = json.loads(value)
                    
                    if category == "processing" and hasattr(self.processing, key):
                        setattr(self.processing, key, parsed_value)
                    elif category == "application" and hasattr(self.application, key):
                        setattr(self.application, key, parsed_value)
        
        except (sqlite3.Error, json.JSONDecodeError, FileNotFoundError):
            # If loading fails, use defaults
            pass
    
    def get_output_directory(self):
        """Get the default output directory for extracted frames"""
        output_dir = Path(self.application.default_save_location)
        output_dir.mkdir(exist_ok=True)
        return output_dir
    
    def get_temp_directory(self):
        """Get temporary directory for processing"""
        temp_dir = self.config_dir / "temp"
        temp_dir.mkdir(exist_ok=True)
        return temp_dir


# Global config manager instance
config = ConfigManager()