#!/usr/bin/env python3
"""
Test Pexels API integration for B-roll video search
"""

import requests
import json
from typing import List, Dict, Optional
from pathlib import Path

class PexelsAPI:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.pexels.com/videos"
        self.headers = {
            "Authorization": api_key
        }
    
    def search_videos(self, query: str, per_page: int = 5, min_duration: int = 3) -> List[Dict]:
        """Search for videos matching the query"""
        
        params = {
            "query": query,
            "per_page": per_page,
            "size": "medium",  # small, medium, large
            "orientation": "landscape"  # landscape, portrait, square
        }
        
        try:
            print(f"🔍 Searching Pexels for: '{query}'")
            response = requests.get(f"{self.base_url}/search", headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                videos = []
                
                for video in data.get("videos", []):
                    # Filter by minimum duration
                    if video.get("duration", 0) >= min_duration:
                        video_data = {
                            "id": video.get("id"),
                            "url": video.get("url"),
                            "duration": video.get("duration"),
                            "width": video.get("width"),
                            "height": video.get("height"),
                            "image": video.get("image"),  # Thumbnail
                            "user": video.get("user", {}).get("name", "Unknown"),
                            "video_files": video.get("video_files", [])
                        }
                        
                        # Get the best quality video URL
                        best_video = self._get_best_video_quality(video_data["video_files"])
                        if best_video:
                            video_data["download_url"] = best_video["link"]
                            video_data["file_type"] = best_video["file_type"]
                            video_data["quality"] = best_video["quality"]
                            videos.append(video_data)
                
                print(f"✅ Found {len(videos)} suitable videos for '{query}'")
                return videos
            
            elif response.status_code == 429:
                print(f"⚠️ Rate limit exceeded. Please wait before making more requests.")
                return []
            
            else:
                print(f"❌ API Error {response.status_code}: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error searching for videos: {e}")
            return []
    
    def _get_best_video_quality(self, video_files: List[Dict]) -> Optional[Dict]:
        """Get the best quality video file from available options"""
        if not video_files:
            return None
        
        # Quality preference: hd, sd, then any available
        quality_preference = ["hd", "sd"]
        
        for quality in quality_preference:
            for video_file in video_files:
                if video_file.get("quality") == quality:
                    return video_file
        
        # If no preferred quality found, return the first one
        return video_files[0]
    
    def get_video_info(self, video_id: int) -> Dict:
        """Get detailed information about a specific video"""
        try:
            response = requests.get(f"{self.base_url}/videos/{video_id}", headers=self.headers)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Error getting video info: {response.status_code}")
                return {}
        except Exception as e:
            print(f"❌ Error getting video info: {e}")
            return {}

def test_pexels_api():
    """Test the Pexels API with different search terms"""
    
    # Use the provided API key
    api_key = "uKxpgNExFg7eaG0HxnWMQejOtIR8XR5vc3yNlEMq6BiOQbz9dwHn8Yd3"
    
    pexels = PexelsAPI(api_key)
    
    print("🎬 Testing Pexels API for B-roll Video Search")
    print("=" * 60)
    
    # Test searches based on our keyword extraction results
    test_keywords = [
        "business office",
        "professional meeting", 
        "computer typing",
        "team collaboration",
        "developer coding",
        "business analyst",
        "office workers",
        "documents paperwork"
    ]
    
    all_results = {}
    
    for keyword in test_keywords:
        print(f"\n🔍 Testing: '{keyword}'")
        print("-" * 40)
        
        videos = pexels.search_videos(keyword, per_page=3)
        all_results[keyword] = videos
        
        if videos:
            for i, video in enumerate(videos, 1):
                print(f"   {i}. Duration: {video['duration']}s | Quality: {video['quality']}")
                print(f"      Size: {video['width']}x{video['height']} | By: {video['user']}")
                print(f"      URL: {video['url'][:60]}...")
                print(f"      Download: {video['download_url'][:60]}...")
        else:
            print(f"   ❌ No videos found for '{keyword}'")
        
        # Small delay to be respectful to the API
        import time
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SEARCH RESULTS SUMMARY")
    print("=" * 60)
    
    total_videos = sum(len(videos) for videos in all_results.values())
    successful_searches = sum(1 for videos in all_results.values() if videos)
    
    print(f"Total searches: {len(test_keywords)}")
    print(f"Successful searches: {successful_searches}")
    print(f"Total videos found: {total_videos}")
    print(f"Success rate: {(successful_searches/len(test_keywords)*100):.1f}%")
    
    # Show best results
    print(f"\n🏆 Best Results:")
    for keyword, videos in all_results.items():
        if videos:
            print(f"   '{keyword}': {len(videos)} videos")
    
    # Show failed searches
    failed_searches = [keyword for keyword, videos in all_results.items() if not videos]
    if failed_searches:
        print(f"\n⚠️ Keywords with no results: {failed_searches}")
    
    print("\n✅ Pexels API test completed!")
    
    return all_results

if __name__ == "__main__":
    results = test_pexels_api()