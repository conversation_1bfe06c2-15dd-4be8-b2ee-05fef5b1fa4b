# Vid2Frames Interactive Wireframes

## Overview

This directory contains interactive wireframes built with Flet (Flutter-based Python UI framework) that demonstrate the modern user experience patterns for the Vid2Frames application. The wireframes have been updated to match the reference designs from the `stitch_video_upload` folder, featuring modern UI components, clean typography, and smooth animations.

## Design Language

The wireframes now follow a modern design system with:

- **Clean Typography**: Large, bold headers with improved hierarchy
- **Modern Upload Areas**: Drag-and-drop styled areas with dashed borders
- **Progress Indicators**: Professional progress tracking with time estimates
- **Results Display**: Card-based layouts with hover animations
- **Responsive Grid**: Flexible grid layouts that adapt to content
- **Consistent Theming**: Light/dark mode support with proper color schemes

## How to Run

```bash
# Run all wireframes in a tabbed interface
python wireframe_runner.py

# Run individual wireframes
python video_frames_wireframe.py
python audio_split_wireframe.py
python f5_tts_wireframe.py
python settings_wireframe.py
```

## Updated Components

### Shared Components (`shared/wireframe_components.py`)

All components have been redesigned to match the reference HTML designs:

- `create_header()` - Modern page headers with centered typography
- `create_file_upload_area()` - Drag-and-drop areas matching reference design
- `create_progress_section()` - Professional progress tracking with time estimates
- `create_result_card()` - Modern frame cards with hover effects
- `create_results_summary()` - Statistical summaries with large numbers
- `create_results_grid()` - Responsive grid layouts for results

## Wireframe Structure

- **wireframe_runner.py** - Main application with all wireframes in tabs
- **video_frames_wireframe.py** - Video processing tab wireframe (UPDATED)
- **audio_split_wireframe.py** - Audio splitting tab wireframe (UPDATED)
- **f5_tts_wireframe.py** - F5-TTS generation tab wireframe
- **settings_wireframe.py** - Application settings tab wireframe
- **shared/mock_data.py** - Sample data for demonstrations
- **shared/wireframe_components.py** - Reusable UI components (UPDATED)

## Features

- ✅ Interactive components with click handlers
- ✅ Responsive design demonstrations
- ✅ Progress simulation animations
- ✅ State management examples
- ✅ Mock data integration
- ✅ Theme switching (Light/Dark)
- ✅ Modern UI patterns matching reference designs
- ✅ Professional upload interfaces
- ✅ Card-based results display
- ✅ Statistical summaries

## Design Patterns Demonstrated

### Video Processing Workflow
- File upload with validation
- Real-time progress tracking
- Frame extraction simulation
- Scene detection results
- Downloadable results with metadata

### Audio Splitting Workflow
- Dual file upload (audio + text)
- Text-audio alignment settings
- Buffer duration configuration
- Split results with playback preview

## Purpose

These wireframes serve as:
- Design documentation based on reference HTML designs
- Interactive prototypes for stakeholder review
- Development reference for UI implementation
- UX testing platform for layout and workflow validation
- Foundation for modern component library

## Reference

The wireframes are based on the HTML designs in `stitch_video_upload/` folder, providing a consistent modern design language across the application.