"""
Test the progress dialog independently to ensure it displays correctly
"""

import flet as ft
import time
import threading

def test_progress_dialog():
    def main(page: ft.Page):
        page.title = "Progress Dialog Test"
        page.theme_mode = ft.ThemeMode.DARK
        
        # Progress dialog components
        progress_text = ft.Text("Initializing...", size=14)
        progress_bar = ft.ProgressBar(value=0, width=300)
        progress_info = ft.Text(
            "Testing progress dialog display", 
            size=12, color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        progress_dialog = ft.AlertDialog(
            title=ft.Text("🎵 Test Progress Dialog", size=18),
            content=ft.Column([
                progress_text,
                progress_bar,
                progress_info
            ], height=120, spacing=15),
            modal=True
        )
        
        def update_progress(message: str, progress: float):
            """Update progress dialog"""
            progress_text.value = message
            progress_bar.value = progress
            page.update()
            print(f"📊 Progress: {message} ({progress:.1%})")
        
        def simulate_progress():
            """Simulate progress updates"""
            steps = [
                ("Loading Whisper model...", 0.1),
                ("Transcribing audio...", 0.3),
                ("Aligning text with audio...", 0.5),
                ("Extracting segments...", 0.8),
                ("Complete!", 1.0)
            ]
            
            for message, progress in steps:
                update_progress(message, progress)
                time.sleep(2)  # 2 second delay between updates
            
            # Close dialog after completion
            time.sleep(2)
            progress_dialog.open = False
            page.update()
        
        def show_test_dialog(e):
            """Show the test progress dialog"""
            print("🔄 Showing test progress dialog...")
            page.dialog = progress_dialog
            progress_dialog.open = True
            page.update()
            
            # Start simulated progress in background
            threading.Thread(target=simulate_progress, daemon=True).start()
        
        # Test button
        test_button = ft.ElevatedButton(
            "Test Progress Dialog",
            on_click=show_test_dialog,
            icon=ft.Icons.PLAY_ARROW
        )
        
        page.add(
            ft.Column([
                ft.Text("Progress Dialog Test", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("Click the button to test the progress dialog"),
                test_button
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=20)
        )
    
    ft.app(target=main)

if __name__ == "__main__":
    test_progress_dialog()