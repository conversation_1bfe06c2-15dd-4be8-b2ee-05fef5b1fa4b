"""
Quick test of B-roll with filename sanitization fix
"""
import os
import sys
sys.path.append('src')

from core.f5_tts import F5TTSProcessor

def test_filename_fix():
    # Test with problematic sentences that caused the original issue
    test_sentences = [
        'This should work fine.',
        'Saying "I want to be rich" won\'t cut it.',  # This caused the original error
        'That\'s a fancy way of saying "use your brain".',  # This also caused error
        'Final test sentence.'
    ]
    
    print("Testing filename fix with F5-TTS B-roll generation...")
    
    processor = F5TTSProcessor()
    
    # Mock broll config - just for filename testing, we don't need real API
    broll_config = {
        'enabled': True,
        'api_key': 'test_key',  # Won't actually call API
        'num_videos': 1,
        'video_quality': 'hd'
    }
    
    try:
        results = processor.process_sentences(
            sentences=test_sentences,
            voice_type='business',
            voice_file=None,
            reference_text="This is a test voice.",
            broll_config=broll_config,
            output_dir="test_filename_fix_output"
        )
        
        print(f"\n✅ Processing completed!")
        print(f"Results keys: {list(results.keys())}")
        
        if 'combined_videos' in results:
            combined_videos = results['combined_videos']
            print(f"Combined videos: {len(combined_videos)}")
            
            for i, video in enumerate(combined_videos):
                print(f"  {i+1}. {video.get('video_file', 'N/A')}")
        else:
            print("No combined_videos in results")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_filename_fix()