@echo off
REM Quick build script for Vid2Frames Pro
REM This script provides a simple way to build the professional installer

echo.
echo ========================================
echo  Vid2Frames Pro - Quick Build Script
echo ========================================
echo.

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PowerShell is required but not found.
    echo Please ensure PowerShell is installed and available in PATH.
    pause
    exit /b 1
)

echo [1] Basic build (no code signing)
echo [2] Professional build with installer
echo [3] Full professional build with code signing
echo [4] Clean build directories
echo.

set /p choice="Select build type (1-4): "

if "%choice%"=="1" goto basic_build
if "%choice%"=="2" goto professional_build  
if "%choice%"=="3" goto signed_build
if "%choice%"=="4" goto clean_build

echo Invalid choice. Please select 1-4.
pause
exit /b 1

:basic_build
echo.
echo Starting basic build...
powershell -ExecutionPolicy Bypass -File "build_tools\build_professional.ps1" -SkipTests -CreateInstaller:$false
goto end

:professional_build
echo.
echo Starting professional build with installer...
powershell -ExecutionPolicy Bypass -File "build_tools\build_professional.ps1"
goto end

:signed_build
set /p cert_path="Enter path to code signing certificate: "
if not exist "%cert_path%" (
    echo Certificate file not found: %cert_path%
    pause
    exit /b 1
)
echo.
echo Starting signed professional build...
powershell -ExecutionPolicy Bypass -File "build_tools\build_professional.ps1" -SignCode -CertPath "%cert_path%"
goto end

:clean_build
echo.
echo Cleaning build directories...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
echo Build directories cleaned.
goto end

:end
echo.
echo Build process completed!
echo Check the 'dist' folder for output files.
echo.
pause