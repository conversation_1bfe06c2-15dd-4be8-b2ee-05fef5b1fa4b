<!DOCTYPE html>
<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Vid2Frames - Upload</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            colors: {
              primary: "#137fec",
              "background-light": "#f6f7f8",
              "background-dark": "#101922",
            },
            fontFamily: {
              display: ["Inter", "sans-serif"],
            },
            borderRadius: {
              DEFAULT: "0.25rem",
              lg: "0.5rem",
              xl: "0.75rem",
              full: "9999px",
            },
          },
        },
      };
    </script>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
</head>
<body class="bg-background-light dark:bg-background-dark font-display text-gray-800 dark:text-gray-200">
<div class="flex min-h-screen flex-col">
<header class="border-b border-gray-200/50 dark:border-gray-700/50">
<div class="mx-auto flex max-w-7xl items-center justify-between px-6 py-4">
<div class="flex items-center gap-4">
<div class="text-primary">
<svg class="h-8 w-8" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4h8v4H4zm6 6H4v4h6zm-6 6h8v4H4zM14 4h6v16h-6z"></path>
</svg>
</div>
<h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              Vid2Frames
            </h1>
</div>
<nav class="hidden items-center gap-8 md:flex">
<a class="text-sm font-medium text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors" href="#">Home</a>
<a class="text-sm font-medium text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors" href="#">Features</a>
<a class="text-sm font-medium text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors" href="#">Pricing</a>
<a class="text-sm font-medium text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-primary transition-colors" href="#">Support</a>
</nav>
<button class="hidden rounded-lg bg-primary px-5 py-2.5 text-sm font-semibold text-white transition-all hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background-light dark:focus:ring-offset-background-dark md:block">
            Upload Video
          </button>
<button class="md:hidden">
<span class="material-symbols-outlined">menu</span>
</button>
</div>
</header>
<main class="flex-grow">
<section class="mx-auto max-w-4xl px-6 py-12 md:py-24">
<div class="text-center">
<h2 class="text-4xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
              Upload Your Video
            </h2>
<p class="mt-4 text-lg text-gray-600 dark:text-gray-400">
              Drag and drop your video file here, or click to select a file from
              your computer. We support all major video formats.
            </p>
</div>
<div class="mt-12">
<div class="group relative flex cursor-pointer flex-col items-center justify-center rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-700 bg-white/10 dark:bg-black/10 px-6 py-16 text-center transition-all duration-300 hover:border-primary hover:bg-primary/5 dark:hover:bg-primary/10">
<div class="absolute inset-0 z-0 scale-95 opacity-0 transition-all duration-300 group-hover:scale-100 group-hover:opacity-100"></div>
<div class="relative z-10">
<span class="material-symbols-outlined text-6xl text-gray-400 dark:text-gray-500 group-hover:text-primary transition-colors duration-300">
                  cloud_upload
                </span>
<p class="mt-4 text-xl font-semibold text-gray-900 dark:text-white">
                  Drag and drop your video here
                </p>
<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  or
                </p>
<button class="mt-4 rounded-lg bg-primary px-5 py-2.5 text-sm font-semibold text-white transition-all hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background-light dark:focus:ring-offset-background-dark">
                  Select File
                </button>
</div>
<input class="absolute inset-0 h-full w-full cursor-pointer opacity-0" type="file"/>
</div>
</div>
</section>
</main>
<footer class="border-t border-gray-200/50 dark:border-gray-700/50">
<div class="mx-auto max-w-7xl px-6 py-8">
<div class="flex flex-col items-center justify-between gap-4 md:flex-row">
<div class="flex flex-wrap justify-center gap-x-6 gap-y-2 md:justify-start">
<a class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary transition-colors" href="#">Terms of Service</a>
<a class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary transition-colors" href="#">Privacy Policy</a>
<a class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary transition-colors" href="#">Contact Us</a>
</div>
<p class="text-sm text-gray-500 dark:text-gray-400">
              © 2024 Vid2Frames. All rights reserved.
            </p>
</div>
</div>
</footer>
</div>


</body></html>