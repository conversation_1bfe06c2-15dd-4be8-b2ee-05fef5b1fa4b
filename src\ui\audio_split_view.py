import flet as ft
from pathlib import Path
from typing import Optional, Callable
import threading
import tempfile
import json
from datetime import datetime

from ..core.audio_splitter import AudioSplitter
from ..core.audio_splitter_from_alignment import AudioSplitterFromAlignment
from ..core.smart_audio_splitter import SmartAudioSplitter, split_audio_smartly
from ..utils.file_manager import FileManager
from ..utils.config import config
from .progress_overlay import ProgressOverlay
from .universal_progress_view import OperationType


class AudioSplitView:
    """Audio splitting view for splitting audio files using text alignment"""
    
    def __init__(self):
        self.file_manager = FileManager()
        self.audio_splitter = AudioSplitter()
        self.alignment_splitter = AudioSplitterFromAlignment()
        self.smart_splitter = SmartAudioSplitter()
        self.page = None  # Will be set by main window
        
        # Selected files
        self.selected_audio_file = None
        self.selected_text_file = None
        self.selected_alignment_file = None
        
        # Processing mode: "text", "alignment", or "smart"
        self.processing_mode = "smart"  # Default to smart mode
        
        # File pickers
        self.audio_picker = ft.FilePicker(on_result=self.on_audio_picker_result)
        self.text_picker = ft.FilePicker(on_result=self.on_text_picker_result)
        self.alignment_picker = ft.FilePicker(on_result=self.on_alignment_picker_result)
        
        # Progress tracking
        self.is_processing = False
        self.progress_dialog = None
        self.progress_overlay = None  # Will be initialized when page is set
        
        # Settings controls
        self.output_format = ft.Dropdown(
            label="Output Format",
            value=config.processing.audio_output_format.lower(),
            options=[
                ft.dropdown.Option("wav", "WAV (Uncompressed)"),
                ft.dropdown.Option("mp3", "MP3 (Compressed)"),
                ft.dropdown.Option("flac", "FLAC (Lossless)"),
            ],
            width=200,
            on_change=self.on_format_change
        )
        
        self.similarity_threshold = ft.Slider(
            min=0.5,
            max=1.0,
            value=config.processing.audio_similarity_threshold,
            divisions=10,
            label="{value}",
            width=300,
            on_change=self.on_similarity_change
        )
        
        # Add text display for similarity threshold
        self.similarity_text = ft.Text(
            f"Text-Audio Similarity: {config.processing.audio_similarity_threshold:.1f}",
            size=14,
            weight=ft.FontWeight.W_500
        )
        
        self.buffer_duration = ft.Slider(
            min=0,
            max=500,
            value=config.processing.audio_buffer_ms,
            divisions=10,
            label="{value}",
            width=300,
            on_change=self.on_buffer_change
        )
        
        # Add text display for buffer duration  
        self.buffer_text = ft.Text(
            f"Audio Buffer: {config.processing.audio_buffer_ms}ms",
            size=14,
            weight=ft.FontWeight.W_500
        )
    
    def set_page(self, page):
        """Set page reference and initialize progress overlay"""
        self.page = page
        if page:
            self.progress_overlay = ProgressOverlay(page)
        
    def build(self):
        # Audio file selection
        self.audio_file_text = ft.Text(
            "No audio file selected",
            size=14,
            color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        self.audio_upload_area = ft.Container(
            content=ft.Column([
                ft.Icon(
                    ft.Icons.AUDIOTRACK_OUTLINED,
                    size=48,
                    color=ft.Colors.PRIMARY
                ),
                ft.Text(
                    "Select Audio File",
                    size=16,
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.PRIMARY
                ),
                ft.Text(
                    "Supported formats: MP3, WAV, M4A, FLAC, OGG",
                    size=12,
                    color=ft.Colors.ON_SURFACE_VARIANT
                ),
                ft.ElevatedButton(
                    "Browse Audio Files",
                    icon=ft.Icons.AUDIOTRACK,
                    on_click=self.open_audio_picker,
                    style=ft.ButtonStyle(
                        bgcolor=ft.Colors.PRIMARY,
                        color=ft.Colors.WHITE,
                    )
                ),
                self.audio_file_text
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=16
            ),
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=12,
            padding=40,
            margin=20,
            border=ft.border.all(2, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            ink=True,
            on_click=self.open_audio_picker,
        )
        
        # Mode selection
        self.mode_selector = ft.RadioGroup(
            content=ft.Column([
                ft.Radio(value="smart", label="🧠 Smart Mode (Silence Detection + Word Boundaries)"),
                ft.Radio(value="text", label="📝 Text Mode (Basic AI Alignment)"),
                ft.Radio(value="alignment", label="📊 JSON Mode (Pre-aligned Data)")
            ]),
            value="smart",
            on_change=self.on_mode_change
        )
        
        # Text file selection
        self.text_file_text = ft.Text(
            "No text file selected",
            size=14,
            color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        self.text_upload_area = ft.Container(
            content=ft.Column([
                ft.Icon(
                    ft.Icons.TEXT_SNIPPET_OUTLINED,
                    size=48,
                    color=ft.Colors.SECONDARY
                ),
                ft.Text(
                    "Select Text File",
                    size=16,
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.SECONDARY
                ),
                ft.Text(
                    "One sentence per line (TXT format)",
                    size=12,
                    color=ft.Colors.ON_SURFACE_VARIANT
                ),
                ft.ElevatedButton(
                    "Browse Text Files",
                    icon=ft.Icons.TEXT_SNIPPET,
                    on_click=self.open_text_picker,
                    style=ft.ButtonStyle(
                        bgcolor=ft.Colors.SECONDARY,
                        color=ft.Colors.WHITE,
                    )
                ),
                self.text_file_text
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=16
            ),
            visible=True,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=12,
            padding=40,
            margin=20,
            border=ft.border.all(2, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            ink=True,
            on_click=self.open_text_picker,
        )
        
        # Alignment file selection (for JSON mode)
        self.alignment_file_text = ft.Text(
            "No alignment file selected",
            size=14,
            color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        self.alignment_upload_area = ft.Container(
            content=ft.Column([
                ft.Icon(
                    ft.Icons.DATA_OBJECT_OUTLINED,
                    size=48,
                    color=ft.Colors.TERTIARY
                ),
                ft.Text(
                    "Select Alignment File",
                    size=16,
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.TERTIARY
                ),
                ft.Text(
                    "JSON format with timing data (alignment_info.json)",
                    size=12,
                    color=ft.Colors.ON_SURFACE_VARIANT
                ),
                ft.ElevatedButton(
                    "Browse Alignment Files",
                    icon=ft.Icons.DATA_OBJECT,
                    on_click=self.open_alignment_picker,
                    style=ft.ButtonStyle(
                        bgcolor=ft.Colors.TERTIARY,
                        color=ft.Colors.WHITE,
                    )
                ),
                self.alignment_file_text
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=16
            ),
            visible=False,  # Hidden by default
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=12,
            padding=40,
            margin=20,
            border=ft.border.all(2, ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300),
            ink=True,
            on_click=self.open_alignment_picker,
        )
        
        # Process button
        self.process_button = ft.ElevatedButton(
            text="Split Audio",
            icon=ft.Icons.CONTENT_CUT,
            on_click=self.start_audio_splitting,
            disabled=True,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.GREEN,
                color=ft.Colors.WHITE,
                shape=ft.RoundedRectangleBorder(radius=8)
            ),
            height=48,
            width=200
        )
        
        # Status text
        self.status_text = ft.Text(
            "Select both audio and text files to begin",
            size=14,
            color=ft.Colors.ON_SURFACE_VARIANT,
            text_align=ft.TextAlign.CENTER
        )
        
        # Main layout
        return ft.Container(
            content=ft.Column([
                # Header
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "Audio Splitter",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.PRIMARY
                        ),
                        ft.Text(
                            "Split audio files using smart boundary detection, AI transcription, or pre-aligned JSON data",
                            size=14,
                            color=ft.Colors.ON_SURFACE_VARIANT
                        )
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                    ),
                    margin=ft.margin.only(bottom=20)
                ),
                
                # Mode selection
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "Processing Mode",
                            size=16,
                            weight=ft.FontWeight.W_600,
                            color=ft.Colors.ON_SURFACE
                        ),
                        self.mode_selector
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=10
                    ),
                    margin=ft.margin.only(bottom=20)
                ),
                
                # File selection areas
                ft.Row([
                    ft.Container(self.audio_upload_area, expand=True),
                    ft.Container(self.text_upload_area, expand=True),
                ]),
                
                # Alignment file selection (conditional)
                ft.Container(self.alignment_upload_area),
                
                # Settings
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "Settings",
                            size=18,
                            weight=ft.FontWeight.W_600,
                            color=ft.Colors.ON_SURFACE
                        ),
                        ft.Row([
                            self.output_format,
                            ft.Column([
                                ft.Text(
                                    "Text-Audio Similarity Threshold",
                                    size=12,
                                    color=ft.Colors.ON_SURFACE_VARIANT
                                ),
                                self.similarity_text,
                                self.similarity_threshold
                            ], spacing=5),
                            ft.Column([
                                ft.Text(
                                    "Audio Buffer (Start/End Padding)",
                                    size=12,
                                    color=ft.Colors.ON_SURFACE_VARIANT
                                ),
                                self.buffer_text,
                                self.buffer_duration
                            ], spacing=5)
                        ], spacing=30, alignment=ft.MainAxisAlignment.CENTER)
                    ], spacing=15),
                    margin=ft.margin.symmetric(vertical=20),
                    padding=20,
                    bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
                    border_radius=12
                ),
                
                # Action area
                ft.Container(
                    content=ft.Column([
                        self.process_button,
                        self.status_text
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=15
                    ),
                    margin=ft.margin.symmetric(vertical=20)
                ),
                
                # Hidden file pickers
                self.audio_picker,
                self.text_picker
            ],
            spacing=0,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            scroll=ft.ScrollMode.AUTO
            ),
            expand=True,
            padding=20
        )
    
    def open_audio_picker(self, e=None):
        """Open audio file picker"""
        if self.page:
            self.page.overlay.append(self.audio_picker)
            self.page.update()
            
        self.audio_picker.pick_files(
            dialog_title="Select Audio File",
            allowed_extensions=["mp3", "wav", "m4a", "flac", "ogg", "aac", "wma"],
            allow_multiple=False
        )
    
    def open_text_picker(self, e=None):
        """Open text file picker"""
        if self.page:
            self.page.overlay.append(self.text_picker)
            self.page.update()
            
        self.text_picker.pick_files(
            dialog_title="Select Text File",
            allowed_extensions=["txt"],
            allow_multiple=False
        )
    
    def on_audio_picker_result(self, e: ft.FilePickerResultEvent):
        """Handle audio file selection"""
        if e.files:
            file_path = Path(e.files[0].path)
            if self.validate_audio_file(file_path):
                self.selected_audio_file = file_path
                self.audio_file_text.value = f"✓ {file_path.name}"
                self.audio_file_text.color = ft.Colors.GREEN
                self.update_ui()
    
    def on_text_picker_result(self, e: ft.FilePickerResultEvent):
        """Handle text file selection"""
        if e.files:
            file_path = Path(e.files[0].path)
            if self.validate_text_file(file_path):
                self.selected_text_file = file_path
                self.text_file_text.value = f"✓ {file_path.name}"
                self.text_file_text.color = ft.Colors.GREEN
                self.update_ui()
    
    def validate_audio_file(self, file_path: Path) -> bool:
        """Validate audio file"""
        if not file_path.exists():
            self.show_error("Audio file does not exist")
            return False
            
        if file_path.suffix.lower() not in ['.mp3', '.wav', '.m4a', '.flac', '.ogg', '.aac', '.wma']:
            self.show_error("Unsupported audio format")
            return False
            
        if file_path.stat().st_size == 0:
            self.show_error("Audio file is empty")
            return False
            
        return True
    
    def validate_text_file(self, file_path: Path) -> bool:
        """Validate text file"""
        if not file_path.exists():
            self.show_error("Text file does not exist")
            return False
            
        if file_path.suffix.lower() != '.txt':
            self.show_error("Only .txt files are supported")
            return False
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if len(lines) == 0:
                    self.show_error("Text file is empty")
                    return False
                    
                # Check for reasonable content
                non_empty_lines = [line.strip() for line in lines if line.strip()]
                if len(non_empty_lines) == 0:
                    self.show_error("Text file contains no text content")
                    return False
                    
        except Exception as e:
            self.show_error(f"Error reading text file: {str(e)}")
            return False
            
        return True
    
    def open_alignment_picker(self, e=None):
        """Open alignment file picker"""
        if not hasattr(self, 'alignment_picker'):
            return
        if self.page and self.alignment_picker not in self.page.overlay:
            self.page.overlay.append(self.alignment_picker)
            self.page.update()
        self.alignment_picker.pick_files(
            dialog_title="Select Alignment JSON File",
            allowed_extensions=["json"],
            allow_multiple=False
        )
    
    def on_alignment_picker_result(self, e: ft.FilePickerResultEvent):
        """Handle alignment file selection"""
        if e.files:
            file_path = Path(e.files[0].path)
            if self.validate_alignment_file(file_path):
                self.selected_alignment_file = file_path
                self.alignment_file_text.value = f"✓ {file_path.name}"
                self.alignment_file_text.color = ft.Colors.GREEN
                print(f"✅ Alignment file selected: {file_path}")
            else:
                # Validation failed - clear selection
                self.selected_alignment_file = None
                self.alignment_file_text.value = "❌ Invalid alignment file"
                self.alignment_file_text.color = ft.Colors.RED
                print(f"❌ Alignment file validation failed: {file_path}")
        else:
            # No files selected - clear selection
            self.selected_alignment_file = None
            self.alignment_file_text.value = "No alignment file selected"
            self.alignment_file_text.color = ft.Colors.GREY
            print("📄 No alignment file selected")
        
        self.update_ui()
    
    def validate_alignment_file(self, file_path: Path) -> bool:
        """Validate alignment JSON file"""
        if not file_path.exists():
            self.show_error("Alignment file does not exist")
            return False
            
        if file_path.suffix.lower() != '.json':
            self.show_error("Only .json files are supported for alignment data")
            return False
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                alignment_data = json.load(f)
                
                if not isinstance(alignment_data, list):
                    self.show_error("Alignment file must contain a list of segments")
                    return False
                
                if len(alignment_data) == 0:
                    self.show_error("Alignment file is empty")
                    return False
                
                # Validate first segment structure
                first_segment = alignment_data[0]
                required_fields = ['start_time', 'end_time', 'text']
                for field in required_fields:
                    if field not in first_segment:
                        self.show_error(f"Alignment data missing required field: {field}")
                        return False
                        
        except json.JSONDecodeError:
            self.show_error("Invalid JSON format in alignment file")
            return False
        except Exception as e:
            self.show_error(f"Error reading alignment file: {str(e)}")
            return False
            
        return True
    
    def on_mode_change(self, e):
        """Handle mode selection change"""
        old_mode = getattr(self, 'processing_mode', 'smart')
        self.processing_mode = e.control.value
        print(f"🔧 Mode changed from '{old_mode}' to '{self.processing_mode}'")
        
        # Toggle visibility of upload areas based on mode
        self.text_upload_area.visible = self.processing_mode in ["text", "smart"]
        self.alignment_upload_area.visible = self.processing_mode == "alignment"
        
        print(f"📄 Text area visible: {self.text_upload_area.visible}")
        print(f"📊 Alignment area visible: {self.alignment_upload_area.visible}")
        
        # Update process button text
        if self.processing_mode == "smart":
            self.process_button.text = "🧠 Smart Split Audio"
        elif self.processing_mode == "alignment":
            self.process_button.text = "📊 Split Audio (JSON Mode)"
        else:
            self.process_button.text = "📝 Split Audio (Basic Mode)"
        
        # Update UI state
        self.update_ui()
        
        if self.page:
            print("🔄 Updating page...")
            self.page.update()
        else:
            print("⚠️ No page reference available")
    
    def update_ui(self):
        """Update UI state based on selections"""
        print(f"🔧 Updating UI for mode: {self.processing_mode}")
        print(f"📁 Audio file: {self.selected_audio_file}")
        print(f"📄 Text file: {self.selected_text_file}")
        print(f"📊 Alignment file: {self.selected_alignment_file}")
        
        if self.processing_mode == "alignment":
            # JSON alignment mode
            if self.selected_audio_file and self.selected_alignment_file:
                self.process_button.disabled = False
                self.status_text.value = "Ready to split audio using alignment data"
                self.status_text.color = ft.Colors.GREEN
                print("✅ JSON mode ready")
            else:
                self.process_button.disabled = True
                missing = []
                if not self.selected_audio_file:
                    missing.append("audio file")
                if not self.selected_alignment_file:
                    missing.append("alignment file")
                self.status_text.value = f"Select {' and '.join(missing)} to begin"
                self.status_text.color = ft.Colors.ON_SURFACE_VARIANT
                print(f"❌ JSON mode missing: {missing}")
        else:
            # Text/Smart modes (both need audio + text files)
            if self.selected_audio_file and self.selected_text_file:
                self.process_button.disabled = False
                if self.processing_mode == "smart":
                    self.status_text.value = "Ready for smart audio splitting (silence detection + word boundaries)"
                else:
                    self.status_text.value = "Ready to split audio (basic mode)"
                self.status_text.color = ft.Colors.GREEN
                print(f"✅ {self.processing_mode} mode ready")
            else:
                self.process_button.disabled = True
                missing = []
                if not self.selected_audio_file:
                    missing.append("audio file")
                if not self.selected_text_file:
                    missing.append("text file")
                self.status_text.value = f"Select {' and '.join(missing)} to begin"
            self.status_text.color = ft.Colors.ON_SURFACE_VARIANT
        
        if self.page:
            self.page.update()
    
    def on_similarity_change(self, e):
        """Handle similarity threshold change and save to config"""
        new_threshold = float(e.control.value)
        config.processing.audio_similarity_threshold = new_threshold
        config.save_settings()
        
        # Update the text display
        self.similarity_text.value = f"Text-Audio Similarity: {new_threshold:.1f}"
        if self.page:
            self.page.update()
        
        # Update audio splitter if it exists
        if hasattr(self, 'audio_splitter') and self.audio_splitter:
            self.audio_splitter.similarity_threshold = new_threshold
    
    def on_buffer_change(self, e):
        """Handle buffer duration change and save to config"""
        new_buffer = int(e.control.value)
        config.processing.audio_buffer_ms = new_buffer
        config.save_settings()
        
        # Update the text display
        self.buffer_text.value = f"Audio Buffer: {new_buffer}ms"
        if self.page:
            self.page.update()
        
        # Update audio splitter if it exists
        if hasattr(self, 'audio_splitter') and self.audio_splitter:
            self.audio_splitter.buffer_ms = new_buffer
    
    def on_format_change(self, e):
        """Handle output format change and save to config"""
        new_format = str(e.control.value).upper()
        config.processing.audio_output_format = new_format
        config.save_settings()
        
        # Update audio splitter if it exists
        if hasattr(self, 'audio_splitter') and self.audio_splitter:
            self.audio_splitter.output_format = new_format
    
    def start_audio_splitting(self, e=None):
        """Start the audio splitting process using universal progress view"""
        print("🎯 Starting audio splitting process...")
        
        if self.is_processing:
            print("⚠️ Already processing, ignoring request")
            return
        
        # Validate files based on processing mode
        if not self.selected_audio_file:
            print("❌ Missing audio file - showing error")
            self.show_error("Please select an audio file")
            return
            
        if self.processing_mode == "alignment":
            # JSON mode needs audio + alignment file
            if not self.selected_alignment_file:
                print("❌ Missing alignment file - showing error")
                self.show_error("Please select an alignment JSON file")
                return
            print(f"📁 Audio file: {self.selected_audio_file}")
            print(f"📊 Alignment file: {self.selected_alignment_file}")
        else:
            # Text and Smart modes need audio + text file
            if not self.selected_text_file:
                print("❌ Missing text file - showing error")
                self.show_error("Please select a text file")
                return
            print(f"📁 Audio file: {self.selected_audio_file}")
            print(f"📄 Text file: {self.selected_text_file}")
        
        print(f"🔧 Processing mode: {self.processing_mode}")
        self.is_processing = True
        
        # Switch to progress tab and start processing
        self._switch_to_progress_tab()
    
    def _switch_to_progress_tab(self):
        """Switch to the progress tab to show audio processing"""
        # Get reference to main window through page
        if hasattr(self.page, 'main_window'):
            main_window = self.page.main_window
            main_window.show_progress_view()
            
            # Start audio processing using universal progress view
            progress_view = main_window.progress_view
            if progress_view:
                # Configure for audio splitting based on processing mode
                config = {
                    'audio_file': self.selected_audio_file,
                    'processing_mode': self.processing_mode,
                    'similarity_threshold': self.similarity_threshold.value,
                    'output_format': self.output_format.value,
                    'buffer_ms': self.buffer_duration.value
                }
                
                # Add mode-specific file
                if self.processing_mode == "alignment":
                    config['alignment_file'] = self.selected_alignment_file
                else:
                    config['text_file'] = self.selected_text_file
                
                print(f"🔧 Universal progress config: {config}")
                
                # Start audio splitting operation
                progress_view.start_operation(
                    OperationType.AUDIO_SPLITTING,
                    self.audio_splitter,
                    config
                )
                
                print("✅ Started audio splitting with universal progress view")
            else:
                print("⚠️ Progress view not found")
        else:
            print("⚠️ Cannot switch to progress tab - main_window reference not found")
    
    async def _start_background_processing(self):
        """This method is no longer needed - processing handled by universal progress view"""
        pass
    
    def process_audio_splitting(self):
        """Process audio splitting in background thread"""
        try:
            # Set output directory with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            mode_suffix = {"smart": "smart", "text": "basic", "alignment": "aligned"}[self.processing_mode]
            output_dir = self.selected_audio_file.parent / f"{self.selected_audio_file.stem}_{mode_suffix}_split_{timestamp}"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            if self.processing_mode == "alignment":
                # JSON alignment mode
                self.update_progress("Using pre-aligned JSON data...", 0)
                
                # Additional safety check for file paths
                if not self.selected_audio_file or not self.selected_alignment_file:
                    raise Exception("Audio file or alignment file path is None")
                
                if not isinstance(self.selected_audio_file, Path) or not isinstance(self.selected_alignment_file, Path):
                    raise Exception("File paths must be Path objects")
                
                if not self.selected_audio_file.exists():
                    raise Exception(f"Audio file does not exist: {self.selected_audio_file}")
                
                if not self.selected_alignment_file.exists():
                    raise Exception(f"Alignment file does not exist: {self.selected_alignment_file}")
                
                print(f"✅ Validated paths:")
                print(f"   Audio: {self.selected_audio_file}")
                print(f"   Alignment: {self.selected_alignment_file}")
                
                config = {
                    'output_dir': output_dir,
                    'output_format': self.output_format.value,
                    'buffer_ms': self.buffer_duration.value
                }
                
                success = self.alignment_splitter.split_audio_from_alignment(
                    audio_path=self.selected_audio_file,
                    alignment_json_path=self.selected_alignment_file,
                    config=config,
                    progress_callback=self.update_progress
                )
                
                if success:
                    # Count segments from alignment file
                    with open(self.selected_alignment_file, 'r', encoding='utf-8') as f:
                        alignment_data = json.load(f)
                    self.show_completion(output_dir, len(alignment_data))
                else:
                    self.show_error("Failed to split audio using alignment data")
                    
            else:
                # Text-based modes (smart or basic)
                # Read text file
                with open(self.selected_text_file, 'r', encoding='utf-8') as f:
                    sentences = [line.strip() for line in f.readlines() if line.strip()]
                
                if not sentences:
                    self.show_error("No sentences found in text file")
                    return
                
                if self.processing_mode == "smart":
                    # Smart mode with silence detection and word boundaries
                    self.update_progress("🧠 Initializing smart audio splitter...", 0)
                    
                    success = split_audio_smartly(
                        audio_path=self.selected_audio_file,
                        text_file_path=self.selected_text_file,
                        output_dir=output_dir,
                        similarity_threshold=self.similarity_threshold.value,
                        output_format=self.output_format.value,
                        silence_threshold=0.01,  # Could make this configurable
                        progress_callback=self.update_progress
                    )
                    
                    if success:
                        self.show_completion(output_dir, len(sentences), mode="smart")
                    else:
                        self.show_error("Smart audio splitting failed")
                        
                else:
                    # Basic text alignment mode
                    self.update_progress("Preparing basic audio splitter...", 0)
                    
                    config = {
                        'similarity_threshold': self.similarity_threshold.value,
                        'output_format': self.output_format.value,
                        'output_dir': output_dir,
                        'buffer_ms': self.buffer_duration.value
                    }
                    
                    success = self.audio_splitter.split_audio_by_text_alignment(
                        audio_path=self.selected_audio_file,
                        sentences=sentences,
                        config=config,
                        progress_callback=self.update_progress
                    )
                    
                    if success:
                        self.show_completion(output_dir, len(sentences), mode="basic")
                    else:
                        self.show_error("Basic audio splitting failed")
            
        except Exception as e:
            self.show_error(f"Error processing audio: {str(e)}")
        finally:
            self.is_processing = False
            self.close_progress_dialog()
    
    def update_progress(self, message: str, progress: float):
        """Update progress using the progress tab - thread-safe"""
        print(f"📊 Progress: {message} ({progress:.1%})")
        
        # Schedule UI update on the main thread using page.run_task
        self.page.run_task(self._update_progress_ui, message, progress)
    
    async def _update_progress_ui(self, message: str, progress: float):
        """Update progress UI on the main thread using progress tab"""
        try:
            # Get reference to main window and progress view
            if hasattr(self.page, 'main_window'):
                main_window = self.page.main_window
                progress_view = main_window.progress_view
                
                if progress_view:
                    # Update progress view with audio splitting info
                    progress_view.overall_progress.value = progress
                    progress_view.progress_text.value = f"🎵 {message}"
                    self.page.update()
                    print(f"✅ Progress tab updated: {message}")
                else:
                    print("⚠️ Progress view not found")
            else:
                print("⚠️ Main window reference not found")
        except Exception as e:
            print(f"Progress update error: {e}")
    
    def show_progress_dialog(self):
        """Show progress overlay instead of dialog"""
        print("🔄 Showing progress overlay...")
        
        if self.progress_overlay:
            self.progress_overlay.show()
        else:
            print("❌ ERROR: progress_overlay is None - page not set properly!")
    
    def _cancel_processing(self, e):
        """Cancel the processing"""
        print("🛑 User cancelled processing")
        self.is_processing = False
        if self.progress_overlay:
            self.progress_overlay.hide()
    
    def close_progress_dialog(self):
        """Return to audio split tab when processing is complete"""
        print("🔄 Audio processing complete - returning to audio split tab...")
        
        # Schedule tab switch on the main thread
        self.page.run_task(self._return_to_audio_tab)
    
    async def _return_to_audio_tab(self):
        """Return to audio split tab on the main thread"""
        try:
            if hasattr(self.page, 'main_window'):
                main_window = self.page.main_window
                main_window.show_audio_split_view()
                print("✅ Returned to audio split tab")
            else:
                print("⚠️ Cannot return to audio tab - main_window reference not found")
        except Exception as e:
            print(f"Error returning to audio tab: {e}")
    
    def show_completion(self, output_dir: Path, segments_count: int, mode: str = "basic"):
        """Show completion dialog - thread-safe"""
        # Schedule UI update on the main thread
        self.page.run_task(self._show_completion_ui, output_dir, segments_count, mode)
    
    async def _show_completion_ui(self, output_dir: Path, segments_count: int, mode: str = "basic"):
        """Show completion dialog on the main thread"""
        try:
            # Customize message based on mode
            mode_messages = {
                "smart": "🧠 Smart audio splitting complete with silence detection and word boundaries!",
                "basic": "📝 Basic audio splitting complete!",
                "alignment": "📊 JSON alignment-based splitting complete!"
            }
            
            mode_details = {
                "smart": "Features used: Silence detection, word-level boundaries, natural pause detection",
                "basic": "Basic AI alignment with Whisper transcription",
                "alignment": "Used pre-existing alignment data for precise timing"
            }
            
            completion_dialog = ft.AlertDialog(
                title=ft.Text(mode_messages.get(mode, "Audio Splitting Complete!"), color=ft.Colors.GREEN),
                content=ft.Column([
                    ft.Text(f"Successfully created {segments_count} audio segments"),
                    ft.Text(mode_details.get(mode, ""), size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                    ft.Text(f"Output directory: {output_dir}"),
                    ft.ElevatedButton(
                        "Open Output Folder",
                        icon=ft.Icons.FOLDER_OPEN,
                        on_click=lambda e: self.open_folder(output_dir)
                    )
                ], height=140, spacing=15),
                actions=[
                    ft.TextButton("OK", on_click=lambda e: self.close_completion_dialog())
                ]
            )
            
            self.page.dialog = completion_dialog
            completion_dialog.open = True
            self.page.update()
        except Exception as e:
            print(f"Error showing completion dialog: {e}")
    
    def close_completion_dialog(self):
        """Close completion dialog"""
        if self.page and self.page.dialog:
            self.page.dialog.open = False
            self.page.update()
    
    def show_error(self, message: str):
        """Show error dialog - thread-safe"""
        # Schedule UI update on the main thread
        self.page.run_task(self._show_error_ui, message)
    
    async def _show_error_ui(self, message: str):
        """Show error dialog on the main thread"""
        try:
            error_dialog = ft.AlertDialog(
                title=ft.Text("Error", color=ft.Colors.RED),
                content=ft.Text(message),
                actions=[
                    ft.TextButton("OK", on_click=lambda e: self.close_error_dialog())
                ]
            )
            
            self.page.dialog = error_dialog
            error_dialog.open = True
            self.page.update()
        except Exception as e:
            print(f"Error showing error dialog: {e}")
    
    def close_error_dialog(self):
        """Close error dialog"""
        if self.page and self.page.dialog:
            self.page.dialog.open = False
            self.page.update()
    
    def open_folder(self, folder_path: Path):
        """Open folder in file explorer"""
        import subprocess
        import sys
        
        try:
            if sys.platform == "win32":
                subprocess.run(["explorer", str(folder_path)], check=True)
            elif sys.platform == "darwin":
                subprocess.run(["open", str(folder_path)], check=True)
            else:
                subprocess.run(["xdg-open", str(folder_path)], check=True)
        except Exception as e:
            print(f"Could not open folder: {e}")