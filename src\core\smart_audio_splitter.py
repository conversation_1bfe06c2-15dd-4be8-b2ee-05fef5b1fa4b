"""
Smart Audio Splitter with Silence Detection and Word-Level Boundaries
This version ensures clean cuts at natural pause points between words
"""
import numpy as np
import librosa
import soundfile as sf
from pathlib import Path
from typing import List, Dict, Any, Callable, Optional, Tuple
import json
import re
import subprocess
from datetime import datetime

from ..utils.config import config

try:
    import torch
    import whisper
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False


class SmartAudioSegment:
    """Represents a smart audio segment with word-level boundaries and silence detection"""
    def __init__(self, start_time: float, end_time: float, text: str, 
                 words: List[Dict], confidence: float = 0.0):
        self.start_time = start_time
        self.end_time = end_time
        self.text = text
        self.words = words  # Word-level timing data
        self.confidence = confidence
        self.duration = end_time - start_time
        self.adjusted_start = start_time  # Will be adjusted for silence
        self.adjusted_end = end_time      # Will be adjusted for silence
    
    def __str__(self):
        return f"[{self.adjusted_start:.2f}s - {self.adjusted_end:.2f}s]: {self.text}"


class SmartAudioSplitter:
    """Handles smart audio splitting with silence detection and word boundaries"""
    
    def __init__(self):
        self.whisper_model = None
        self.device = self._select_device()
        
        # Audio analysis parameters
        self.silence_threshold = 0.01  # Amplitude threshold for silence detection
        self.min_silence_duration = 0.1  # Minimum silence duration in seconds
        self.word_boundary_buffer = 0.05  # Buffer around word boundaries
        
        # Load settings from config
        self.similarity_threshold = config.processing.audio_similarity_threshold
        self.output_format = config.processing.audio_output_format
        self.buffer_ms = config.processing.audio_buffer_ms
        
        if not AUDIO_PROCESSING_AVAILABLE:
            raise ImportError("Audio processing libraries are required (torch, whisper, librosa, soundfile)")
    
    def _select_device(self) -> str:
        """Select the best available device for processing"""
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"
        else:
            return "cpu"
    
    def load_whisper_model(self, model_size: str = "base") -> bool:
        """Load Whisper model for transcription"""
        try:
            print(f"Loading Whisper model '{model_size}' on {self.device}...")
            self.whisper_model = whisper.load_model(model_size, device=self.device)
            print(f"✅ Whisper model loaded successfully on {self.device}")
            return True
        except Exception as e:
            print(f"❌ Error loading Whisper model: {e}")
            return False
    
    def detect_silence_regions(self, audio_path: Path, 
                             silence_threshold: float = None,
                             min_silence_duration: float = None) -> List[Tuple[float, float]]:
        """Detect silence regions in the audio for clean cut points"""
        if silence_threshold is None:
            silence_threshold = self.silence_threshold
        if min_silence_duration is None:
            min_silence_duration = self.min_silence_duration
        
        try:
            # Load audio with librosa
            audio, sr = librosa.load(str(audio_path), sr=None)
            
            # Calculate RMS energy in small windows
            hop_length = 512
            frame_length = 2048
            rms = librosa.feature.rms(y=audio, frame_length=frame_length, hop_length=hop_length)[0]
            
            # Convert frame indices to time
            times = librosa.frames_to_time(np.arange(len(rms)), sr=sr, hop_length=hop_length)
            
            # Find silence regions
            silent_frames = rms < silence_threshold
            silence_regions = []
            
            in_silence = False
            silence_start = 0
            
            for i, (time, is_silent) in enumerate(zip(times, silent_frames)):
                if is_silent and not in_silence:
                    # Start of silence region
                    silence_start = time
                    in_silence = True
                elif not is_silent and in_silence:
                    # End of silence region
                    silence_duration = time - silence_start
                    if silence_duration >= min_silence_duration:
                        silence_regions.append((silence_start, time))
                    in_silence = False
            
            # Handle case where audio ends in silence
            if in_silence:
                silence_duration = times[-1] - silence_start
                if silence_duration >= min_silence_duration:
                    silence_regions.append((silence_start, times[-1]))
            
            print(f"🔇 Found {len(silence_regions)} silence regions")
            return silence_regions
            
        except Exception as e:
            print(f"❌ Error detecting silence: {e}")
            return []
    
    def transcribe_with_word_timestamps(self, audio_path: Path) -> List[Dict]:
        """Transcribe audio with word-level timestamps"""
        if not self.whisper_model:
            if not self.load_whisper_model():
                raise Exception("Failed to load Whisper model")
        
        try:
            print(f"🎙️ Transcribing audio with word-level timestamps: {audio_path.name}")
            
            # Use Whisper with word-level timestamps
            result = self.whisper_model.transcribe(
                str(audio_path), 
                word_timestamps=True,
                verbose=False
            )
            
            segments = []
            for segment in result["segments"]:
                # Get word-level timing data
                words = segment.get("words", [])
                segment_data = {
                    "start": segment["start"],
                    "end": segment["end"],
                    "text": segment["text"].strip(),
                    "words": words
                }
                segments.append(segment_data)
            
            print(f"✅ Transcription complete: {len(segments)} segments with word-level timing")
            return segments
            
        except Exception as e:
            print(f"❌ Error transcribing audio: {e}")
            return []
    
    def find_optimal_cut_point(self, target_time: float, silence_regions: List[Tuple[float, float]], 
                             search_window: float = 2.0) -> float:
        """Find the optimal cut point near the target time using silence detection"""
        
        # Look for silence regions within the search window
        candidates = []
        for silence_start, silence_end in silence_regions:
            silence_center = (silence_start + silence_end) / 2
            distance = abs(silence_center - target_time)
            
            if distance <= search_window:
                candidates.append((distance, silence_center, silence_end - silence_start))
        
        if candidates:
            # Sort by distance, then by silence duration (prefer longer silences)
            candidates.sort(key=lambda x: (x[0], -x[2]))
            optimal_time = candidates[0][1]
            print(f"  🎯 Found optimal cut at {optimal_time:.2f}s (silence region)")
            return optimal_time
        else:
            # No silence found, use target time
            print(f"  ⚠️ No silence found near {target_time:.2f}s, using target time")
            return target_time
    
    def align_sentences_with_smart_boundaries(self, transcription_segments: List[Dict], 
                                           target_sentences: List[str],
                                           silence_regions: List[Tuple[float, float]],
                                           similarity_threshold: float = 0.6) -> List[SmartAudioSegment]:
        """Align sentences with transcription using smart boundary detection"""
        
        print(f"🧠 Smart alignment of {len(target_sentences)} sentences with {len(transcription_segments)} segments")
        
        aligned_segments = []
        used_segments = set()
        
        for i, sentence in enumerate(target_sentences):
            print(f"Processing sentence {i+1}/{len(target_sentences)}: '{sentence[:50]}...'")
            
            best_match = None
            best_similarity = 0
            best_segment_range = None
            best_words = []
            
            # Try to find the best matching segment(s) for this sentence
            for start_idx in range(len(transcription_segments)):
                if start_idx in used_segments:
                    continue
                    
                # Try different combinations of consecutive segments
                for end_idx in range(start_idx, min(start_idx + 3, len(transcription_segments))):
                    if any(idx in used_segments for idx in range(start_idx, end_idx + 1)):
                        continue
                    
                    # Combine segments and words in this range
                    combined_text = " ".join([
                        transcription_segments[idx]["text"] 
                        for idx in range(start_idx, end_idx + 1)
                    ])
                    
                    combined_words = []
                    for idx in range(start_idx, end_idx + 1):
                        combined_words.extend(transcription_segments[idx].get("words", []))
                    
                    # Calculate similarity
                    similarity = self._calculate_text_similarity(sentence, combined_text)
                    
                    if similarity > best_similarity and similarity >= similarity_threshold:
                        best_similarity = similarity
                        best_match = combined_text
                        best_segment_range = (start_idx, end_idx)
                        best_words = combined_words
            
            if best_match and best_segment_range:
                # Mark segments as used
                for idx in range(best_segment_range[0], best_segment_range[1] + 1):
                    used_segments.add(idx)
                
                # Get initial timing from the segment range
                initial_start = transcription_segments[best_segment_range[0]]["start"]
                initial_end = transcription_segments[best_segment_range[1]]["end"]
                
                # Find optimal cut points using silence detection
                if i == 0:
                    # First segment: start from beginning or find silence before first word
                    optimal_start = 0.0
                else:
                    optimal_start = self.find_optimal_cut_point(initial_start, silence_regions)
                
                # Find optimal end point
                optimal_end = self.find_optimal_cut_point(initial_end, silence_regions)
                
                # Ensure we don't cut words in half by using word boundaries
                if best_words:
                    # Adjust start to word boundary
                    for word in best_words:
                        if word.get("start", 0) >= optimal_start - self.word_boundary_buffer:
                            optimal_start = max(0, word.get("start", 0) - self.word_boundary_buffer)
                            break
                    
                    # Adjust end to word boundary
                    for word in reversed(best_words):
                        if word.get("end", optimal_end) <= optimal_end + self.word_boundary_buffer:
                            optimal_end = word.get("end", optimal_end) + self.word_boundary_buffer
                            break
                
                smart_segment = SmartAudioSegment(
                    start_time=initial_start,
                    end_time=initial_end,
                    text=sentence,
                    words=best_words,
                    confidence=best_similarity
                )
                smart_segment.adjusted_start = optimal_start
                smart_segment.adjusted_end = optimal_end
                
                aligned_segments.append(smart_segment)
                
                print(f"  ✅ Matched with similarity {best_similarity:.2f}")
                print(f"      Original: {initial_start:.2f}s - {initial_end:.2f}s")
                print(f"      Smart cut: {optimal_start:.2f}s - {optimal_end:.2f}s")
            else:
                print(f"  ⚠️ No match found for sentence (similarity < {similarity_threshold})")
                # Could create estimated segment here if needed
        
        print(f"✅ Smart alignment complete: {len(aligned_segments)} segments aligned")
        return aligned_segments
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings"""
        from difflib import SequenceMatcher
        
        # Normalize texts
        text1_norm = re.sub(r'[^\w\s]', '', text1.lower())
        text2_norm = re.sub(r'[^\w\s]', '', text2.lower())
        
        return SequenceMatcher(None, text1_norm, text2_norm).ratio()
    
    def extract_smart_audio_segment(self, audio_path: Path, start_time: float, end_time: float, 
                                  output_path: Path, output_format: str = "wav") -> bool:
        """Extract audio segment using the smart boundaries"""
        try:
            # Build FFmpeg command with precise timing
            cmd = [
                "ffmpeg", "-y",  # -y to overwrite existing files
                "-i", str(audio_path),
                "-ss", str(start_time),  # Start time (smart boundary)
                "-to", str(end_time),    # End time (smart boundary) 
                "-avoid_negative_ts", "make_zero"  # Handle negative timestamps
            ]
            
            # Set codec based on output format
            if output_format.lower() == "mp3":
                cmd.extend(["-acodec", "mp3", "-b:a", "192k"])
            elif output_format.lower() == "flac":
                cmd.extend(["-acodec", "flac"])
            elif output_format.lower() == "wav":
                cmd.extend(["-acodec", "pcm_s16le"])
            else:
                cmd.extend(["-acodec", "pcm_s16le"])
                output_format = "wav"
            
            cmd.append(str(output_path))
            
            # Run FFmpeg
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"FFmpeg error: {result.stderr}")
                return False
                
            return output_path.exists() and output_path.stat().st_size > 0
            
        except Exception as e:
            print(f"Error extracting smart audio segment: {e}")
            return False
    
    def split_audio_smartly(self, audio_path: Path, sentences: List[str], 
                          config: Dict[str, Any], 
                          progress_callback: Optional[Callable] = None) -> bool:
        """Split audio file using smart boundary detection"""
        
        try:
            output_dir = config["output_dir"]
            output_format = config.get("output_format", "wav")
            similarity_threshold = config.get("similarity_threshold", 0.6)
            
            total_steps = 5 + len(sentences)
            current_step = 0
            
            # Step 1: Detect silence regions
            if progress_callback:
                progress_callback("Analyzing audio for silence regions...", current_step / total_steps)
            
            silence_regions = self.detect_silence_regions(audio_path)
            current_step += 1
            
            # Step 2: Transcribe with word-level timestamps
            if progress_callback:
                progress_callback("Transcribing audio with word-level timing...", current_step / total_steps)
            
            transcription_segments = self.transcribe_with_word_timestamps(audio_path)
            current_step += 1
            
            if not transcription_segments:
                raise Exception("Failed to transcribe audio")
            
            # Step 3: Smart alignment
            if progress_callback:
                progress_callback("Performing smart text-audio alignment...", current_step / total_steps)
            
            aligned_segments = self.align_sentences_with_smart_boundaries(
                transcription_segments, sentences, silence_regions, similarity_threshold
            )
            current_step += 1
            
            if not aligned_segments:
                raise Exception("No segments could be aligned with the provided text")
            
            # Step 4: Create output directory and save metadata
            if progress_callback:
                progress_callback("Preparing output directory...", current_step / total_steps)
            
            output_dir.mkdir(exist_ok=True)
            
            # Save detailed alignment information
            alignment_info = []
            for i, segment in enumerate(aligned_segments):
                word_timings = []
                for word in segment.words:
                    word_timings.append({
                        "word": word.get("word", ""),
                        "start": word.get("start", 0),
                        "end": word.get("end", 0),
                        "confidence": word.get("probability", 0)
                    })
                
                alignment_info.append({
                    "segment_number": i + 1,
                    "original_start_time": segment.start_time,
                    "original_end_time": segment.end_time,
                    "smart_start_time": segment.adjusted_start,
                    "smart_end_time": segment.adjusted_end,
                    "duration": segment.adjusted_end - segment.adjusted_start,
                    "text": segment.text,
                    "confidence": segment.confidence,
                    "word_timings": word_timings,
                    "boundary_optimization": "silence_detection"
                })
            
            with open(output_dir / "smart_alignment_info.json", 'w', encoding='utf-8') as f:
                json.dump(alignment_info, f, indent=2, ensure_ascii=False)
            
            current_step += 1
            
            # Step 5: Extract smart audio segments
            successful_extractions = 0
            
            for i, segment in enumerate(aligned_segments):
                if progress_callback:
                    progress_callback(
                        f"Extracting smart segment {i+1}/{len(aligned_segments)}: '{segment.text[:30]}...'",
                        (current_step + i) / total_steps
                    )
                
                # Generate output filename
                safe_text = re.sub(r'[^\w\s-]', '', segment.text[:50]).strip()
                safe_text = re.sub(r'\s+', '_', safe_text)
                
                output_filename = f"smart_segment_{i+1:03d}_{safe_text}.{output_format}"
                output_path = output_dir / output_filename
                
                # Extract using smart boundaries
                success = self.extract_smart_audio_segment(
                    audio_path, segment.adjusted_start, segment.adjusted_end, 
                    output_path, output_format
                )
                
                if success:
                    successful_extractions += 1
                    duration = segment.adjusted_end - segment.adjusted_start
                    print(f"  ✅ Smart extract: {output_path.name} ({duration:.2f}s)")
                else:
                    print(f"  ❌ Failed to extract: {output_filename}")
            
            # Summary
            print(f"\n✅ Smart audio splitting complete!")
            print(f"   Total segments: {len(aligned_segments)}")
            print(f"   Successful extractions: {successful_extractions}")
            print(f"   Silence regions used: {len(silence_regions)}")
            print(f"   Output directory: {output_dir}")
            
            if progress_callback:
                progress_callback(f"Smart splitting complete! {successful_extractions}/{len(aligned_segments)} segments", 1.0)
            
            return successful_extractions > 0
            
        except Exception as e:
            error_msg = f"Error during smart audio splitting: {e}"
            print(f"❌ {error_msg}")
            if progress_callback:
                progress_callback(f"Error: {e}", 1.0)
            return False
    
    def update_settings(self, buffer_ms: int = None, similarity_threshold: float = None, 
                       output_format: str = None, silence_threshold: float = None):
        """Update smart audio splitting settings"""
        if buffer_ms is not None:
            self.buffer_ms = buffer_ms
            config.processing.audio_buffer_ms = buffer_ms
        
        if similarity_threshold is not None:
            self.similarity_threshold = similarity_threshold
            config.processing.audio_similarity_threshold = similarity_threshold
        
        if output_format is not None:
            self.output_format = output_format
            config.processing.audio_output_format = output_format
        
        if silence_threshold is not None:
            self.silence_threshold = silence_threshold
        
        # Save config
        config.save()


# Convenience function for smart splitting
def split_audio_smartly(audio_path: Path, text_file_path: Path, output_dir: Path, 
                       similarity_threshold: float = 0.6, output_format: str = "wav",
                       silence_threshold: float = 0.01, 
                       progress_callback: Optional[Callable] = None) -> bool:
    """
    Smart audio splitting with silence detection and word boundaries
    
    Args:
        audio_path: Path to the audio file
        text_file_path: Path to the text file (one sentence per line)
        output_dir: Directory to save the split audio files
        similarity_threshold: Threshold for text-audio alignment (0.0 to 1.0)
        output_format: Output audio format ("wav", "mp3", "flac")
        silence_threshold: Amplitude threshold for silence detection
        progress_callback: Optional callback for progress updates
    
    Returns:
        True if splitting was successful, False otherwise
    """
    
    if not AUDIO_PROCESSING_AVAILABLE:
        print("Audio processing libraries are not available")
        return False
    
    try:
        # Read sentences from text file
        with open(text_file_path, 'r', encoding='utf-8') as f:
            sentences = [line.strip() for line in f.readlines() if line.strip()]
        
        if not sentences:
            print("No sentences found in text file")
            return False
        
        # Create smart splitter
        splitter = SmartAudioSplitter()
        if silence_threshold != 0.01:
            splitter.silence_threshold = silence_threshold
        
        config_dict = {
            'output_dir': output_dir,
            'output_format': output_format,
            'similarity_threshold': similarity_threshold
        }
        
        return splitter.split_audio_smartly(
            audio_path, sentences, config_dict, progress_callback
        )
        
    except Exception as e:
        print(f"Error in smart audio splitting: {e}")
        return False