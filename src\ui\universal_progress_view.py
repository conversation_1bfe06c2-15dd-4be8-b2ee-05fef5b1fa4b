import flet as ft
from typing import Optional, Dict, Any, List, Callable
from pathlib import Path
import threading
from enum import Enum


class OperationType(Enum):
    """Types of operations that can be processed"""
    VIDEO_PROCESSING = "video"
    AUDIO_SPLITTING = "audio"
    F5_TTS_GENERATION = "f5tts"


class UniversalProgressView:
    """Universal progress view that can handle any type of processing operation"""
    
    def __init__(self, on_processing_complete: Callable):
        self.on_processing_complete = on_processing_complete
        self.page = None  # Will be set by main window
        
        # Current operation state
        self.current_operation = None
        self.current_processor = None
        self.operation_config = {}
        
        # UI components (will be created in build())
        self.overall_progress = None
        self.progress_text = None
        self.stage_indicator = None
        self.stage_labels = None
        self.stats_container = None
        self.preview_container = None
        self.cancel_button = None
        
        # Dynamic components that change per operation
        self.stat_items = []
        self.stage_containers = []
        
    def build(self):
        """Build the universal progress UI"""
        # Overall progress bar
        self.overall_progress = ft.ProgressBar(
            width=400,
            color=ft.Colors.PRIMARY,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            value=0.0
        )
        
        # Progress text
        self.progress_text = ft.Text(
            "Ready to start processing...",
            size=14,
            color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        # Dynamic stage indicator (will be populated based on operation)
        self.stage_indicator = ft.Row([], alignment=ft.MainAxisAlignment.CENTER, spacing=0)
        
        # Dynamic stage labels
        self.stage_labels = ft.Row([], alignment=ft.MainAxisAlignment.CENTER, spacing=0)
        
        # Dynamic stats container
        self.stats_container = ft.Container(
            content=ft.Column([
                ft.Text("Operation Stats", weight=ft.FontWeight.W_500, size=16),
                ft.Row([], alignment=ft.MainAxisAlignment.CENTER, spacing=30)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
            padding=20,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=12,
            width=350
        )
        
        # Dynamic preview container
        self.preview_container = ft.Container(
            content=ft.Column([
                ft.Text("Preview", weight=ft.FontWeight.W_500),
                ft.Container(
                    content=ft.Text("No preview available", color=ft.Colors.ON_SURFACE_VARIANT),
                    width=200,
                    height=150,
                    bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
                    border_radius=8,
                    alignment=ft.alignment.center
                )
            ], spacing=10, horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        )
        
        # Cancel button
        self.cancel_button = ft.ElevatedButton(
            "Cancel Processing",
            icon=ft.Icons.CANCEL,
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.RED_400,
            on_click=self.cancel_processing,
            disabled=True
        )
        
        # Main layout
        return ft.Container(
            content=ft.Column([
                # Header
                ft.Container(
                    content=ft.Text(
                        "Processing Status",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PRIMARY
                    ),
                    padding=20,
                    alignment=ft.alignment.center
                ),
                
                # Progress section
                ft.Container(
                    content=ft.Column([
                        self.overall_progress,
                        ft.Container(height=10),
                        self.progress_text,
                        ft.Container(height=20),
                        self.stage_indicator,
                        ft.Container(height=10),
                        self.stage_labels
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    padding=20
                ),
                
                # Stats and preview row
                ft.Row([
                    self.stats_container,
                    self.preview_container
                ], alignment=ft.MainAxisAlignment.CENTER, spacing=40),
                
                # Controls
                ft.Container(
                    content=self.cancel_button,
                    padding=20,
                    alignment=ft.alignment.center
                )
                
            ], scroll=ft.ScrollMode.AUTO, expand=True),
            expand=True
        )
    
    def start_operation(self, operation_type: OperationType, processor, config: Dict[str, Any]):
        """Start a new processing operation"""
        print(f"🚀 Starting {operation_type.value} operation...")
        
        self.current_operation = operation_type
        self.current_processor = processor
        self.operation_config = config
        
        # Configure UI for this operation type
        self._configure_for_operation(operation_type)
        
        # Reset progress
        self.overall_progress.value = 0.0
        self.progress_text.value = f"Starting {operation_type.value} processing..."
        self.cancel_button.disabled = False
        
        # Update page
        if self.page:
            self.page.update()
        
        # Start processing in background thread
        threading.Thread(
            target=self._run_operation,
            daemon=True
        ).start()
    
    def _configure_for_operation(self, operation_type: OperationType):
        """Configure UI elements based on operation type"""
        if operation_type == OperationType.VIDEO_PROCESSING:
            self._setup_video_processing_ui()
        elif operation_type == OperationType.AUDIO_SPLITTING:
            self._setup_audio_splitting_ui()
        elif operation_type == OperationType.F5_TTS_GENERATION:
            self._setup_f5_tts_ui()
    
    def _setup_video_processing_ui(self):
        """Setup UI for video processing"""
        # Stages: Analyze → Extract → Scenes → Transcribe → Complete
        stages = ["Analyze", "Extract", "Scenes", "Transcribe", "Complete"]
        self._create_stage_indicators(stages)
        
        # Stats: Current Frame, Extracted, Total Frames, FPS, Quality, Duration
        stats = [
            ("Current Frame", "0"),
            ("Extracted", "0"),
            ("Total Frames", "0"),
            ("FPS", "0"),
            ("Quality", "0.00"),
            ("Duration", "0:00")
        ]
        self._create_stats(stats)
        
        # Preview: Frame preview
        self._setup_frame_preview()
    
    def _setup_audio_splitting_ui(self):
        """Setup UI for audio splitting"""
        # Stages: Load → Transcribe → Align → Extract → Complete
        stages = ["Load", "Transcribe", "Align", "Extract", "Complete"]
        self._create_stage_indicators(stages)
        
        # Stats: Duration, Segments, Progress, Model, Buffer
        stats = [
            ("Duration", "0:00"),
            ("Text Lines", "0"),
            ("Segments Created", "0"),
            ("Model", "Whisper"),
            ("Buffer", "500ms"),
            ("Similarity", "0.9")
        ]
        self._create_stats(stats)
        
        # Preview: Waveform or text preview
        self._setup_audio_preview()
    
    def _setup_f5_tts_ui(self):
        """Setup UI for F5-TTS generation"""
        # Stages: Load → Encode → Generate → Process → Complete
        stages = ["Load", "Encode", "Generate", "Process", "Complete"]
        self._create_stage_indicators(stages)
        
        # Stats: Text Length, Voice Model, Progress, Quality
        stats = [
            ("Text Length", "0 chars"),
            ("Voice Model", "F5-TTS"),
            ("Generated", "0s"),
            ("Quality", "High"),
            ("Sample Rate", "24kHz"),
            ("Format", "WAV")
        ]
        self._create_stats(stats)
        
        # Preview: Text being processed
        self._setup_tts_preview()
    
    def _create_stage_indicators(self, stages: List[str]):
        """Create stage indicator circles and labels"""
        self.stage_containers = []
        indicator_controls = []
        label_controls = []
        
        for i, stage in enumerate(stages):
            # Create stage circle
            stage_circle = ft.Container(
                content=ft.Text(str(i+1), color=ft.Colors.WHITE, size=12, text_align=ft.TextAlign.CENTER),
                width=24, height=24,
                bgcolor=ft.Colors.GREY_600 if i > 0 else ft.Colors.BLUE_400,
                border_radius=ft.border_radius.all(12),
                alignment=ft.alignment.center
            )
            self.stage_containers.append(stage_circle)
            indicator_controls.append(stage_circle)
            
            # Add connector line (except for last item)
            if i < len(stages) - 1:
                indicator_controls.append(
                    ft.Container(width=30, height=2, 
                               bgcolor=ft.Colors.GREY_600 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_300)
                )
            
            # Create stage label
            label_controls.append(
                ft.Container(
                    content=ft.Text(stage, size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                    width=65 if i < len(stages) - 1 else 60,
                    alignment=ft.alignment.center
                )
            )
        
        # Update UI
        self.stage_indicator.controls = indicator_controls
        self.stage_labels.controls = label_controls
    
    def _create_stats(self, stats: List[tuple]):
        """Create statistics display"""
        stat_controls = []
        self.stat_items = []
        
        # Split into two rows of 3 items each
        for i in range(0, len(stats), 3):
            row_stats = stats[i:i+3]
            row_controls = []
            
            for label, value in row_stats:
                stat_item = self._create_stat_item(label, value)
                self.stat_items.append(stat_item)
                row_controls.append(stat_item)
            
            stat_controls.append(
                ft.Row(row_controls, alignment=ft.MainAxisAlignment.CENTER, spacing=20)
            )
        
        # Update stats container
        self.stats_container.content.controls[1] = ft.Column(stat_controls, spacing=10)
    
    def _create_stat_item(self, label: str, value: str):
        """Create a statistic display item"""
        value_text = ft.Text(value, size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.PRIMARY)
        label_text = ft.Text(label, size=10, color=ft.Colors.ON_SURFACE_VARIANT)
        
        return ft.Column([
            value_text,
            label_text
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2)
    
    def _setup_frame_preview(self):
        """Setup frame preview for video processing - shows actual frame thumbnails"""
        # Create initial placeholder that will be replaced with actual frames
        self.frame_preview = ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.VIDEO_FILE, size=48, color=ft.Colors.BLUE_400),
                ft.Text("Frame Preview", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                ft.Text("Processing will show live frames here", size=10, color=ft.Colors.ON_SURFACE_VARIANT)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=5),
            width=220,
            height=180,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=8,
            alignment=ft.alignment.center,
            padding=10
        )
        
        self.preview_container.content.controls[1] = self.frame_preview
    
    def _setup_audio_preview(self):
        """Setup audio preview (waveform visualization or text) - flexible like frame preview"""
        self.audio_preview = ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.AUDIOTRACK, size=48, color=ft.Colors.BLUE_400),
                ft.Text("Audio Processing", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                ft.Text("Waveform and segments will show here", size=10, color=ft.Colors.ON_SURFACE_VARIANT)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=5),
            width=220,
            height=180,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=8,
            alignment=ft.alignment.center,
            padding=10
        )
        
        self.preview_container.content.controls[1] = self.audio_preview
    
    def _setup_tts_preview(self):
        """Setup TTS preview (text being processed) - flexible like frame preview"""
        self.tts_preview = ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.RECORD_VOICE_OVER, size=48, color=ft.Colors.GREEN_400),
                ft.Text("Text-to-Speech", size=12, color=ft.Colors.ON_SURFACE_VARIANT),
                ft.Text("Generated audio preview will show here", size=10, color=ft.Colors.ON_SURFACE_VARIANT)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=5),
            width=220,
            height=180,
            bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
            border_radius=8,
            alignment=ft.alignment.center,
            padding=10
        )
        
        self.preview_container.content.controls[1] = self.tts_preview
    
    def _run_operation(self):
        """Run the current operation in background thread"""
        try:
            if self.current_operation == OperationType.VIDEO_PROCESSING:
                self._run_video_processing()
            elif self.current_operation == OperationType.AUDIO_SPLITTING:
                self._run_audio_splitting()
            elif self.current_operation == OperationType.F5_TTS_GENERATION:
                self._run_f5_tts_generation()
        except Exception as e:
            print(f"❌ Operation failed: {e}")
            self._on_operation_error(str(e))
    
    def _run_video_processing(self):
        """Run video processing"""
        try:
            # Get configuration
            video_path = self.operation_config.get('video_path')
            split_scenes = self.operation_config.get('split_scenes', True)
            enable_transcription = self.operation_config.get('enable_transcription', False)
            
            if not video_path:
                raise ValueError("Video path not provided")
            
            # Import here to avoid circular imports
            from ..core.video_processor import VideoProcessor
            
            # Create video processor with our progress callback
            def progress_callback(progress_info: dict):
                # Convert video processor format to universal format
                stage = progress_info.get('stage', 'processing')
                progress = progress_info.get('progress', 0.0)
                
                # Map stages to indices
                stage_mapping = {
                    'initializing': 0,
                    'extracting': 1,
                    'filtering': 1, 
                    'detecting_scenes': 2,
                    'scene_detection': 2,
                    'splitting_scenes': 3,
                    'transcribing': 3,
                    'transcription_complete': 3,
                    'scenes_split': 3,
                    'metadata': 4,
                    'complete': 4
                }
                
                stage_index = stage_mapping.get(stage, 1)
                
                # Create stats dictionary with string values
                stats = {}
                preview_data = None
                
                for key, value in progress_info.items():
                    if key not in ['stage', 'progress', 'message', 'current_frame_data']:
                        if isinstance(value, (int, float)):
                            # Format numbers properly (integers for frame counts, etc.)
                            if key in ['total_frames', 'current_frame', 'extracted', 'scenes_detected', 'total_scenes']:
                                stats[key.replace('_', ' ').title()] = str(int(value))
                            else:
                                stats[key.replace('_', ' ').title()] = str(value)
                        elif isinstance(value, str):
                            stats[key.replace('_', ' ').title()] = value
                        elif isinstance(value, dict):
                            # Handle nested dict (like video_info)
                            for sub_key, sub_value in value.items():
                                if isinstance(sub_value, (int, float)):
                                    # Format nested numbers properly
                                    if sub_key in ['total_frames', 'width', 'height']:
                                        stats[sub_key.replace('_', ' ').title()] = str(int(sub_value))
                                    else:
                                        stats[sub_key.replace('_', ' ').title()] = str(sub_value)
                                elif isinstance(sub_value, str):
                                    stats[sub_key.replace('_', ' ').title()] = sub_value
                
                # Handle frame preview data
                if 'current_frame_data' in progress_info:
                    try:
                        import cv2
                        import base64
                        
                        frame = progress_info['current_frame_data']
                        if frame is not None:
                            # Resize frame for preview (max 200x150)
                            height, width = frame.shape[:2]
                            max_width, max_height = 200, 150
                            
                            if width > max_width or height > max_height:
                                scale = min(max_width/width, max_height/height)
                                new_width = int(width * scale)
                                new_height = int(height * scale)
                                frame = cv2.resize(frame, (new_width, new_height))
                            
                            # Convert to base64 for display
                            _, buffer = cv2.imencode('.png', frame)
                            preview_data = base64.b64encode(buffer).decode('utf-8')
                    except Exception as e:
                        print(f"⚠️ Failed to process frame preview: {e}")
                        preview_data = None
                
                # Create message from stage
                message_mapping = {
                    'initializing': 'Initializing video processing...',
                    'extracting': 'Extracting frames...',
                    'filtering': 'Filtering frames...',
                    'detecting_scenes': 'Detecting scene boundaries...',
                    'scene_detection': 'Detecting scene boundaries...',
                    'splitting_scenes': 'Splitting scenes...',
                    'transcribing': 'Transcribing audio...',
                    'transcription_complete': 'Transcription complete',
                    'scenes_split': 'Scenes split successfully',
                    'metadata': 'Finalizing metadata...',
                    'complete': 'Processing complete!'
                }
                
                message = message_mapping.get(stage, f"Processing: {stage}")
                
                # Update progress with universal format
                universal_progress = {
                    'progress': progress,
                    'message': message,
                    'stage': stage_index,
                    'stats': stats
                }
                
                # Add preview data if available
                if preview_data:
                    universal_progress['preview_data'] = preview_data
                
                self.update_progress(universal_progress)
            
            processor = VideoProcessor(progress_callback=progress_callback)
            
            # Process video
            result = processor.process_video(
                video_path,
                split_scenes=split_scenes,
                enable_transcription=enable_transcription
            )
            
            # Operation completed successfully
            completion_result = {
                'type': 'video_processing',
                'video_path': video_path,
                'result': result
            }
            
            self._on_operation_complete(completion_result)
            
        except Exception as e:
            print(f"❌ Video processing failed: {e}")
            self._on_operation_error(str(e))
    
    def _run_audio_splitting(self):
        """Run audio splitting"""
        try:
            # Get configuration
            audio_file = self.operation_config.get('audio_file')
            processing_mode = self.operation_config.get('processing_mode', 'text')
            similarity_threshold = self.operation_config.get('similarity_threshold', 0.85)
            output_format = self.operation_config.get('output_format', 'wav')
            buffer_ms = self.operation_config.get('buffer_ms', 500)
            
            print(f"🎯 Universal progress processing mode: {processing_mode}")
            
            if processing_mode == "alignment":
                # JSON alignment mode
                alignment_file = self.operation_config.get('alignment_file')
                if not alignment_file:
                    raise ValueError("Alignment file not provided for JSON mode")
                
                print(f"📊 Using alignment file: {alignment_file}")
                
                # Load alignment data to get segment count
                import json
                with open(alignment_file, 'r', encoding='utf-8') as f:
                    alignment_data = json.load(f)
                
                segments_count = len(alignment_data)
                
                # Update initial stats
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_dir = audio_file.parent / f"{audio_file.stem}_aligned_split_{timestamp}"
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # Configure alignment splitter
                config = {
                    'output_dir': output_dir,
                    'output_format': output_format.lower(),
                    'buffer_ms': buffer_ms
                }
                
                # Create progress callback for alignment mode
                def progress_callback(message: str, progress: float):
                    stage_index = 0
                    stats = {
                        "Duration": f"{self._get_audio_duration(audio_file):.1f}s",
                        "Text Lines": str(segments_count),
                        "Segments Created": "0",
                        "Model": "JSON",
                        "Buffer": f"{buffer_ms}ms",
                        "Similarity": "N/A"
                    }
                    
                    if "Loading alignment" in message:
                        stage_index = 0
                    elif "Preparing output" in message:
                        stage_index = 1
                    elif "Extracting" in message:
                        stage_index = 2
                        # Extract segment count from message
                        import re
                        match = re.search(r'(\d+)/(\d+)', message)
                        if match:
                            stats["Segments Created"] = match.group(1)
                    elif "Complete" in message:
                        stage_index = 4
                        stats["Segments Created"] = str(segments_count)
                    
                    progress_info = {
                        'progress': progress,
                        'message': message,
                        'stage': stage_index,
                        'stats': stats
                    }
                    self.update_progress(progress_info)
                
                # Use alignment splitter for JSON mode
                from ..core.audio_splitter_from_alignment import AudioSplitterFromAlignment
                alignment_splitter = AudioSplitterFromAlignment()
                
                result = alignment_splitter.split_audio_from_alignment(
                    audio_path=audio_file,
                    alignment_json_path=alignment_file,
                    config=config,
                    progress_callback=progress_callback
                )
                
                # Operation completed successfully
                completion_result = {
                    'type': 'audio_splitting',
                    'mode': 'alignment',
                    'output_dir': output_dir,
                    'segments_count': segments_count,
                    'success': result
                }
                
            else:
                # Text-based modes (smart, text)
                text_file = self.operation_config.get('text_file')
                if not text_file:
                    raise ValueError("Text file not provided for text-based mode")
                
                # Read text file
                with open(text_file, 'r', encoding='utf-8') as f:
                    sentences = [line.strip() for line in f.readlines() if line.strip()]
                
                if not sentences:
                    raise ValueError("No sentences found in text file")
                
                # Update initial stats
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                mode_suffix = {"smart": "smart", "text": "basic"}.get(processing_mode, "basic")
                output_dir = audio_file.parent / f"{audio_file.stem}_{mode_suffix}_split_{timestamp}"
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # Configure splitter
                config = {
                    'similarity_threshold': similarity_threshold,
                    'output_format': output_format.lower(),
                    'output_dir': output_dir,
                    'buffer_ms': buffer_ms
                }
                
                # Create progress callback that updates our universal progress
                def progress_callback(message: str, progress: float):
                    # Map the message to stage and stats
                    stage_index = 0
                    stats = {
                        "Duration": f"{self._get_audio_duration(audio_file):.1f}s",
                        "Text Lines": str(len(sentences)),
                        "Segments Created": "0",
                        "Model": "Whisper",
                        "Buffer": f"{buffer_ms}ms",
                        "Similarity": f"{similarity_threshold:.1f}"
                    }
                    
                    if "Loading Whisper" in message:
                        stage_index = 0
                    elif "Transcribing" in message:
                        stage_index = 1
                    elif "Aligning" in message:
                        stage_index = 2
                    elif "Extracting" in message:
                        stage_index = 3
                        # Extract segment count if present in message
                        import re
                        match = re.search(r'(\d+)/(\d+)', message)
                        if match:
                            stats["Segments Created"] = match.group(1)
                    elif "Complete" in message:
                        stage_index = 4
                        stats["Segments Created"] = str(len(sentences))
                    
                    # Update progress
                    progress_info = {
                        'progress': progress,
                        'message': message,
                        'stage': stage_index,
                        'stats': stats
                    }
                    self.update_progress(progress_info)
                
                # Split audio using the existing audio splitter
                result = self.current_processor.split_audio_by_text_alignment(
                    audio_path=audio_file,
                    sentences=sentences,
                    config=config,
                    progress_callback=progress_callback
                )
                
                # Operation completed successfully
                completion_result = {
                    'type': 'audio_splitting',
                    'mode': processing_mode,
                    'output_dir': output_dir,
                    'segments_count': len(sentences),
                    'success': result
                }
            
            self._on_operation_complete(completion_result)
            
        except Exception as e:
            print(f"❌ Audio splitting failed: {e}")
            self._on_operation_error(str(e))
    
    def _get_audio_duration(self, audio_file):
        """Get audio file duration in seconds"""
        try:
            import librosa
            y, sr = librosa.load(str(audio_file), duration=1)  # Just load 1 second to get info
            duration = librosa.get_duration(filename=str(audio_file))
            return duration
        except Exception:
            return 0.0
    
    def _run_f5_tts_generation(self):
        """Run F5-TTS generation"""
        try:
            # Get configuration
            sentences = self.operation_config.get('sentences', [])
            text_file = self.operation_config.get('text_file')
            reference_audio = self.operation_config.get('reference_audio')
            reference_text = self.operation_config.get('reference_text', "")
            model_name = self.operation_config.get('model_name', 'E2TTS_Base')
            params = self.operation_config.get('params', {})
            
            if not sentences and not text_file:
                raise ValueError("No text provided for TTS generation")
            
            # Read sentences from file if provided
            if text_file and not sentences:
                with open(text_file, 'r', encoding='utf-8') as f:
                    sentences = [line.strip() for line in f.readlines() if line.strip()]
            
            if not sentences:
                raise ValueError("No sentences found for processing")
            
            # Create output directory
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = Path.cwd() / f"f5_tts_output_{timestamp}"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Create progress callback that updates our universal progress
            def progress_callback(message: str, progress: float):
                # Map the message to stage and stats
                stage_index = 0
                stats = {
                    "Text Length": f"{sum(len(s) for s in sentences)} chars",
                    "Voice Model": model_name,
                    "Generated": f"0/{len(sentences)}",
                    "Quality": "High",
                    "Sample Rate": "24kHz",
                    "Format": "WAV"
                }
                
                if "Loading" in message or "model" in message.lower():
                    stage_index = 0
                elif "Encoding" in message or "encode" in message.lower():
                    stage_index = 1
                elif "Generating" in message or "generate" in message.lower():
                    stage_index = 2
                    # Extract progress from message if present
                    import re
                    match = re.search(r'(\d+)/(\d+)', message)
                    if match:
                        stats["Generated"] = f"{match.group(1)}/{match.group(2)}"
                elif "Processing" in message or "process" in message.lower():
                    stage_index = 3
                elif "Complete" in message or "complete" in message.lower():
                    stage_index = 4
                    stats["Generated"] = f"{len(sentences)}/{len(sentences)}"
                
                # Update progress
                progress_info = {
                    'progress': progress,
                    'message': message,
                    'stage': stage_index,
                    'stats': stats
                }
                self.update_progress(progress_info)
            
            # Start with initial progress
            progress_callback("Loading F5-TTS model...", 0.0)
            
            # Load model if not already loaded
            if not self.current_processor.model_loaded:
                self.current_processor.load_model(
                    model_name=model_name,
                    progress_callback=progress_callback
                )
            
            # Set reference audio if provided
            if reference_audio:
                progress_callback("Setting reference audio...", 0.15)
                self.current_processor.set_reference_audio(reference_audio, reference_text)
            
            # Process sentences
            progress_callback("Generating speech...", 0.2)
            results = self.current_processor.process_sentences(
                sentences=sentences,
                output_dir=output_dir,
                params=params,
                broll_config=self.operation_config.get('broll_config'),
                progress_callback=lambda msg, prog: progress_callback(msg, 0.2 + prog * 0.75)
            )
            
            # Operation completed successfully
            completion_result = {
                'type': 'f5_tts_generation',
                'success': True,
                'output_dir': output_dir,
                'sentences_count': len(sentences),
                'results': results,
                'broll_data': results.get('broll_data', {}),
                'broll_config': self.operation_config.get('broll_config')
            }
            
            self._on_operation_complete(completion_result)
            
        except Exception as e:
            print(f"❌ F5-TTS generation failed: {e}")
            self._on_operation_error(str(e))
    
    def update_progress(self, progress_info: Dict[str, Any]):
        """Universal progress update method"""
        if self.page:
            self.page.run_task(self._update_progress_ui, progress_info)
    
    async def _update_progress_ui(self, progress_info: Dict[str, Any]):
        """Update progress UI on main thread"""
        try:
            # Update overall progress
            if 'progress' in progress_info:
                self.overall_progress.value = progress_info['progress']
            
            # Update progress text
            if 'message' in progress_info:
                self.progress_text.value = progress_info['message']
            
            # Update stage
            if 'stage' in progress_info:
                self._update_stage(progress_info['stage'])
            
            # Update stats
            if 'stats' in progress_info:
                self._update_stats(progress_info['stats'])
            
            # Update preview
            if 'preview_data' in progress_info:
                self._update_preview(progress_info['preview_data'])
            
            # Update page
            self.page.update()
            
        except Exception as e:
            print(f"Progress update error: {e}")
    
    def _update_stage(self, stage_index: int):
        """Update stage indicators"""
        for i, container in enumerate(self.stage_containers):
            if i < stage_index:
                container.bgcolor = ft.Colors.GREEN_400  # Completed
            elif i == stage_index:
                container.bgcolor = ft.Colors.BLUE_400   # Active
            else:
                container.bgcolor = ft.Colors.GREY_600   # Pending
    
    def _update_stats(self, stats: Dict[str, str]):
        """Update statistics display"""
        for i, (key, value) in enumerate(stats.items()):
            if i < len(self.stat_items):
                # Update the value text (first control in the Column)
                self.stat_items[i].controls[0].value = str(value)
    
    def _update_preview(self, preview_data: Any):
        """Update preview area based on operation type"""
        if self.current_operation == OperationType.VIDEO_PROCESSING:
            if preview_data and hasattr(self, 'frame_preview'):
                # Create frame preview with metadata overlay - like original design
                frame_image = ft.Image(
                    src_base64=preview_data,
                    width=200,
                    height=130,
                    fit=ft.ImageFit.CONTAIN,
                    border_radius=ft.border_radius.all(6)
                )
                
                # Add frame info like original (timestamp, quality)
                frame_info = ft.Column([
                    frame_image,
                    ft.Text("Current Frame", size=10, color=ft.Colors.ON_SURFACE_VARIANT, text_align=ft.TextAlign.CENTER),
                    ft.Text("Live Preview", size=8, color=ft.Colors.PRIMARY, text_align=ft.TextAlign.CENTER)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=2)
                
                # Create container with frame preview
                frame_container = ft.Container(
                    content=frame_info,
                    width=220,
                    height=180,
                    bgcolor=ft.Colors.GREY_800 if (self.page and self.page.theme_mode == ft.ThemeMode.DARK) else ft.Colors.GREY_100,
                    border_radius=8,
                    alignment=ft.alignment.center,
                    padding=10,
                    border=ft.border.all(1, ft.Colors.PRIMARY)
                )
                
                self.preview_container.content.controls[1] = frame_container
                self.frame_preview = frame_container  # Update reference
                
                # Update UI
                if self.page:
                    self.page.update()
                    
        elif self.current_operation == OperationType.AUDIO_SPLITTING:
            # Handle audio preview updates
            if hasattr(self, 'audio_preview'):
                # Could show waveform or text preview here
                pass
                
        elif self.current_operation == OperationType.F5_TTS:
            # Handle TTS preview updates  
            if hasattr(self, 'tts_preview'):
                # Could show text being processed
                pass
    
    def cancel_processing(self, e=None):
        """Cancel the current processing operation"""
        print("🛑 Cancelling operation...")
        if self.current_processor and hasattr(self.current_processor, 'cancel'):
            self.current_processor.cancel()
        
        self.cancel_button.disabled = True
        self.progress_text.value = "Cancelling..."
        if self.page:
            self.page.update()
    
    def _on_operation_complete(self, result):
        """Handle operation completion"""
        self.cancel_button.disabled = True
        self.overall_progress.value = 1.0
        self.progress_text.value = f"{self.current_operation.value.title()} processing complete!"
        
        if self.page:
            self.page.update()
        
        # Notify completion callback
        if self.on_processing_complete:
            self.on_processing_complete(result)
    
    def _on_operation_error(self, error_message: str):
        """Handle operation error"""
        self.cancel_button.disabled = True
        self.overall_progress.color = ft.Colors.RED_400
        self.progress_text.value = f"Error: {error_message}"
        
        if self.page:
            self.page.update()


# Keep the old class name as an alias for backward compatibility
class ProgressView(UniversalProgressView):
    """Backward compatibility alias"""
    pass