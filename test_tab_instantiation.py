from src.ui.f5_tts_view import F5TTSView

class MockWindow:
    def __init__(self):
        self.page = None

try:
    view = F5TTSView()
    print("✅ F5TTSView instantiation successful")
    print(f"📝 Text input available: {hasattr(view, 'text_input')}")
    print(f"🎤 Audio picker available: {hasattr(view, 'audio_picker')}")
    print(f"🔧 Generate button available: {hasattr(view, 'generate_button')}")
except Exception as e:
    print(f"❌ Error instantiating F5TTSView: {e}")
    import traceback
    traceback.print_exc()