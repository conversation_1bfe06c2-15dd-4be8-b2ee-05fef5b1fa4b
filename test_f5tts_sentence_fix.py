#!/usr/bin/env python3
"""
Test script to verify F5-TTS sentence processing fix
Tests that text is split by lines only, not by periods within lines
"""

def get_sentences_from_input(text: str) -> list:
    """Extract sentences from text input (copy of the fixed method)"""
    text = text.strip() if text else ""
    if not text:
        return []
    
    # Split by lines only - each line becomes one audio file
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    return lines

def test_sentence_splitting():
    """Test that sentences are split by lines only, not by periods"""
    
    # Test case from the screenshot
    test_text = """Forget the usual fluff.
Let's get straight into The 7 Habits of Highly Effective People by <PERSON>.
This book wants your life to run like a Swiss watch, but let's be real, most of us are still figuring out which batteries go where.
Stick around—by the end you'll either be effective or really good at pretending.
Ever notice how some people always get stuff done while you're still looking for your keys?
That's not magic.
It's habit one: Be proactive."""
    
    # Get sentences using the fixed method
    sentences = get_sentences_from_input(test_text)
    
    print("=== F5-TTS Sentence Splitting Test ===")
    print(f"Input text has {len(test_text.split())} words")
    print(f"Found {len(sentences)} audio files to generate:")
    print()
    
    for i, sentence in enumerate(sentences, 1):
        print(f"Audio file {i}: '{sentence}'")
    
    print()
    
    # Verify the problematic line is kept intact
    problematic_line = "Let's get straight into The 7 Habits of Highly Effective People by Stephen R. Covey."
    if problematic_line in sentences:
        print("✅ SUCCESS: Problematic line kept intact as one audio file")
    else:
        print("❌ FAILED: Problematic line was split incorrectly")
        # Show what happened to the problematic line
        for sentence in sentences:
            if "Covey" in sentence or "Stephen" in sentence:
                print(f"   Found part: '{sentence}'")
    
    # Verify expected number of lines
    expected_lines = len([line.strip() for line in test_text.split('\n') if line.strip()])
    if len(sentences) == expected_lines:
        print(f"✅ SUCCESS: Correct number of audio files ({len(sentences)})")
    else:
        print(f"❌ FAILED: Expected {expected_lines} audio files, got {len(sentences)}")
    
    return len(sentences) == expected_lines and problematic_line in sentences

if __name__ == "__main__":
    success = test_sentence_splitting()
    if success:
        print("\n🎉 All tests passed! F5-TTS will now create one audio file per line.")
    else:
        print("\n💥 Tests failed! Check the sentence splitting logic.")
    
    exit(0 if success else 1)