"""
Modular main window using the tab registry system
"""
import flet as ft
from pathlib import Path
from typing import Dict, Optional

from .tabs.tab_registry import tab_registry, TabInfo
from ..core.operations.registry import operation_registry


class ModularMainWindow:
    """Main application window with dynamic tab navigation"""

    def __init__(self):
        self.page: Optional[ft.Page] = None
        self.current_tab = "upload"
        self.tab_views: Dict[str, ft.Control] = {}
        
        # UI Components
        self.navigation_bar: Optional[ft.NavigationBar] = None
        self.content_area: Optional[ft.Container] = None
        
        # Load plugins
        self._load_plugins()

    def _load_plugins(self):
        """Load all plugins and register tabs/operations"""
        print("🔌 Loading plugins...")
        
        # Load tab registry
        tab_registry.load_builtin_tabs()
        
        # Load operation registry (after views are available)
        operation_registry.load_builtin_operations()
        
        print(f"📊 Loaded {len(tab_registry.get_tabs())} tabs")
        print(f"🔧 Loaded {len(operation_registry.list_operations())} operations")

    def set_page(self, page: ft.Page):
        """Set page reference for this window and all child views"""
        self.page = page
        
        # Set page reference for any existing tab views
        for tab_view in self.tab_views.values():
            if hasattr(tab_view, 'set_page'):
                tab_view.set_page(page)
            elif hasattr(tab_view, 'page'):
                tab_view.page = page

    def build(self) -> ft.Control:
        """Build main window with dynamic tabs"""
        # Get all registered tabs
        tabs = tab_registry.get_tabs()
        
        if not tabs:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.ERROR_OUTLINE, size=64, color=ft.Colors.RED_400),
                    ft.Text("No tabs available", size=20, weight=ft.FontWeight.BOLD),
                    ft.Text("Please check plugin loading", size=14, color=ft.Colors.ON_SURFACE_VARIANT)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
                alignment=ft.alignment.center,
                expand=True
            )
        
        # Build navigation
        nav_items = []
        for tab in tabs:
            nav_items.append(
                ft.NavigationDestination(
                    icon=tab.icon,
                    label=tab.name,
                    tooltip=tab.tooltip
                )
            )
        
        self.navigation_bar = ft.NavigationBar(
            destinations=nav_items,
            selected_index=0,
            on_change=self._on_tab_change
        )
        
        # Build content area with default tab
        default_tab_id = tabs[0].tab_id if tabs else "upload"
        self.content_area = ft.Container(
            content=self._get_tab_view(default_tab_id),
            expand=True,
            padding=0
        )
        
        return ft.Column([
            self.content_area,
            self.navigation_bar
        ], expand=True, spacing=0)
    
    def _on_tab_change(self, e):
        """Handle tab navigation"""
        tabs = tab_registry.get_tabs()
        selected_index = e.control.selected_index
        
        if 0 <= selected_index < len(tabs):
            selected_tab = tabs[selected_index]
            self.switch_to_tab(selected_tab.tab_id)
    
    def switch_to_tab(self, tab_id: str):
        """Switch to a specific tab"""
        if not tab_registry.has_tab(tab_id):
            print(f"⚠️ Tab not found: {tab_id}")
            return
        
        self.current_tab = tab_id
        self.content_area.content = self._get_tab_view(tab_id)
        
        if self.page:
            self.page.update()
        
        print(f"📱 Switched to tab: {tab_id}")
    
    def _get_tab_view(self, tab_id: str) -> ft.Control:
        """Get or create tab view"""
        # Return cached view if available
        if tab_id in self.tab_views:
            return self.tab_views[tab_id]
        
        # Get tab info
        tab_info = tab_registry.get_tab(tab_id)
        if not tab_info:
            return self._create_not_found_view(tab_id)
        
        # Create new view
        try:
            new_view = tab_info.view_factory()
            
            # Set page reference if view supports it
            if hasattr(new_view, 'set_page') and self.page:
                new_view.set_page(self.page)
            elif hasattr(new_view, 'page') and self.page:
                new_view.page = self.page
            
            # Cache the view
            self.tab_views[tab_id] = new_view
            
            return new_view
            
        except Exception as e:
            print(f"❌ Error creating view for tab {tab_id}: {e}")
            return self._create_error_view(tab_id, str(e))
    
    def _create_not_found_view(self, tab_id: str) -> ft.Control:
        """Create view for tab not found"""
        return ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.TAB, size=64, color=ft.Colors.GREY_400),
                ft.Text("Tab Not Found", size=20, weight=ft.FontWeight.BOLD),
                ft.Text(f"Tab '{tab_id}' is not registered", size=14, color=ft.Colors.ON_SURFACE_VARIANT)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
            alignment=ft.alignment.center,
            expand=True
        )
    
    def _create_error_view(self, tab_id: str, error: str) -> ft.Control:
        """Create view for tab loading error"""
        return ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.ERROR_OUTLINE, size=64, color=ft.Colors.RED_400),
                ft.Text("Tab Error", size=20, weight=ft.FontWeight.BOLD),
                ft.Text(f"Error loading tab '{tab_id}':", size=14, color=ft.Colors.ON_SURFACE_VARIANT),
                ft.Text(error, size=12, color=ft.Colors.RED_400),
                ft.Container(height=20),
                ft.ElevatedButton(
                    "Reload Tab",
                    icon=ft.Icons.REFRESH,
                    on_click=lambda e: self._reload_tab(tab_id)
                )
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
            alignment=ft.alignment.center,
            expand=True
        )
    
    def _reload_tab(self, tab_id: str):
        """Reload a tab by clearing its cache"""
        if tab_id in self.tab_views:
            del self.tab_views[tab_id]
        self.switch_to_tab(tab_id)
    
    def get_current_tab_id(self) -> str:
        """Get current tab ID"""
        return self.current_tab
    
    def get_available_tabs(self) -> list:
        """Get list of available tab IDs"""
        return tab_registry.list_tab_ids()
    
    def register_custom_tab(self, tab_info: TabInfo):
        """Register a custom tab (for plugins)"""
        tab_registry.register_tab(tab_info)
        
        # If we're already built, rebuild navigation
        if self.navigation_bar and self.page:
            # Rebuild the whole window to update navigation
            content = self.build()
            self.page.controls.clear()
            self.page.add(content)
            self.page.update()
    
    # Legacy compatibility methods for existing views
    def start_video_processing(self, video_path: Path, **kwargs):
        """Start video processing (legacy compatibility)"""
        print(f"🎬 Starting video processing: {video_path}")
        
        # Switch to progress tab
        self.switch_to_tab("progress")
        
        # TODO: Integrate with new operation system
        # For now, use existing functionality
        if "progress" in self.tab_views:
            progress_view = self.tab_views["progress"]
            if hasattr(progress_view, 'start_video_processing'):
                progress_view.start_video_processing(video_path, **kwargs)
    
    def on_processing_complete(self, result):
        """Handle processing completion (legacy compatibility)"""
        print(f"✅ Processing complete: {result}")
        
        # Switch to results tab
        self.switch_to_tab("results")
        
        # TODO: Update results view with new result
        if "results" in self.tab_views:
            results_view = self.tab_views["results"]
            if hasattr(results_view, 'set_result'):
                results_view.set_result(result)