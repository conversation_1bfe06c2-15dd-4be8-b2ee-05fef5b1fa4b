import flet as ft
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ui.main_window import MainWindow


def main(page: ft.Page):
    """Main application entry point"""
    page.title = "Vid2Frames"
    page.theme_mode = ft.ThemeMode.DARK  # Dark theme by default
    page.window_width = 1200
    page.window_height = 800
    page.window_min_width = 800
    page.window_min_height = 600
    
    # Window styling - use system title bar for proper window controls
    # page.window_title_bar_hidden = True  # older Flet API
    # try:
    #     page.window.title_bar_hidden = True  # newer Flet API
    #     page.window.frameless = True
    #     page.window.shadow = True
    # except Exception:
    #     # Older API fallbacks
    #     try:
    #         page.window_frameless = True
    #     except Exception:
    #         pass
    page.padding = 0
    
    # App-specific theme settings - Light theme
    page.theme = ft.Theme(
        color_scheme=ft.ColorScheme(
            primary=ft.Colors.BLUE_600,
            on_primary=ft.Colors.WHITE,
            primary_container=ft.Colors.BLUE_100,
            on_primary_container=ft.Colors.BLUE_900,
            secondary=ft.Colors.CYAN_600,
            on_secondary=ft.Colors.WHITE,
            secondary_container=ft.Colors.CYAN_100,
            on_secondary_container=ft.Colors.CYAN_800,
            surface=ft.Colors.WHITE,
            on_surface=ft.Colors.BLACK,
            surface_variant=ft.Colors.GREY_100,
            on_surface_variant=ft.Colors.GREY_700,
            background=ft.Colors.WHITE,
            on_background=ft.Colors.BLACK,
            outline=ft.Colors.GREY_400,
            outline_variant=ft.Colors.GREY_300,
            error=ft.Colors.RED_600,
            on_error=ft.Colors.WHITE,
        ),
        use_material3=True,
    )
    page.dark_theme = ft.Theme(
        color_scheme=ft.ColorScheme(
            primary=ft.Colors.BLUE_400,
            on_primary=ft.Colors.BLACK,
            primary_container=ft.Colors.BLUE_800,
            on_primary_container=ft.Colors.BLUE_100,
            secondary=ft.Colors.CYAN_400,
            on_secondary=ft.Colors.BLACK,
            secondary_container=ft.Colors.CYAN_800,
            on_secondary_container=ft.Colors.CYAN_100,
            surface=ft.Colors.GREY_800,
            on_surface=ft.Colors.WHITE,
            surface_variant=ft.Colors.GREY_800,
            on_surface_variant=ft.Colors.GREY_300,
            background=ft.Colors.GREY_900,
            on_background=ft.Colors.WHITE,
            outline=ft.Colors.GREY_500,
            outline_variant=ft.Colors.GREY_600,
            error=ft.Colors.RED_400,
            on_error=ft.Colors.BLACK,
        ),
        use_material3=True,
    )

    # Initialize main window
    main_window = MainWindow()
    main_window.set_page(page)
    
    # Store main window reference in page for access by views
    page.main_window = main_window

    # React to platform brightness changes when in SYSTEM mode
    try:
        def _on_brightness_change(e):
            # main_window.update_title_bar_theme()  # Disabled - using system title bar
            pass
        page.on_platform_brightness_change = _on_brightness_change
    except Exception:
        pass

    # Build and add to page
    content = main_window.build()
    page.add(content)
    page.update()


if __name__ == "__main__":
    # Explicitly use desktop app view to ensure window APIs (title bar hidden, frameless) work
    ft.app(target=main, view=ft.AppView.FLET_APP)