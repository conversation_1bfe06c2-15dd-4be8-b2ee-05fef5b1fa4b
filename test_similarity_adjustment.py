"""
Test script to help adjust similarity threshold for better matching
"""
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def analyze_matching_results():
    """Analyze the similarity threshold impact"""
    print("🔍 Similarity Threshold Analysis")
    print("=" * 50)
    print()
    
    print("📊 Current Results (threshold 0.7):")
    print("   - Total sentences: 115")
    print("   - Whisper segments: 69") 
    print("   - Matched segments: ~35-40 (estimated from logs)")
    print("   - Missing: ~75-80 sentences!")
    print()
    
    print("❌ Common Issues:")
    print("   1. First sentence never captured (Whisper didn't detect start)")
    print("   2. Short sentences like 'That's not magic....' rejected")
    print("   3. Similar phrases rejected due to strict threshold")
    print()
    
    print("💡 Recommendations:")
    print()
    print("🎯 Option 1: Lower Similarity Threshold")
    print("   - Try 0.5 or 0.6 instead of 0.7")
    print("   - Will capture more matches but may be less accurate")
    print()
    
    print("🎯 Option 2: Manual Audio Inspection")
    print("   - Check if first few seconds contain the missing text")
    print("   - Audio might start differently than text")
    print()
    
    print("🎯 Option 3: Preprocess Audio")
    print("   - Add small silence padding at start")
    print("   - Helps Whisper detect beginning better")
    print()
    
    print("🎯 Option 4: Use Smart Mode Instead")
    print("   - Smart mode uses silence detection")
    print("   - Less dependent on text-to-audio matching")
    print("   - Better for capturing all spoken content")
    print()
    
    print("🔧 Quick Fix - Update Similarity Threshold:")
    print("   In the UI, try changing similarity from 0.85 to 0.5 or 0.6")
    print("   This should capture more of the missing sentences")

if __name__ == "__main__":
    analyze_matching_results()