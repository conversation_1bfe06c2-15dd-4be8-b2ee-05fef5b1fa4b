# Modular Operation System Design

## Overview
A plugin-based architecture that allows easy addition of new operation types and tabs without modifying core application code.

---

## Core Architecture

### 1. Base Operation Interface
```python
# src/core/operations/base_operation.py
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
from enum import Enum

class OperationStage(Enum):
    """Standard operation stages"""
    INITIALIZE = "initialize"
    PROCESS = "process"
    FINALIZE = "finalize"
    COMPLETE = "complete"
    ERROR = "error"

class BaseOperation(ABC):
    """Base class for all operations"""
    
    @property
    @abstractmethod
    def operation_id(self) -> str:
        """Unique identifier for this operation type"""
        pass
    
    @property
    @abstractmethod
    def display_name(self) -> str:
        """Human-readable name for UI"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Description for tooltips/help"""
        pass
    
    @property
    @abstractmethod
    def icon(self) -> str:
        """Icon name for UI (Flet icon name)"""
        pass
    
    @abstractmethod
    def get_stages(self) -> List[str]:
        """Return list of stage names for this operation"""
        pass
    
    @abstractmethod
    def get_stats_schema(self) -> Dict[str, str]:
        """Return stat labels and their descriptions"""
        pass
    
    @abstractmethod
    def create_progress_view(self) -> 'BaseProgressView':
        """Create operation-specific progress view"""
        pass
    
    @abstractmethod
    def create_results_view(self) -> 'BaseResultsView':
        """Create operation-specific results view"""
        pass
    
    @abstractmethod
    def execute(self, config: Dict[str, Any], progress_callback: Callable) -> Dict[str, Any]:
        """Execute the operation with given configuration"""
        pass
```

### 2. Progress View Base Class
```python
# src/ui/views/base_progress_view.py
from abc import ABC, abstractmethod
import flet as ft
from typing import Dict, Any, Optional

class BaseProgressView(ABC):
    """Base class for operation-specific progress views"""
    
    def __init__(self, operation_config: Dict[str, Any]):
        self.operation_config = operation_config
        self.page: Optional[ft.Page] = None
        
    @abstractmethod
    def build_stats_panel(self) -> ft.Control:
        """Build the stats display panel"""
        pass
    
    @abstractmethod
    def build_preview_panel(self) -> ft.Control:
        """Build the preview area"""
        pass
    
    @abstractmethod
    def update_progress(self, progress_info: Dict[str, Any]):
        """Update progress display"""
        pass
    
    def build(self) -> ft.Control:
        """Build the complete progress view (common layout)"""
        return ft.Column([
            self._build_common_header(),
            ft.Row([
                ft.Container(
                    content=self.build_stats_panel(),
                    width=400
                ),
                ft.Container(
                    content=self.build_preview_panel(),
                    width=300
                )
            ])
        ])
    
    def _build_common_header(self) -> ft.Control:
        """Build common progress header (progress bar, stage indicators)"""
        # Common progress bar and stage indicators
        pass
```

### 3. Results View Base Class
```python
# src/ui/views/base_results_view.py
from abc import ABC, abstractmethod
import flet as ft
from typing import Dict, Any, List

class BaseResultsView(ABC):
    """Base class for operation-specific results views"""
    
    def __init__(self, operation_result: Dict[str, Any]):
        self.operation_result = operation_result
        
    @abstractmethod
    def build_summary_cards(self) -> List[ft.Control]:
        """Build summary information cards"""
        pass
    
    @abstractmethod
    def build_action_buttons(self) -> List[ft.Control]:
        """Build action buttons for this result type"""
        pass
    
    @abstractmethod
    def build_results_display(self) -> ft.Control:
        """Build the main results display area"""
        pass
    
    def build(self) -> ft.Control:
        """Build the complete results view"""
        return ft.Column([
            ft.Row(self.build_summary_cards(), wrap=True),
            ft.Row(self.build_action_buttons(), wrap=True),
            self.build_results_display()
        ])
```

---

## Operation Plugins

### 1. Video Processing Plugin
```python
# src/core/operations/video_operation.py
from .base_operation import BaseOperation, OperationStage
from ..video_processor import VideoProcessor

class VideoOperation(BaseOperation):
    @property
    def operation_id(self) -> str:
        return "video_processing"
    
    @property
    def display_name(self) -> str:
        return "Video Processing"
    
    @property
    def description(self) -> str:
        return "Extract frames and detect scenes from video files"
    
    @property
    def icon(self) -> str:
        return "video_file"
    
    def get_stages(self) -> List[str]:
        return ["Analyze", "Extract", "Scenes", "Transcribe", "Complete"]
    
    def get_stats_schema(self) -> Dict[str, str]:
        return {
            "Current Frame": "Frame being processed",
            "Extracted": "Frames that passed quality filter",
            "Total Frames": "Total frames in video",
            "FPS": "Frames per second",
            "Duration": "Video duration",
            "Scenes Detected": "Number of scenes found"
        }
    
    def create_progress_view(self):
        from ...ui.views.video_progress_view import VideoProgressView
        return VideoProgressView(self.operation_config)
    
    def create_results_view(self):
        from ...ui.views.video_results_view import VideoResultsView
        return VideoResultsView(self.operation_result)
    
    def execute(self, config: Dict[str, Any], progress_callback: Callable) -> Dict[str, Any]:
        processor = VideoProcessor(progress_callback=progress_callback)
        return processor.process_video(
            config['video_path'],
            split_scenes=config.get('split_scenes', True),
            enable_transcription=config.get('enable_transcription', False)
        )
```

### 2. Audio Splitting Plugin
```python
# src/core/operations/audio_operation.py
from .base_operation import BaseOperation

class AudioSplittingOperation(BaseOperation):
    @property
    def operation_id(self) -> str:
        return "audio_splitting"
    
    @property
    def display_name(self) -> str:
        return "Audio Splitting"
    
    @property
    def description(self) -> str:
        return "Split audio files using text alignment"
    
    @property
    def icon(self) -> str:
        return "audiotrack"
    
    def get_stages(self) -> List[str]:
        return ["Load", "Transcribe", "Align", "Split", "Complete"]
    
    def get_stats_schema(self) -> Dict[str, str]:
        return {
            "Duration": "Audio file duration",
            "Text Lines": "Number of text lines",
            "Segments Created": "Audio segments created",
            "Model": "Transcription model used",
            "Buffer": "Audio buffer size",
            "Similarity": "Alignment similarity threshold"
        }
    
    def create_progress_view(self):
        from ...ui.views.audio_progress_view import AudioProgressView
        return AudioProgressView(self.operation_config)
    
    def create_results_view(self):
        from ...ui.views.audio_results_view import AudioResultsView
        return AudioResultsView(self.operation_result)
```

---

## Plugin Registry System

### 1. Operation Registry
```python
# src/core/operations/registry.py
from typing import Dict, Type, List
from .base_operation import BaseOperation

class OperationRegistry:
    """Registry for all available operations"""
    
    def __init__(self):
        self._operations: Dict[str, Type[BaseOperation]] = {}
        self._load_builtin_operations()
    
    def register_operation(self, operation_class: Type[BaseOperation]):
        """Register a new operation type"""
        instance = operation_class()
        self._operations[instance.operation_id] = operation_class
    
    def get_operation(self, operation_id: str) -> Type[BaseOperation]:
        """Get operation class by ID"""
        if operation_id not in self._operations:
            raise ValueError(f"Unknown operation: {operation_id}")
        return self._operations[operation_id]
    
    def list_operations(self) -> List[Dict[str, str]]:
        """List all available operations"""
        result = []
        for op_class in self._operations.values():
            instance = op_class()
            result.append({
                'id': instance.operation_id,
                'name': instance.display_name,
                'description': instance.description,
                'icon': instance.icon
            })
        return result
    
    def _load_builtin_operations(self):
        """Load built-in operations"""
        from .video_operation import VideoOperation
        from .audio_operation import AudioSplittingOperation
        from .f5tts_operation import F5TTSOperation
        
        self.register_operation(VideoOperation)
        self.register_operation(AudioSplittingOperation)
        self.register_operation(F5TTSOperation)

# Global registry instance
operation_registry = OperationRegistry()
```

### 2. Tab Registry
```python
# src/ui/tabs/tab_registry.py
from typing import Dict, Type, List, Callable
import flet as ft

class TabInfo:
    def __init__(self, tab_id: str, name: str, icon: str, 
                 view_factory: Callable[[], ft.Control], 
                 order: int = 100):
        self.tab_id = tab_id
        self.name = name
        self.icon = icon
        self.view_factory = view_factory
        self.order = order

class TabRegistry:
    """Registry for all application tabs"""
    
    def __init__(self):
        self._tabs: Dict[str, TabInfo] = {}
        self._load_builtin_tabs()
    
    def register_tab(self, tab_info: TabInfo):
        """Register a new tab"""
        self._tabs[tab_info.tab_id] = tab_info
    
    def get_tabs(self) -> List[TabInfo]:
        """Get all tabs sorted by order"""
        return sorted(self._tabs.values(), key=lambda t: t.order)
    
    def get_tab(self, tab_id: str) -> TabInfo:
        """Get specific tab info"""
        return self._tabs.get(tab_id)
    
    def _load_builtin_tabs(self):
        """Load built-in tabs"""
        from ..views.upload_view import UploadView
        from ..views.universal_progress_view import UniversalProgressView
        from ..views.results_view import ResultsView
        from ..views.settings_view import SettingsView
        
        # Core tabs
        self.register_tab(TabInfo("upload", "Upload", "upload_file", 
                                lambda: UploadView(), order=10))
        self.register_tab(TabInfo("progress", "Progress", "timeline", 
                                lambda: UniversalProgressView(), order=20))
        self.register_tab(TabInfo("results", "Results", "folder", 
                                lambda: ResultsView(), order=30))
        self.register_tab(TabInfo("settings", "Settings", "settings", 
                                lambda: SettingsView(), order=90))

# Global registry
tab_registry = TabRegistry()
```

---

## Modular Main Window

### 1. Dynamic Main Window
```python
# src/ui/main_window.py
import flet as ft
from .tabs.tab_registry import tab_registry
from ..core.operations.registry import operation_registry

class MainWindow:
    def __init__(self):
        self.page = None
        self.current_tab = "upload"
        self.tab_views = {}
        
    def build(self) -> ft.Control:
        """Build main window with dynamic tabs"""
        # Get all registered tabs
        tabs = tab_registry.get_tabs()
        
        # Build navigation
        nav_items = []
        for tab in tabs:
            nav_items.append(
                ft.NavigationDestination(
                    icon=tab.icon,
                    label=tab.name
                )
            )
        
        self.navigation_bar = ft.NavigationBar(
            destinations=nav_items,
            on_change=self._on_tab_change
        )
        
        # Build content area
        self.content_area = ft.Container(
            expand=True,
            content=self._get_tab_view("upload")  # Default tab
        )
        
        return ft.Column([
            self.content_area,
            self.navigation_bar
        ], expand=True)
    
    def _on_tab_change(self, e):
        """Handle tab navigation"""
        tabs = tab_registry.get_tabs()
        selected_tab = tabs[e.control.selected_index]
        
        self.current_tab = selected_tab.tab_id
        self.content_area.content = self._get_tab_view(selected_tab.tab_id)
        self.page.update()
    
    def _get_tab_view(self, tab_id: str) -> ft.Control:
        """Get or create tab view"""
        if tab_id not in self.tab_views:
            tab_info = tab_registry.get_tab(tab_id)
            if tab_info:
                self.tab_views[tab_id] = tab_info.view_factory()
        
        return self.tab_views.get(tab_id, ft.Text("Tab not found"))
```

---

## Adding New Operations (Example)

### 1. Create New Operation Plugin
```python
# src/core/operations/image_enhancement_operation.py
from .base_operation import BaseOperation

class ImageEnhancementOperation(BaseOperation):
    @property
    def operation_id(self) -> str:
        return "image_enhancement"
    
    @property
    def display_name(self) -> str:
        return "Image Enhancement"
    
    # ... implement other required methods
```

### 2. Create Progress View
```python
# src/ui/views/image_progress_view.py
from .base_progress_view import BaseProgressView
import flet as ft

class ImageProgressView(BaseProgressView):
    def build_stats_panel(self) -> ft.Control:
        return ft.Column([
            ft.Text("Images Processed: 5/20"),
            ft.Text("Current Enhancement: Sharpening"),
            ft.Text("Quality Improvement: +15%")
        ])
    
    def build_preview_panel(self) -> ft.Control:
        return ft.Container(
            content=ft.Column([
                ft.Text("Before/After Preview"),
                ft.Row([
                    ft.Image(width=100, height=100),  # Before
                    ft.Image(width=100, height=100)   # After
                ])
            ])
        )
```

### 3. Register the Operation
```python
# In your plugin loading code or main.py
from src.core.operations.registry import operation_registry
from src.core.operations.image_enhancement_operation import ImageEnhancementOperation

# Register new operation
operation_registry.register_operation(ImageEnhancementOperation)
```

### 4. Add New Tab (Optional)
```python
# Add a dedicated tab for image operations
from src.ui.tabs.tab_registry import tab_registry, TabInfo

tab_registry.register_tab(
    TabInfo("images", "Images", "image", 
           lambda: ImageUploadView(), order=25)
)
```

---

## Benefits of This Architecture

### 1. **Easy Extension**
- Add new operations without touching core code
- Plugin system allows third-party extensions
- Each operation is self-contained

### 2. **Maintainability**
- Clear separation of concerns
- Each operation manages its own UI and logic
- Common functionality in base classes

### 3. **Consistency**
- Base classes enforce consistent interfaces
- Common UI patterns across all operations
- Standardized progress reporting

### 4. **Flexibility**
- Operations can have unique UI elements
- Custom stats and preview areas
- Configurable stages and workflows

### 5. **Future-Proof**
- Easy to add new tabs and features
- Plugin architecture supports community extensions
- Registry system handles dynamic loading

This modular approach makes it super easy to add new functionality - you just create a new operation plugin and it automatically integrates into the existing UI framework! 🚀