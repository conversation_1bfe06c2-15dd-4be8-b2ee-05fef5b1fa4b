# B-Roll UI Integration for F5-TTS Tab

## Integration Strategy

### Location in UI
- Add B-roll section between Settings panel and Action area in F5-TTS tab
- Collapsible/expandable section to save space
- Default to collapsed unless user has generated audio files

### UI Components Required

#### 1. B-Roll Toggle Section
```
🎬 B-Roll Video Generation [Toggle] [Enable/Disable]
    ℹ️ Automatically create video content for your generated audio
```

#### 2. B-Roll Settings Panel (when expanded)
```
┌─ B-Roll Settings ─────────────────────────────────┐
│ ✅ Auto-generate B-roll   [Pexels API: Connected] │
│                                                   │
│ Keywords Preview: business, meeting, working...   │
│ Video Quality: [HD ▼]  Duration: [Auto ▼]       │
│ Results per keyword: [3 ▼]                       │
└───────────────────────────────────────────────────┘
```

#### 3. B-Roll Preview Grid (after generation)
```
┌─ B-Roll Videos (7 sentences) ─────────────────────┐
│ 📄 "So you typed into Google..."                  │
│ [🎬 Video 1] [🎬 Video 2] [🎬 Video 3]          │
│ Selected: Video 1 (business office) 8s HD         │
│                                                   │
│ 📄 "A business analyst is the bridge..."          │
│ [🎬 Video 1] [🎬 Video 2] [🎬 Video 3]          │
│ Selected: Video 2 (professional meeting) 6s HD    │
│                                                   │
│ [Generate Combined Video] [Export Settings]       │
└───────────────────────────────────────────────────┘
```

## Implementation Plan

### Phase 1: Basic Integration (Current)
- [x] Create B-roll core modules
- [ ] Add B-roll toggle to F5-TTS UI
- [ ] Add basic settings panel
- [ ] Connect to existing TTS generation workflow

### Phase 2: Preview System
- [ ] Video thumbnail previews
- [ ] Grid selection interface
- [ ] Alternative video options per sentence
- [ ] Smart caching system

### Phase 3: Video Generation
- [ ] FFmpeg integration for video creation
- [ ] Audio-video synchronization
- [ ] Export options and settings
- [ ] Progress tracking for video generation

## Integration Points

### F5-TTS Generation Workflow
1. User generates TTS audio files (existing)
2. If B-roll enabled → Extract keywords from text
3. Search for videos using Pexels API
4. Show preview grid for user selection
5. Generate combined video on demand

### Settings Persistence
- Store B-roll preferences in app settings
- Cache video selections for regeneration
- Remember API credentials securely

### Error Handling
- Graceful fallback if API unavailable
- Clear user feedback for connection issues
- Option to disable B-roll if problems occur

## Technical Requirements

### New Dependencies
- Requests (for Pexels API) - already added
- Pillow (for image processing) - already added
- FFmpeg-python (for video processing)

### File Structure
```
src/
├── core/
│   └── broll/
│       ├── __init__.py ✅
│       ├── keyword_extractor.py ✅
│       ├── pexels_api.py ✅
│       └── broll_generator.py ✅
├── ui/
│   ├── f5_tts_view.py (modify)
│   └── components/
│       └── broll_panel.py (new)
```

## Next Steps
1. Add B-roll toggle and basic settings to F5-TTS UI
2. Connect B-roll generation to TTS workflow
3. Implement preview grid system
4. Add video generation capabilities