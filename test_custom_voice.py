#!/usr/bin/env python3
"""
Test script to create a sample audio file for testing custom voice upload workflow
"""
import numpy as np
import soundfile as sf
from pathlib import Path

def create_test_voice():
    """Create a test audio file that simulates a voice sample"""
    # Generate a simple tone sequence (simulating speech patterns)
    sample_rate = 22050
    duration = 3.0  # 3 seconds
    
    # Create a simple multi-tone pattern that simulates speech prosody
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Create a speech-like pattern with varying frequencies
    frequencies = [200, 300, 250, 400, 180, 350]  # Hz values
    audio = np.zeros_like(t)
    
    segment_length = len(t) // len(frequencies)
    
    for i, freq in enumerate(frequencies):
        start_idx = i * segment_length
        end_idx = start_idx + segment_length if i < len(frequencies) - 1 else len(t)
        
        # Create a segment with the frequency, adding some envelope
        segment_t = t[start_idx:end_idx]
        envelope = np.exp(-3 * (segment_t - segment_t[0]) / (segment_t[-1] - segment_t[0]))
        tone = np.sin(2 * np.pi * freq * (segment_t - segment_t[0])) * envelope * 0.3
        
        audio[start_idx:end_idx] = tone
    
    # Add some noise to make it more realistic
    noise = np.random.normal(0, 0.02, len(audio))
    audio = audio + noise
    
    # Normalize
    audio = audio / np.max(np.abs(audio)) * 0.7
    
    # Save the test voice file
    test_file = Path("test_business_voice.wav")
    sf.write(test_file, audio, sample_rate)
    
    print(f"✅ Created test voice file: {test_file}")
    print(f"   Duration: {duration}s")
    print(f"   Sample rate: {sample_rate}Hz")
    print(f"   File size: {test_file.stat().st_size} bytes")
    
    return test_file

if __name__ == "__main__":
    create_test_voice()