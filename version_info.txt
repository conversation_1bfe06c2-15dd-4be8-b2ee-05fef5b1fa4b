# Version Information for Windows Executable
# Used by PyInstaller to embed version info in the .exe file

VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable(
        u'040904B0',
        [
          StringStruct(u'CompanyName', u'Vid2Frames Solutions'),
          StringStruct(u'FileDescription', u'Vid2Frames Pro - Professional Video Frame Extraction Tool'),
          StringStruct(u'FileVersion', u'*******'),
          StringStruct(u'InternalName', u'Vid2Frames'),
          StringStruct(u'LegalCopyright', u'Copyright © 2025 Vid2Frames Solutions. All rights reserved.'),
          StringStruct(u'LegalTrademarks', u'Vid2Frames® is a trademark of Vid2Frames Solutions'),
          StringStruct(u'OriginalFilename', u'Vid2Frames.exe'),
          StringStruct(u'ProductName', u'Vid2Frames Pro'),
          StringStruct(u'ProductVersion', u'1.0.0'),
          StringStruct(u'Comments', u'Revolutionary desktop application for intelligent video frame extraction with AI-powered scene detection'),
        ]
      )
    ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)