#!/usr/bin/env python3
"""
Test script for scene splitting functionality
"""
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.video_processor import VideoProcessor, SceneData
from src.utils.config import config


def test_scene_data():
    """Test SceneData class"""
    print("Testing SceneData class...")
    
    scene = SceneData(start_time=0.0, end_time=5.5, start_frame=0, end_frame=132)
    
    assert scene.start_time == 0.0
    assert scene.end_time == 5.5
    assert scene.duration == 5.5
    assert scene.frame_count == 133
    
    print("✓ SceneData class works correctly")


def test_video_processor_scene_methods():
    """Test VideoProcessor scene-related methods"""
    print("Testing VideoProcessor scene methods...")
    
    processor = VideoProcessor()
    
    # Check if scene methods exist
    assert hasattr(processor, '_detect_scenes'), "_detect_scenes method missing"
    assert hasattr(processor, '_split_video_into_scenes'), "_split_video_into_scenes method missing"
    
    print("✓ VideoProcessor has scene-related methods")


def test_config_scene_settings():
    """Test configuration scene settings"""
    print("Testing configuration scene settings...")
    
    # Check if new config options exist
    assert hasattr(config.processing, 'split_scenes'), "split_scenes setting missing"
    assert hasattr(config.processing, 'min_scene_duration'), "min_scene_duration setting missing"
    
    # Check default values
    assert config.processing.split_scenes == False, "Default split_scenes should be False"
    assert config.processing.min_scene_duration == 1.0, "Default min_scene_duration should be 1.0"
    
    print("✓ Configuration has scene splitting settings")


def test_process_video_signature():
    """Test process_video method accepts split_scenes parameter"""
    print("Testing process_video method signature...")
    
    processor = VideoProcessor()
    
    # Check method signature
    import inspect
    sig = inspect.signature(processor.process_video)
    params = list(sig.parameters.keys())
    
    assert 'split_scenes' in params, "split_scenes parameter missing from process_video"
    
    print("✓ process_video method accepts split_scenes parameter")


def test_ui_settings():
    """Test UI settings integration"""
    print("Testing UI settings integration...")
    
    from src.ui.settings_view import SettingsView
    
    # Mock main window
    class MockMainWindow:
        def __init__(self):
            self.page = None
    
    settings_view = SettingsView(MockMainWindow())
    default_settings = settings_view.load_default_settings()
    
    # Check if scene settings are in default settings
    assert 'split_scenes' in default_settings, "split_scenes missing from UI settings"
    assert 'min_scene_duration' in default_settings, "min_scene_duration missing from UI settings"
    
    # Check default values
    assert default_settings['split_scenes'] == False, "Default UI split_scenes should be False"
    assert default_settings['min_scene_duration'] == 1.0, "Default UI min_scene_duration should be 1.0"
    
    print("✓ UI settings include scene splitting options")


def main():
    """Run all scene splitting tests"""
    print("Running scene splitting functionality tests...\n")
    
    tests = [
        test_scene_data,
        test_video_processor_scene_methods,
        test_config_scene_settings,
        test_process_video_signature,
        test_ui_settings,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
            print("✓ Test passed\n")
        except Exception as e:
            failed += 1
            print(f"✗ Test failed: {e}\n")
    
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All scene splitting tests passed!")
        print("\nScene Splitting Features Added:")
        print("✓ SceneData class for managing scene information")
        print("✓ Scene detection based on frame similarity")
        print("✓ Video splitting using FFmpeg")
        print("✓ UI integration with progress tracking")
        print("✓ Results view with scene information")
        print("✓ Configuration options for scene splitting")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())