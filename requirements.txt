# Core dependencies
flet>=0.21.0
opencv-python>=4.8.0
scikit-image>=0.21.0
Pillow>=10.0.0
ffmpeg-python>=0.2.0

# Audio transcription
openai-whisper>=20231117
torch>=2.0.0  # Required by whisper
torchaudio>=2.0.0  # Required by whisper
soundfile>=0.12.1  # Audio backend for torchaudio
librosa>=0.10.0  # Audio processing and loading

# Data and storage
# sqlite3 is built-in to Python

# Utilities
diskcache>=5.6.0
python-dotenv>=1.0.0

# Development dependencies
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
pre-commit>=3.3.0

# Build and packaging
pyinstaller>=5.13.0