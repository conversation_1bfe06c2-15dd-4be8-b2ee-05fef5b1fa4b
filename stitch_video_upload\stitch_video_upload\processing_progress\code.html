<!DOCTYPE html>
<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Vid2Frames - Processing</title>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            colors: {
              "primary": "#137fec",
              "background-light": "#f6f7f8",
              "background-dark": "#101922",
            },
            fontFamily: {
              "display": ["Inter"]
            },
            borderRadius: {"DEFAULT": "0.25rem", "lg": "0.5rem", "xl": "0.75rem", "full": "9999px"},
          },
        },
      }
    </script>
<style>
      .animate-pulse-opacity {
        animation: pulse-opacity 2s ease-in-out infinite;
      }
      @keyframes pulse-opacity {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display text-gray-800 dark:text-gray-200">
<div class="flex flex-col min-h-screen">
<header class="border-b border-gray-200 dark:border-gray-800">
<nav class="container mx-auto px-6 py-4 flex items-center justify-between">
<div class="flex items-center gap-4">
<div class="size-6 text-primary">
<svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><path d="M4 4H17.3334V17.3334H30.6666V30.6666H44V44H4V4Z" fill="currentColor"></path></svg>
</div>
<h1 class="text-xl font-bold text-gray-900 dark:text-white">Vid2Frames</h1>
</div>
<div class="hidden md:flex items-center gap-8">
<a class="text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors" href="#">Dashboard</a>
<a class="text-sm font-medium text-primary dark:text-primary" href="#">Projects</a>
<a class="text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors" href="#">Settings</a>
</div>
<div class="flex items-center gap-4">
<button class="md:hidden p-2 rounded-md text-gray-600 dark:text-gray-300">
<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<path d="M4 6h16M4 12h16m-7 6h7" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
<div class="w-10 h-10 rounded-full bg-cover bg-center" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAeb26y-QYqRRh6hSSKv0yThhcKmOwAzeynPuWKSkm7w8C5l8c4ROee5ZDJDdnE8TLJTtG4_lDVzjIuCeDFSibX0T5QGGkhmALdGDS-JGRTEsVRv2Ej-GeYvfgvn6uo5zgjOJhci2nQVq-5G79AXemddgJi05ufOiqLX6F3jMAUIRXqAtc6NOlbFJ6NwcaTry2jD9UQ8H-SPFzoWLKjmX8qBq5NYHRnVVMfB4nf8HI3mqC-NxhhAv-JvgRuszdxjpMnwrobFILJloA");'></div>
</div>
</nav>
</header>
<main class="flex-grow container mx-auto px-6 py-12 flex justify-center items-center">
<div class="w-full max-w-3xl space-y-8">
<div class="text-center">
<h2 class="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">Processing Your Video</h2>
<p class="mt-4 text-lg text-gray-600 dark:text-gray-400">Please wait while we extract the frames. This might take a moment.</p>
</div>
<div class="bg-white/50 dark:bg-background-dark/50 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-800">
<div class="space-y-6">
<div class="flex justify-between items-center text-sm font-medium text-gray-700 dark:text-gray-300">
<span class="animate-pulse-opacity">Processing...</span>
<span class="font-semibold text-primary">30%</span>
</div>
<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden">
<div class="bg-primary h-2.5 rounded-full transition-all duration-500 ease-out" style="width: 30%"></div>
</div>
<div class="text-xs text-center text-gray-500 dark:text-gray-400">
                Estimated time remaining: 5 minutes
              </div>
</div>
<div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-800 space-y-3 text-sm text-gray-600 dark:text-gray-400">
<div class="flex justify-between">
<span class="font-medium text-gray-800 dark:text-gray-200">Video Name:</span>
<span>MyVideo.mp4</span>
</div>
<div class="flex justify-between">
<span class="font-medium text-gray-800 dark:text-gray-200">Duration:</span>
<span>10 minutes</span>
</div>
<div class="flex justify-between">
<span class="font-medium text-gray-800 dark:text-gray-200">Resolution:</span>
<span>1920x1080</span>
</div>
</div>
<div class="mt-6 text-center text-sm p-4 rounded-lg bg-primary/10 dark:bg-primary/20 text-primary-800 dark:text-primary-200">
<p><span class="font-bold">Status:</span> Extracting frames from the video. This process may take a few minutes depending on the video size and length.</p>
</div>
</div>
</div>
</main>
</div>

</body></html>