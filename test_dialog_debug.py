"""
Test to debug why the progress dialog isn't appearing
"""

import flet as ft
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ui.audio_split_view import AudioSplitView

def test_progress_dialog_appearance():
    def main(page: ft.Page):
        page.title = "Progress Dialog Debug Test"
        page.theme_mode = ft.ThemeMode.DARK
        
        # Create the actual audio split view
        audio_view = AudioSplitView()
        audio_view.page = page
        
        # Mock that files are selected so the button is enabled
        from pathlib import Path
        audio_view.selected_audio_file = Path("fake_audio.wav")
        audio_view.selected_text_file = Path("fake_text.txt")
        
        def test_just_dialog(e):
            """Test just showing the dialog without processing"""
            print("🔄 Testing progress dialog appearance...")
            audio_view.show_progress_dialog()
            print("✅ show_progress_dialog() called")
            
        def test_progress_updates(e):
            """Test progress updates without actual processing"""
            print("🔄 Testing progress updates...")
            audio_view.show_progress_dialog()
            
            # Simulate some progress updates
            import time
            import threading
            
            def fake_progress():
                steps = [
                    ("Loading Whisper model...", 0.2),
                    ("Transcribing audio...", 0.4),  
                    ("Aligning text...", 0.6),
                    ("Extracting segments...", 0.8),
                    ("Complete!", 1.0)
                ]
                
                for msg, prog in steps:
                    time.sleep(1)
                    print(f"Updating progress: {msg} ({prog})")
                    audio_view.update_progress(msg, prog)
                
                time.sleep(2)
                audio_view.close_progress_dialog()
            
            threading.Thread(target=fake_progress, daemon=True).start()
        
        page.add(
            ft.Column([
                ft.Text("Progress Dialog Debug", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("This tests if the modal dialog appears at all"),
                ft.ElevatedButton(
                    "Test Dialog Appearance Only", 
                    on_click=test_just_dialog,
                    bgcolor=ft.Colors.BLUE,
                    color=ft.Colors.WHITE
                ),
                ft.ElevatedButton(
                    "Test Dialog + Progress Updates",
                    on_click=test_progress_updates, 
                    bgcolor=ft.Colors.GREEN,
                    color=ft.Colors.WHITE
                ),
                ft.Text("Check console for debug output", 
                       size=12, color=ft.Colors.GREY_500)
            ], 
            horizontal_alignment=ft.CrossAxisAlignment.CENTER, 
            spacing=20)
        )
    
    ft.app(target=main)

if __name__ == "__main__":
    test_progress_dialog_appearance()