"""
RTX 5090 Actual Functionality Test
==================================

This script tests if PyTorch 2.8.0+cu128 can actually use the RTX 5090
despite warning about sm_120 compatibility.
"""

import torch
import time

def test_rtx5090_functionality():
    """Test if RTX 5090 actually works with PyTorch 2.8.0"""
    print("🧪 RTX 5090 Actual Functionality Test")
    print("=" * 50)
    
    # Basic info
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA version: {torch.version.cuda}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        device_props = torch.cuda.get_device_properties(0)
        print(f"Compute capability: sm_{device_props.major}{device_props.minor}")
        print(f"Memory: {device_props.total_memory / 1024**3:.1f} GB")
        
        # Test 1: Simple tensor operations
        print(f"\n🔬 Test 1: Basic tensor operations")
        try:
            x = torch.randn(1000, 1000, device='cuda')
            y = torch.randn(1000, 1000, device='cuda')
            
            start_time = time.time()
            z = torch.matmul(x, y)
            end_time = time.time()
            
            print(f"✅ Matrix multiplication successful!")
            print(f"   Time: {(end_time - start_time)*1000:.2f} ms")
            print(f"   Result shape: {z.shape}")
            print(f"   Result mean: {z.mean().item():.4f}")
            
        except Exception as e:
            print(f"❌ Matrix multiplication failed: {e}")
            return False
        
        # Test 2: Neural network operations
        print(f"\n🔬 Test 2: Neural network operations")
        try:
            model = torch.nn.Sequential(
                torch.nn.Linear(1000, 500),
                torch.nn.ReLU(),
                torch.nn.Linear(500, 100),
                torch.nn.ReLU(),
                torch.nn.Linear(100, 10)
            ).cuda()
            
            input_data = torch.randn(64, 1000, device='cuda')
            
            start_time = time.time()
            with torch.no_grad():
                output = model(input_data)
            end_time = time.time()
            
            print(f"✅ Neural network forward pass successful!")
            print(f"   Time: {(end_time - start_time)*1000:.2f} ms")
            print(f"   Output shape: {output.shape}")
            print(f"   Output mean: {output.mean().item():.4f}")
            
        except Exception as e:
            print(f"❌ Neural network operations failed: {e}")
            return False
        
        # Test 3: Memory allocation
        print(f"\n🔬 Test 3: Large memory allocation")
        try:
            # Try to allocate a large tensor
            large_tensor = torch.randn(10000, 10000, device='cuda')
            print(f"✅ Large tensor allocation successful!")
            print(f"   Tensor shape: {large_tensor.shape}")
            print(f"   Memory usage: {large_tensor.numel() * 4 / 1024**3:.2f} GB")
            del large_tensor
            torch.cuda.empty_cache()
            
        except Exception as e:
            print(f"❌ Large memory allocation failed: {e}")
            return False
        
        # Test 4: CUDA-specific functions
        print(f"\n🔬 Test 4: CUDA-specific operations")
        try:
            x = torch.randn(1000, device='cuda')
            # Test CUDA-specific functions
            result1 = torch.cuda.FloatTensor(1000).normal_()
            result2 = torch.fft.fft(x)
            result3 = torch.sort(x)[0]
            
            print(f"✅ CUDA-specific operations successful!")
            print(f"   Random tensor shape: {result1.shape}")
            print(f"   FFT result shape: {result2.shape}")
            print(f"   Sort result shape: {result3.shape}")
            
        except Exception as e:
            print(f"❌ CUDA-specific operations failed: {e}")
            return False
            
        print(f"\n🎉 CONCLUSION:")
        print(f"✅ RTX 5090 is WORKING with PyTorch 2.8.0+cu128!")
        print(f"✅ Despite sm_120 warnings, all operations succeed")
        print(f"✅ Full GPU acceleration is available")
        print(f"✅ Your transcription can now use GPU mode!")
        
        return True
        
    else:
        print(f"❌ CUDA not available")
        return False

if __name__ == "__main__":
    success = test_rtx5090_functionality()
    if success:
        print(f"\n🚀 Ready to enable GPU acceleration in Vid2Frames!")
    else:
        print(f"\n🔄 Continue using CPU mode for now.")